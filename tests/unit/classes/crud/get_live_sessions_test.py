from unittest.mock import MagicMock, AsyncMock
from uuid import UUI<PERSON>, uuid4

import pendulum
import pytest
from tortoise.queryset import RawSQL, Q

from ciba_participant.classes.crud import LiveSessionRepository
from ciba_participant.classes.crud.get_live_sessions import (
    parse_session,
    get_query_annotations,
    get_query_filters,
)
from ciba_participant.classes.crud.get_webinars import FilterInput
from ciba_participant.classes.models import (
    LiveSession,
    Webinar,
    TopicEnum,
    TimeOfDayEnum,
)

test_start_date = pendulum.parse("2025-04-15")
test_end_date = pendulum.parse("2025-04-20")
test_host_id = UUID("5f08e622-9fdb-45f4-b700-71c22a39db95")
base_filters = [
    Q(meeting_start_time__gte=test_start_date),
    Q(meeting_start_time__lte=test_end_date),
]


@pytest.mark.asyncio
async def test_get_live_sessions_with_invalid_date_range():
    """
    get_live_sessions should raise an exception when end date is before start date.
    """
    with pytest.raises(ValueError) as error_info:
        await LiveSessionRepository.get_live_sessions(
            start_date=test_end_date, end_date=test_start_date
        )

    assert error_info.value.args[0] == "Start date cannot be greater than end date."


@pytest.mark.asyncio
async def test_get_live_sessions_with_longer_date_range():
    """
    get_live_sessions should raise an exception when the date range is too long.
    """
    with pytest.raises(ValueError) as error_info:
        await LiveSessionRepository.get_live_sessions(
            start_date=test_start_date, end_date=pendulum.parse("2025-06-15")
        )

    assert (
        error_info.value.args[0]
        == "Range length cannot be greater than max duration allowed (46 days, 0:00:00)."
    )


@pytest.mark.asyncio
async def test_parse_session_success():
    """
    parse_session should map a live session db entry into a raw live session object.
    """
    query_set = MagicMock()
    query_set.count = AsyncMock(return_value=1)
    test_bookings = MagicMock()
    test_bookings.filter = MagicMock(return_value=query_set)

    test_webinar = MagicMock(spec=Webinar)
    test_webinar.id = uuid4()
    test_webinar.host_id = test_host_id
    test_webinar.max_capacity = 10
    test_webinar.topic = TopicEnum.ACTIVITY

    test_live_session = MagicMock(spec=LiveSession)
    test_live_session.id = uuid4()
    test_live_session.created_at = test_start_date
    test_live_session.updated_at = test_start_date
    test_live_session.title = "Mock Title"
    test_live_session.description = "Mock Description"
    test_live_session.meeting_start_time = test_end_date
    test_live_session.webinar_id = test_webinar.id
    test_live_session.zoom_link = "https://zoom.com"
    test_live_session.webinar = test_webinar
    test_live_session.bookings = test_bookings

    actual_value = await parse_session(test_live_session)

    assert actual_value.id == test_live_session.id
    assert actual_value.title == test_live_session.title
    assert actual_value.description == test_live_session.description
    assert actual_value.meeting_start_time == test_end_date
    assert actual_value.host_id == test_host_id
    assert actual_value.max_capacity == test_webinar.max_capacity
    assert actual_value.bookings_count == 1
    assert actual_value.topic == test_webinar.topic


def test_get_query_annotations_without_filters():
    """
    get_query_annotations should return the initial annotations.
    """
    actual_value = get_query_annotations(None, "UTC")

    assert actual_value == {
        "parsed_date": RawSQL("meeting_start_time AT TIME ZONE 'UTC'")
    }


def test_get_query_annotations_with_filters():
    """
    get_query_annotations should return the annotations based on the provided filters.
    """
    actual_value = get_query_annotations(
        FilterInput(time_of_day=TimeOfDayEnum.MORNING), "UTC"
    )

    assert actual_value == {
        "parsed_date": RawSQL("meeting_start_time AT TIME ZONE 'UTC'"),
        "parsed_hour": RawSQL(
            "EXTRACT(HOUR FROM meeting_start_time AT TIME ZONE 'UTC')"
        ),
    }


@pytest.mark.parametrize(
    "test_filters, expected_value",
    [
        (None, base_filters),
        (
            FilterInput(title_like="filter test"),
            [*base_filters, Q(title__icontains="filter test")],
        ),
        (
            FilterInput(topic=TopicEnum.FOOD),
            [*base_filters, Q(webinar__topic=TopicEnum.FOOD)],
        ),
        (
            FilterInput(webinar_host=test_host_id),
            [*base_filters, Q(webinar__host_id=test_host_id)],
        ),
        (
            FilterInput(time_of_day=TimeOfDayEnum.MORNING),
            [*base_filters, Q(parsed_hour__lt=12)],
        ),
        (
            FilterInput(time_of_day=TimeOfDayEnum.AFTERNOON),
            [*base_filters, Q(Q(parsed_hour__gte=12) & Q(parsed_hour__lt=18))],
        ),
        (
            FilterInput(time_of_day=TimeOfDayEnum.EVENING),
            [*base_filters, Q(parsed_hour__gte=18)],
        ),
    ],
)
def test_get_query_filters(test_filters, expected_value):
    """
    get_query_filters should return the query filters based on the provided values.
    """
    actual_value = get_query_filters(
        start_date=test_start_date, end_date=test_end_date, filters=test_filters
    )

    assert actual_value == expected_value
