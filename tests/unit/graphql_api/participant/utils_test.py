from unittest.mock import patch
from uuid import UUID

import pytest

from app.graphql_api.participant.utils import parse_withings_connection_status
from tests.unit.common import test_connection_status

test_id = UUID("7ef864f9-55da-4254-8896-d4fa56aaf5e5")


@pytest.mark.asyncio
@pytest.mark.parametrize("test_status", test_connection_status[-2:])
async def test_parse_withings_connection_status_false_scenarios(test_status):
    """
    parse_withings_connection_status should return false
    when the connection status values are not connected or reconnect.
    """
    with patch(
        "app.graphql_api.participant.utils.get_single_device_status",
        return_value=test_status,
    ):
        actual_value = await parse_withings_connection_status(test_id)

        assert actual_value is False


@pytest.mark.asyncio
async def test_parse_withings_connection_status_true_scenario():
    """
    parse_withings_connection_status should return true
    when the connection status value is connected.
    """
    with patch(
        "app.graphql_api.participant.utils.get_single_device_status",
        return_value=test_connection_status[0],
    ):
        actual_value = await parse_withings_connection_status(test_id)

        assert actual_value is True
