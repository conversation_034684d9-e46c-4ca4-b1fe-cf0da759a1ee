import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from unittest import mock
from uuid import uuid4

import pendulum

from app.graphql_api.participant.mutations import pair_device, unpair_device
from app.graphql_api.participant.types import DeviceDetailedResponse
from ciba_participant.rpm_api.models import (
    DeviceTypeEnum,
    DeviceStatusEnum,
    DetailedConnectionStatus,
)
from ciba_participant.rpm_api.exceptions import RPMCallError


class TestPairDeviceMutation:
    """Test cases for pair_device mutation."""

    @pytest.fixture
    def mock_participant(self):
        """Mock participant fixture."""
        participant = MagicMock()
        participant.id = uuid4()
        participant.email = "<EMAIL>"
        return participant

    @pytest.fixture
    def mock_cohort(self):
        """Mock cohort fixture."""
        cohort = MagicMock()
        cohort.started_at = pendulum.now().subtract(days=7)
        return cohort

    @pytest.mark.asyncio
    async def test_pair_device_unsupported_device_type(self):
        """Test pair_device with unsupported device type."""
        # Create a mock device type that's not supported
        from unittest.mock import MagicMock

        unsupported_device_type = MagicMock()
        unsupported_device_type.name = "UNSUPPORTED"

        result = await pair_device(
            participant_id=uuid4(),
            device_type=unsupported_device_type,
        )

        assert isinstance(result, DeviceDetailedResponse)
        assert result.success is False
        assert result.message == "Device type not supported"

    @pytest.mark.asyncio
    async def test_pair_device_participant_not_found(self):
        """Test pair_device when participant is not found."""
        participant_id = uuid4()

        with patch(
            "app.graphql_api.participant.mutations.Participant.filter"
        ) as mock_filter:
            mock_filter.return_value.get_or_none = AsyncMock(return_value=None)

            result = await pair_device(participant_id=participant_id)

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "Unexpected error" in result.message

    @pytest.mark.asyncio
    async def test_pair_withings_device_already_connected_and_healthy(
        self, mock_participant
    ):
        """Test pair_device when Withings device is already connected and healthy."""
        mock_device_status = DetailedConnectionStatus(
            healthy=True,
            token="test_token",
            status=DeviceStatusEnum.CONNECTED,
            device=DeviceTypeEnum.WITHINGS,
            account_id="123456",
        )

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.get_single_device_status",
                new_callable=AsyncMock,
                return_value=mock_device_status,
            ),
            patch(
                "app.graphql_api.participant.mutations.set_user_weight_status",
                new_callable=AsyncMock,
                return_value=True,
            ) as mock_set_weight,
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )

            result = await pair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.WITHINGS,
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is True
            assert (
                "Withings device is already connected and healthy"
                in result.message
            )
            assert result.device_status == DeviceStatusEnum.CONNECTED.value
            assert result.requires_authentication is False

            # Verify weight status was set to True
            mock_set_weight.assert_called_once_with(
                mock_participant, True, mock.ANY
            )

    @pytest.mark.asyncio
    async def test_pair_withings_device_needs_authentication_with_auth_url(
        self, mock_participant, mock_cohort
    ):
        """Test pair_device when Withings device needs authentication and auth URL is returned."""
        mock_device_status = DetailedConnectionStatus(
            healthy=False,
            token=None,
            status=DeviceStatusEnum.NOT_CONNECTED,
            device=DeviceTypeEnum.WITHINGS,
            account_id=None,
        )

        mock_auth_response = {"auth_url": "https://example.com/auth"}

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.get_single_device_status",
                new_callable=AsyncMock,
                return_value=mock_device_status,
            ),
            patch(
                "app.graphql_api.participant.mutations.Cohort.filter"
            ) as mock_cohort_filter,
            patch(
                "app.graphql_api.participant.mutations.RPMRequestHandler"
            ) as mock_rpm_handler,
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )
            mock_cohort_filter.return_value.first = AsyncMock(
                return_value=mock_cohort
            )
            mock_rpm_handler.return_value.get_device_auth = AsyncMock(
                return_value=mock_auth_response
            )

            result = await pair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.WITHINGS,
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is True
            assert "Authentication required" in result.message
            assert result.auth_url == "https://example.com/auth"
            assert result.device_status == DeviceStatusEnum.NOT_CONNECTED.value
            assert result.requires_authentication is True

    @pytest.mark.asyncio
    async def test_pair_withings_device_needs_authentication_no_auth_url(
        self, mock_participant, mock_cohort
    ):
        """Test pair_device when Withings device needs authentication but no auth URL is returned."""
        mock_device_status = DetailedConnectionStatus(
            healthy=False,
            token=None,
            status=DeviceStatusEnum.NOT_CONNECTED,
            device=DeviceTypeEnum.WITHINGS,
            account_id=None,
        )

        mock_auth_response = {}  # No auth_url

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.get_single_device_status",
                new_callable=AsyncMock,
                return_value=mock_device_status,
            ),
            patch(
                "app.graphql_api.participant.mutations.Cohort.filter"
            ) as mock_cohort_filter,
            patch(
                "app.graphql_api.participant.mutations.RPMRequestHandler"
            ) as mock_rpm_handler,
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )
            mock_cohort_filter.return_value.first = AsyncMock(
                return_value=mock_cohort
            )
            mock_rpm_handler.return_value.get_device_auth = AsyncMock(
                return_value=mock_auth_response
            )

            result = await pair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.WITHINGS,
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert (
                "Failed to get authentication URL for Withings device"
                in result.message
            )
            assert result.device_status == DeviceStatusEnum.NOT_CONNECTED.value
            assert result.requires_authentication is True

    @pytest.mark.asyncio
    async def test_pair_transtek_device_successful(self, mock_participant):
        """Test successful Transtek device pairing with IMEI and serial number."""
        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.pair_transtek_device",
                new_callable=AsyncMock,
            ) as mock_pair,
            patch(
                "app.graphql_api.participant.mutations.set_user_weight_status",
                new_callable=AsyncMock,
                return_value=True,
            ) as mock_set_weight,
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )

            result = await pair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.TRANSTEK,
                imei="123456789",
                serial_number="SN123",
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is True
            assert "Transtek device paired successfully" in result.message
            assert result.device_status == "paired"
            assert result.requires_authentication is False

            # Verify pair_transtek_device was called with correct parameters
            mock_pair.assert_called_once_with(
                participant_id=mock_participant.id,
                participant_email=mock_participant.email,
                imei="123456789",
                serial_number="SN123",
            )

            # Verify weight status was set to True
            mock_set_weight.assert_called_once_with(
                mock_participant, True, mock.ANY
            )

    @pytest.mark.asyncio
    async def test_pair_transtek_device_missing_identifiers(
        self, mock_participant
    ):
        """Test Transtek device pairing fails when IMEI and serial number are missing."""
        with patch(
            "app.graphql_api.participant.mutations.Participant.filter"
        ) as mock_filter:
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )

            result = await pair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.TRANSTEK,
                # No IMEI or serial_number provided
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "IMEI or serial number is required" in result.message
            assert result.requires_authentication is False

    @pytest.mark.asyncio
    async def test_pair_transtek_device_rpm_call_error(self, mock_participant):
        """Test pair_device when RPMCallError is raised during Transtek pairing."""
        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.pair_transtek_device",
                new_callable=AsyncMock,
                side_effect=RPMCallError("RPM service error"),
            ),
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )

            result = await pair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.TRANSTEK,
                imei="123456789",
                serial_number="SN123",
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "RPM service error" in result.message

    @pytest.mark.asyncio
    async def test_pair_device_unexpected_error(self, mock_participant):
        """Test pair_device when unexpected error occurs."""
        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.pair_transtek_device",
                new_callable=AsyncMock,
                side_effect=Exception("Unexpected error"),
            ),
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )

            result = await pair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.TRANSTEK,
                imei="123456789",
                serial_number="SN123",
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "Unexpected error" in result.message


class TestUnpairDeviceMutation:
    """Test cases for unpair_device mutation."""

    @pytest.fixture
    def mock_participant(self):
        """Mock participant fixture."""
        participant = MagicMock()
        participant.id = uuid4()
        participant.email = "<EMAIL>"
        return participant

    @pytest.mark.asyncio
    async def test_unpair_device_unsupported_device_type(self):
        """Test unpair_device with unsupported device type."""
        # Create a mock device type that's not supported
        from unittest.mock import MagicMock

        unsupported_device_type = MagicMock()
        unsupported_device_type.name = "UNSUPPORTED"

        result = await unpair_device(
            participant_id=uuid4(),
            device_id=uuid4(),
            device_type=unsupported_device_type,
        )

        assert isinstance(result, DeviceDetailedResponse)
        assert result.success is False
        assert result.message == "Device type not supported"

    @pytest.mark.asyncio
    async def test_unpair_device_participant_not_found(self):
        """Test unpair_device when participant is not found."""
        participant_id = uuid4()
        device_id = uuid4()

        with patch(
            "app.graphql_api.participant.mutations.Participant.filter"
        ) as mock_filter:
            mock_filter.return_value.get_or_none = AsyncMock(return_value=None)

            result = await unpair_device(
                participant_id=participant_id, device_id=device_id
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "Unexpected error" in result.message

    @pytest.mark.asyncio
    async def test_unpair_transtek_device_successful(self, mock_participant):
        """Test successful Transtek device unpairing."""

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.disconnect_device",
                new_callable=AsyncMock,
            ) as mock_disconnect,
            patch(
                "app.graphql_api.participant.mutations.set_user_weight_status",
                new_callable=AsyncMock,
                return_value=True,
            ) as mock_set_weight,
        ):
            # Mock both calls to Participant.filter (main function and helper function)
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )

            result = await unpair_device(
                participant_id=mock_participant.id,
                device_type=DeviceTypeEnum.TRANSTEK,
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is True
            assert "Transtek device unpaired successfully" in result.message
            assert result.device_status == "disconnected"
            assert result.requires_authentication is False

            # Verify disconnect_device was called with correct parameters
            mock_disconnect.assert_called_once_with(
                participant_id=mock_participant.id,
                device_model=None,
                device_type=DeviceTypeEnum.TRANSTEK,
            )

            # Verify weight status was set to False
            mock_set_weight.assert_called_once_with(
                mock_participant, False, mock.ANY
            )

    @pytest.mark.asyncio
    async def test_unpair_withings_device_successful(self, mock_participant):
        """Test successful Withings device unpairing."""
        device_id = uuid4()
        mock_rpm_response = {"disconnected": True}

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.RPMRequestHandler"
            ) as mock_rpm_handler,
            patch(
                "app.graphql_api.participant.mutations.set_user_weight_status",
                new_callable=AsyncMock,
                return_value=True,
            ) as mock_set_weight,
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )
            mock_rpm_handler.return_value.disconnect = AsyncMock(
                return_value=mock_rpm_response
            )

            result = await unpair_device(
                participant_id=mock_participant.id,
                device_id=device_id,
                device_type=DeviceTypeEnum.WITHINGS,
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is True
            assert "Withings device unpaired successfully" in result.message
            assert result.device_status == "disconnected"
            assert result.requires_authentication is False

            # Verify RPM disconnect was called with correct parameters
            mock_rpm_handler.return_value.disconnect.assert_called_once_with(
                type_device="withings",
                member_type="participant",
                member_id=str(mock_participant.id),
                correlation_id=mock.ANY,
            )

            # Verify weight status was set to False
            mock_set_weight.assert_called_once_with(
                mock_participant, False, mock.ANY
            )

    @pytest.mark.asyncio
    async def test_unpair_withings_device_failed_disconnect(
        self, mock_participant
    ):
        """Test Withings device unpairing when disconnect fails."""
        device_id = uuid4()
        mock_rpm_response = {"disconnected": False}

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.RPMRequestHandler"
            ) as mock_rpm_handler,
            patch(
                "app.graphql_api.participant.mutations.set_user_weight_status",
                new_callable=AsyncMock,
            ) as mock_set_weight,
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )
            mock_rpm_handler.return_value.disconnect = AsyncMock(
                return_value=mock_rpm_response
            )

            result = await unpair_device(
                participant_id=mock_participant.id,
                device_id=device_id,
                device_type=DeviceTypeEnum.WITHINGS,
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "Failed to disconnect Withings device" in result.message
            assert result.device_status == "connected"
            assert result.requires_authentication is False

            # Verify weight status was NOT called since disconnect failed
            mock_set_weight.assert_not_called()

    @pytest.mark.asyncio
    async def test_unpair_device_rpm_call_error(self, mock_participant):
        """Test unpair_device when RPMCallError is raised."""
        device_id = uuid4()

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
            patch(
                "app.graphql_api.participant.mutations.disconnect_device",
                new_callable=AsyncMock,
                side_effect=RPMCallError("RPM service error"),
            ),
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                return_value=mock_participant
            )

            result = await unpair_device(
                participant_id=mock_participant.id, device_id=device_id
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "RPM service error" in result.message

    @pytest.mark.asyncio
    async def test_unpair_device_unexpected_error(self, mock_participant):
        """Test unpair_device when unexpected error occurs."""
        device_id = uuid4()

        with (
            patch(
                "app.graphql_api.participant.mutations.Participant.filter"
            ) as mock_filter,
        ):
            mock_filter.return_value.get_or_none = AsyncMock(
                side_effect=Exception("Unexpected error")
            )

            result = await unpair_device(
                participant_id=mock_participant.id, device_id=device_id
            )

            assert isinstance(result, DeviceDetailedResponse)
            assert result.success is False
            assert "Unexpected error" in result.message
