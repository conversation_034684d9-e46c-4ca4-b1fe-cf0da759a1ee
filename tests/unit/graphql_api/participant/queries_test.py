import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime

from app.graphql_api.participant.queries import get_transtek_device_info
from app.graphql_api.participant.types import TranstekDeviceInfoType
from ciba_participant.rpm_api.exceptions import RPMCallError


class TestGetTranstekDeviceInfoQuery:
    """Test cases for get_transtek_device_info query."""

    @pytest.fixture
    def mock_transtek_device_info(self):
        """Mock TranstekDeviceInfo fixture."""
        mock_device = MagicMock()
        mock_device.id = "device_123"
        mock_device.device_id = "SN123456789"
        mock_device.imei = "123456789012345"
        mock_device.model = "LS207-B"
        mock_device.device_type = "scale"
        mock_device.status = "active"
        mock_device.tracking_number = "1Z999AA1234567890"
        mock_device.carrier = "UPS"
        mock_device.tracking_url = (
            "https://www.ups.com/track?tracknum=1Z999AA1234567890"
        )
        mock_device.timezone = "America/New_York"
        mock_device.last_status_report = "2023-12-01T10:30:00Z"
        mock_device.member_id = "participant_456"
        return mock_device

    @pytest.fixture
    def mock_transtek_device_info_minimal(self):
        """Mock TranstekDeviceInfo with minimal required fields."""
        mock_device = MagicMock()
        mock_device.id = "device_456"
        mock_device.device_id = "SN987654321"
        mock_device.imei = "987654321098765"
        mock_device.model = "LS207-A"
        mock_device.device_type = "scale"
        mock_device.status = "inactive"
        mock_device.tracking_number = None
        mock_device.carrier = None
        mock_device.tracking_url = None
        mock_device.timezone = None
        mock_device.last_status_report = None
        mock_device.member_id = None
        return mock_device

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_success_full_data(
        self, mock_transtek_device_info
    ):
        """Test get_transtek_device_info with complete device information."""
        participant_id = uuid4()

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            return_value=mock_transtek_device_info,
        ) as mock_get_device:
            result = await get_transtek_device_info(participant_id)

            # Verify the API was called with correct participant_id
            mock_get_device.assert_called_once_with(participant_id)

            # Verify the result is correct type
            assert isinstance(result, TranstekDeviceInfoType)

            # Verify all fields are correctly mapped
            assert result.id == "device_123"
            assert result.serial_number == "SN123456789"
            assert result.imei == "123456789012345"
            assert result.model == "LS207-B"
            assert result.device_type == "scale"
            assert result.status == "active"
            assert result.tracking_number == "1Z999AA1234567890"
            assert result.carrier == "UPS"
            assert (
                result.tracking_url
                == "https://www.ups.com/track?tracknum=1Z999AA1234567890"
            )
            assert result.timezone == "America/New_York"
            assert result.last_status_report == "2023-12-01T10:30:00Z"
            assert result.member_id == "participant_456"

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_success_minimal_data(
        self, mock_transtek_device_info_minimal
    ):
        """Test get_transtek_device_info with minimal device information."""
        participant_id = uuid4()

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            return_value=mock_transtek_device_info_minimal,
        ) as mock_get_device:
            result = await get_transtek_device_info(participant_id)

            # Verify the API was called with correct participant_id
            mock_get_device.assert_called_once_with(participant_id)

            # Verify the result is correct type
            assert isinstance(result, TranstekDeviceInfoType)

            # Verify required fields are present
            assert result.id == "device_456"
            assert result.serial_number == "SN987654321"
            assert result.imei == "987654321098765"
            assert result.model == "LS207-A"
            assert result.device_type == "scale"
            assert result.status == "inactive"

            # Verify optional fields are None
            assert result.tracking_number is None
            assert result.carrier is None
            assert result.tracking_url is None
            assert result.timezone is None
            assert result.last_status_report is None
            assert result.member_id is None

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_rpm_call_error(self):
        """Test get_transtek_device_info when RPMCallError is raised."""
        participant_id = uuid4()

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            side_effect=RPMCallError("Device not found"),
        ) as mock_get_device:
            with pytest.raises(RPMCallError, match="Device not found"):
                await get_transtek_device_info(participant_id)

            # Verify the API was called with correct participant_id
            mock_get_device.assert_called_once_with(participant_id)

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_unexpected_error(self):
        """Test get_transtek_device_info when unexpected error occurs."""
        participant_id = uuid4()

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            side_effect=Exception("Unexpected error"),
        ) as mock_get_device:
            with pytest.raises(Exception, match="Unexpected error"):
                await get_transtek_device_info(participant_id)

            # Verify the API was called with correct participant_id
            mock_get_device.assert_called_once_with(participant_id)

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_with_datetime_last_status_report(
        self,
    ):
        """Test get_transtek_device_info with datetime last_status_report conversion."""

        participant_id = uuid4()

        # Create mock device info with datetime object for last_status_report
        mock_device = MagicMock()
        mock_device.id = "device_789"
        mock_device.device_id = "SN111222333"
        mock_device.imei = "111222333444555"
        mock_device.model = "LS207-C"
        mock_device.device_type = "scale"
        mock_device.status = "active"
        mock_device.tracking_number = None
        mock_device.carrier = None
        mock_device.tracking_url = None
        mock_device.timezone = None
        mock_device.last_status_report = datetime(2023, 12, 15, 14, 30, 0)
        mock_device.member_id = None

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            return_value=mock_device,
        ) as mock_get_device:
            result = await get_transtek_device_info(participant_id)

            # Verify the API was called with correct participant_id
            mock_get_device.assert_called_once_with(participant_id)

            # Verify the result is correct type
            assert isinstance(result, TranstekDeviceInfoType)

            # Verify datetime is converted to string
            assert result.last_status_report == "2023-12-15 14:30:00"

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_type_conversion(self):
        """Test that TranstekDeviceInfoType.from_model correctly converts all field types."""
        participant_id = uuid4()

        # Create mock device info with various data types
        mock_device = MagicMock()
        mock_device.id = "device_999"
        mock_device.device_id = "SN999888777"
        mock_device.imei = "999888777666555"
        mock_device.model = "LS207-D"
        mock_device.device_type = "scale"
        mock_device.status = "maintenance"
        mock_device.tracking_number = "FEDEX123456789"
        mock_device.carrier = "FedEx"
        mock_device.tracking_url = (
            "https://www.fedex.com/track?tracknum=FEDEX123456789"
        )
        mock_device.timezone = "UTC"
        mock_device.last_status_report = (
            "2023-12-20T09:15:30Z"  # String format
        )
        mock_device.member_id = "participant_999"

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            return_value=mock_device,
        ):
            result = await get_transtek_device_info(participant_id)

            # Verify all fields are strings as expected by GraphQL
            assert isinstance(result.id, str)
            assert isinstance(result.serial_number, str)
            assert isinstance(result.imei, str)
            assert isinstance(result.model, str)
            assert isinstance(result.device_type, str)
            assert isinstance(result.status, str)
            assert isinstance(result.tracking_number, str)
            assert isinstance(result.carrier, str)
            assert isinstance(result.tracking_url, str)
            assert isinstance(result.timezone, str)
            assert isinstance(result.last_status_report, str)
            assert isinstance(result.member_id, str)

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_field_mapping(self):
        """Test that device_id is correctly mapped to serial_number."""
        participant_id = uuid4()

        mock_device = MagicMock()
        mock_device.id = "device_mapping_test"
        mock_device.device_id = (
            "DEVICE_ID_123"  # This should map to serial_number
        )
        mock_device.imei = "123456789012345"
        mock_device.model = "LS207-E"
        mock_device.device_type = "scale"
        mock_device.status = "active"
        mock_device.tracking_number = None
        mock_device.carrier = None
        mock_device.tracking_url = None
        mock_device.timezone = None
        mock_device.last_status_report = None
        mock_device.member_id = None

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            return_value=mock_device,
        ):
            result = await get_transtek_device_info(participant_id)

            # Verify device_id is mapped to serial_number
            assert result.serial_number == "DEVICE_ID_123"
            assert result.id == "device_mapping_test"

    @pytest.mark.asyncio
    async def test_get_transtek_device_info_none_handling(self):
        """Test that None values are handled correctly in optional fields."""
        participant_id = uuid4()

        mock_device = MagicMock()
        mock_device.id = "device_none_test"
        mock_device.device_id = "SN_NONE_TEST"
        mock_device.imei = "000000000000000"
        mock_device.model = "LS207-F"
        mock_device.device_type = "scale"
        mock_device.status = "inactive"
        mock_device.tracking_number = None
        mock_device.carrier = None
        mock_device.tracking_url = None
        mock_device.timezone = None
        mock_device.last_status_report = None
        mock_device.member_id = None

        with patch(
            "app.graphql_api.participant.queries.get_transtek_device",
            new_callable=AsyncMock,
            return_value=mock_device,
        ):
            result = await get_transtek_device_info(participant_id)

            # Verify None values are preserved for optional fields
            assert result.tracking_number is None
            assert result.carrier is None
            assert result.tracking_url is None
            assert result.timezone is None
            assert result.last_status_report is None
            assert result.member_id is None

            # Verify required fields are still present
            assert result.id == "device_none_test"
            assert result.serial_number == "SN_NONE_TEST"
            assert result.imei == "000000000000000"
            assert result.model == "LS207-F"
            assert result.device_type == "scale"
            assert result.status == "inactive"
