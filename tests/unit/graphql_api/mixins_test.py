from dataclasses import dataclass
import pytest

from app.graphql_api.mixins import MarshalMixin


@dataclass
class TestModel:
    id: int
    name: str
    description: str = ""


@dataclass
class TestType(MarshalMixin):
    id: int
    name: str
    description: str = ""
    custom_field: str = ""


@dataclass
class TestTypeWithAliases(MarshalMixin):
    id: int
    full_name: str
    description: str = ""

    _aliases = {"full_name": "name"}


@dataclass
class TestTypeWithCustomGetter(MarshalMixin):
    id: int
    name: str
    description: str = ""
    custom_field: str = ""

    @staticmethod
    def get_custom_field(instance):
        return f"Custom: {instance.name}"


@dataclass
class TestTypeWithAsyncGetter(MarshalMixin):
    id: int
    name: str
    description: str = ""
    async_field: str = ""

    @staticmethod
    async def get_async_field(instance):
        return f"Async: {instance.name}"


@pytest.mark.asyncio
async def test_marshal_basic():
    """Test basic marshaling of an instance to a dataclass."""
    # Arrange
    instance = TestModel(id=1, name="Test", description="Test Description")

    # Act
    result = await TestType.marshal(instance)

    # Assert
    assert result.id == instance.id
    assert result.name == instance.name
    assert result.description == instance.description
    assert result.custom_field == ""  # Default value


@pytest.mark.asyncio
async def test_marshal_with_defaults():
    """Test marshaling with default values."""
    # Arrange
    instance = TestModel(id=1, name="Test")
    defaults = {
        "description": "Default Description",
        "custom_field": "Default Custom",
    }

    # Act
    result = await TestType.marshal(instance, defaults)

    # Assert
    assert result.id == instance.id
    assert result.name == instance.name
    assert result.description == "Default Description"
    assert result.custom_field == "Default Custom"


@pytest.mark.asyncio
async def test_marshal_with_aliases():
    """Test marshaling with field aliases."""
    # Arrange
    instance = TestModel(id=1, name="Test", description="Test Description")

    # Act
    result = await TestTypeWithAliases.marshal(instance)

    # Assert
    assert result.id == instance.id
    assert result.full_name == instance.name  # Aliased from 'name'
    assert result.description == instance.description


@pytest.mark.asyncio
async def test_marshal_with_custom_getter():
    """Test marshaling with custom getter methods."""
    # Arrange
    instance = TestModel(id=1, name="Test", description="Test Description")

    # Act
    result = await TestTypeWithCustomGetter.marshal(instance)

    # Assert
    assert result.id == instance.id
    assert result.name == instance.name
    assert result.description == instance.description
    assert result.custom_field == "Custom: Test"


@pytest.mark.asyncio
async def test_marshal_with_async_getter():
    """Test marshaling with async getter methods."""
    # Arrange
    instance = TestModel(id=1, name="Test", description="Test Description")

    # Act
    result = await TestTypeWithAsyncGetter.marshal(instance)

    # Assert
    assert result.id == instance.id
    assert result.name == instance.name
    assert result.description == instance.description
    assert result.async_field == "Async: Test"


@pytest.mark.asyncio
async def test_marshal_many():
    """Test marshaling multiple instances."""
    # Arrange
    instances = [
        TestModel(id=1, name="Test 1", description="Description 1"),
        TestModel(id=2, name="Test 2", description="Description 2"),
        TestModel(id=3, name="Test 3", description="Description 3"),
    ]

    # Act
    results = await TestType.marshal_many(instances)

    # Assert
    assert len(results) == len(instances)
    for i, result in enumerate(results):
        assert result.id == instances[i].id
        assert result.name == instances[i].name
        assert result.description == instances[i].description


@pytest.mark.asyncio
async def test_marshal_many_with_defaults():
    """Test marshaling multiple instances with defaults."""
    # Arrange
    instances = [
        TestModel(id=1, name="Test 1"),
        TestModel(id=2, name="Test 2"),
        TestModel(id=3, name="Test 3"),
    ]
    defaults = {
        "description": "Default Description",
        "custom_field": "Default Custom",
    }

    # Act
    results = await TestType.marshal_many(instances, defaults)

    # Assert
    assert len(results) == len(instances)
    for i, result in enumerate(results):
        assert result.id == instances[i].id
        assert result.name == instances[i].name
        assert result.description == "Default Description"
        assert result.custom_field == "Default Custom"
