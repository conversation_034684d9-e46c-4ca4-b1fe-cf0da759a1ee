from unittest.mock import <PERSON><PERSON><PERSON>, patch, Mock
from uuid import uuid4

import pytest
from strawberry.exceptions import GraphQLError
from tortoise.exceptions import BaseORMException

from app.graphql_api import schema

test_id = uuid4()
test_query = """
    query GetClassesCreators {
      getClassesCreators {
        id
        fullName
        isAdmin
      }
    }
"""


@pytest.fixture
def mock_context():
    test_user = MagicMock()
    test_user.sub = uuid4()
    test_user.is_authenticated = True
    test_request = MagicMock()
    test_request.headers = {"Time-Zone-Offset": "00:00"}
    test_context = MagicMock()
    test_context.user = test_user
    test_context.request = test_request

    return test_context


@pytest.mark.asyncio
async def test_get_classes_creators_with_errors(mock_context):
    """
    get_classes_creators should return an empty list with unsuccessful status
    when a database error occurs.
    """
    with (
        patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
        patch(
            "ciba_participant.classes.crud.WebinarRepository.get_creators",
            side_effect=BaseORMException(),
        ),
    ):
        actual_value = await schema.execute(
            test_query, context_value=mock_context
        )

        assert actual_value.data is None
        assert actual_value.errors is not None
        assert isinstance(actual_value.errors[0], GraphQLError)


@pytest.mark.asyncio
async def test_get_classes_creators_success(mock_context):
    """
    get_classes_creators should return a list of users.
    """
    test_author = MagicMock()
    test_author.id = test_id
    test_author.full_name = Mock(return_value="Jane Doe")
    test_author.first_name = "Jane"
    test_author.last_name = "Doe"
    test_author.email = "<EMAIL>"
    test_author.chat_identity = "randomId"
    test_author.role = "admin"

    with (
        patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
        patch(
            "ciba_participant.classes.crud.WebinarRepository.get_creators",
            return_value=[test_author],
        ),
    ):
        actual_value = await schema.execute(
            test_query, context_value=mock_context
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getClassesCreators"] == [
            {"id": str(test_id), "fullName": "Jane Doe", "isAdmin": True},
        ]
