import uuid
from datetime import date


from app.graphql_api.admin.inputs import AdminInput, AdminsInput, CohortInput


def test_admin_input():
    """Test that AdminInput can be instantiated with correct fields."""
    # Create test data
    test_id = uuid.uuid4()
    test_chat_identity = "test_user"

    # Create AdminInput instance
    admin_input = AdminInput(id=test_id, chat_identity=test_chat_identity)

    # Verify fields
    assert admin_input.id == test_id
    assert admin_input.chat_identity == test_chat_identity

    # Verify it's a strawberry input type
    assert hasattr(admin_input, "__strawberry_definition__")


def test_admins_input():
    """Test that AdminsInput can be instantiated with a list of AdminInput."""
    # Create test data
    test_id1 = uuid.uuid4()
    test_id2 = uuid.uuid4()
    test_chat_identity1 = "test_user1"
    test_chat_identity2 = "test_user2"

    # Create AdminInput instances
    admin_input1 = AdminInput(id=test_id1, chat_identity=test_chat_identity1)
    admin_input2 = AdminInput(id=test_id2, chat_identity=test_chat_identity2)

    # Create AdminsInput instance
    admins_input = AdminsInput(admins=[admin_input1, admin_input2])

    # Verify fields
    assert len(admins_input.admins) == 2
    assert admins_input.admins[0].id == test_id1
    assert admins_input.admins[0].chat_identity == test_chat_identity1
    assert admins_input.admins[1].id == test_id2
    assert admins_input.admins[1].chat_identity == test_chat_identity2

    # Verify it's a strawberry input type
    assert hasattr(admins_input, "__strawberry_definition__")


def test_cohort_input():
    """Test that CohortInput can be instantiated with correct fields."""
    # Create test data
    test_start_date = date(2023, 1, 1)
    test_title = "Test Cohort"
    test_limit = 10

    # Create CohortInput instance
    cohort_input = CohortInput(
        start_date=test_start_date, title=test_title, limit=test_limit
    )

    # Verify fields
    assert cohort_input.start_date == test_start_date
    assert cohort_input.title == test_title
    assert cohort_input.limit == test_limit

    # Verify it's a strawberry input type
    assert hasattr(cohort_input, "__strawberry_definition__")
