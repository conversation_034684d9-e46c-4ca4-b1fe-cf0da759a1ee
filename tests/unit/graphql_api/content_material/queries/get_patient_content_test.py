from unittest.mock import MagicMock, patch
from uuid import uuid4

import pendulum
import pytest

from app.graphql_api import schema


test_material_id = uuid4()
test_date = pendulum.parse("2025-07-04")
test_query_fields = """
        pageInfo {
          hasNextPage
          hasPreviousPage
          currentPage
          perPage
          lastPage
          total
        }
        items {
          id
          addedAt
          mimeType
          title
          description
          tags
          link
          linkExpiration
        }
        success
        error
"""


@pytest.fixture
def mock_material_list():
    """
    fixture that mocks a material query response from ciba_participant.
    """
    test_material = MagicMock()
    test_material.id = test_material_id
    test_material.created_at = test_date
    test_material.mime_type = "text/plain"
    test_material.title = "Mocked material"
    test_material.description = "Mocked description"
    test_material.link = "https://mock.test"
    test_material.file_url_expiration = test_date
    test_material.tags = []
    test_material.activity_types = []

    mock_response = MagicMock()
    mock_response.total = 1
    mock_response.total_pages = 1
    mock_response.items = [test_material]

    return mock_response


@pytest.mark.asyncio
async def test_get_patient_content_material_without_filters(
    mock_query_context, mock_material_list
):
    """
    get_patient_content_material should return all the content materials.
    """
    test_query = f"""
        query GetPatientContentMaterial {{
          getPatientContentMaterial {{
            {test_query_fields}
          }}
        }}
    """

    with (
        patch(
            "ciba_participant.content_library.crud.ContentMaterialRepository.get_material",
            return_value=mock_material_list,
        ),
        patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await schema.execute(
            test_query, context_value=mock_query_context
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert (
            actual_value.data["getPatientContentMaterial"]["success"] is True
        )
        assert actual_value.data["getPatientContentMaterial"]["items"] == [
            {
                "id": str(test_material_id),
                "addedAt": test_date.isoformat(),
                "mimeType": "text/plain",
                "title": "Mocked material",
                "description": "Mocked description",
                "tags": [],
                "link": "https://mock.test",
                "linkExpiration": test_date.isoformat(),
            }
        ]


@pytest.mark.asyncio
async def test_get_patient_content_material_with_invalid_pagination(
    mock_query_context, mock_material_list
):
    """
    get_patient_content_material should return unsuccessful response
    when invalid pagination parameters are provided
    """
    test_query = f"""
        query GetPatientContentMaterial($page: Int, $perPage: Int) {{
          getPatientContentMaterial(page: $page, perPage: $perPage) {{
            {test_query_fields}
          }}
        }}
    """
    variables = {
        "page": 0,
        "per_page": 10,
    }

    with patch(
        "app.auth.permissions.IsAuthenticated.has_permission",
        return_value=True,
    ):
        actual_value = await schema.execute(
            test_query,
            context_value=mock_query_context,
            variable_values=variables,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert (
            actual_value.data["getPatientContentMaterial"]["success"] is False
        )
        assert (
            actual_value.data["getPatientContentMaterial"]["error"]
            == "Pagination parameters must be greater than 0"
        )


@pytest.mark.asyncio
async def test_get_patient_content_material_with_filters(
    mock_query_context, mock_material_list
):
    """
    get_patient_content_material should return the filtered material
    """
    test_query = f"""
        query GetPatientContentMaterial($filters: FavoriteFilters) {{
          getPatientContentMaterial(filters: $filters) {{
            {test_query_fields}
          }}
        }}
    """
    variables = {"filters": {"search": "Testing"}}
    test_materials = mock_material_list
    test_materials.items = []

    with (
        patch(
            "ciba_participant.content_library.crud.ContentMaterialRepository.get_material",
            return_value=test_materials,
        ),
        patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await schema.execute(
            test_query,
            context_value=mock_query_context,
            variable_values=variables,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert (
            actual_value.data["getPatientContentMaterial"]["success"] is True
        )
        assert actual_value.data["getPatientContentMaterial"]["items"] == []
