from unittest.mock import MagicMock, patch
from uuid import uuid4

import pendulum
import pytest

from app.graphql_api import schema
from app.graphql_api.content_library.constants import NO_PROGRAM
from app.graphql_api.content_library.enums import MaterialStatusEnum
from ciba_participant.program.models import Program

PERMISSIONS_MODULE = "app.auth.permissions.IsAuthenticated.has_permission"
test_material_id = uuid4()
test_date = pendulum.parse("1995-12-07")
test_favorite_content_query = """
    query GetFavoriteMaterials($page: Int, $perPage: Int) {
      getFavoriteMaterials(page: $page, perPage: $perPage) {
        items {
          id
          isFavorite
          linkExpiration
        }
        success
        error
      }
    }
"""
test_variables = {"page": 1, "perPage": 10}


@pytest.fixture
def mock_material_list():
    """
    fixture that mocks a material query response from ciba_participant.
    """
    test_material = MagicMock()
    test_material.id = test_material_id
    test_material.created_at = test_date
    test_material.mime_type = "text/plain"
    test_material.title = "Mocked material"
    test_material.description = "Mocked description"
    test_material.status = MaterialStatusEnum.ACTIVE
    test_material.link = "https://mock.test"
    test_material.file_url_expiration = test_date
    test_material.tags = []
    test_material.activity_types = []

    mock_response = MagicMock()
    mock_response.total = 1
    mock_response.total_pages = 1
    mock_response.items = [test_material]

    return mock_response


@pytest.mark.asyncio
async def test_get_favorite_content_with_no_program_error(
    mock_material_list, mock_query_context
):
    """
    get_favorite_content should return an unsuccessful response
    when the participant does not belong to any program.
    """
    query = test_favorite_content_query

    with (
        patch(
            PERMISSIONS_MODULE,
            return_value=True,
        ),
        patch(
            "ciba_participant.participant.crud.ParticipantRepository.get_participant_program",
            return_value=None,
        ),
    ):
        actual_value = await schema.execute(
            query,
            context_value=mock_query_context,
            variable_values=test_variables,
        )

        assert actual_value.data is not None
        assert actual_value.data["getFavoriteMaterials"]["success"] is False
        assert actual_value.data["getFavoriteMaterials"]["items"] == []
        assert actual_value.data["getFavoriteMaterials"]["error"] == NO_PROGRAM


@pytest.mark.asyncio
async def test_get_favorite_content_with_invalid_pagination(
    mock_material_list, mock_query_context
):
    """
    get_favorite_content should return an unsuccessful response
    when the provided pagination params are not valid.
    """
    query = test_favorite_content_query

    with (
        patch(PERMISSIONS_MODULE, return_value=True),
        patch(
            "ciba_participant.participant.crud.ParticipantRepository.get_participant_program",
            return_value=None,
        ),
    ):
        actual_value = await schema.execute(
            query,
            context_value=mock_query_context,
            variable_values={"page": -1, "perPage": 0},
        )

        assert actual_value.data is not None
        assert (
            actual_value.data["getFavoriteMaterials"]["error"]
            == "Pagination parameters must be greater than 0"
        )


@pytest.mark.asyncio
async def test_get_favorite_content_default_response(
    mock_material_list, mock_query_context
):
    """
    get_favorite_content should return the provided participant favorite content.
    """
    query = test_favorite_content_query
    mock_program = MagicMock(spec=Program)
    mock_program.id = uuid4()

    with (
        patch(
            PERMISSIONS_MODULE,
            return_value=True,
        ),
        patch(
            "ciba_participant.participant.crud.ParticipantRepository.get_participant_program",
            return_value=mock_program,
        ),
        patch(
            "ciba_participant.content_library.crud.MaterialInteractionRepository.get_favorite_content",
            return_value=[test_material_id],
        ),
        patch(
            "ciba_participant.content_library.crud.ContentMaterialRepository.get_material",
            return_value=mock_material_list,
        ),
    ):
        actual_value = await schema.execute(
            query,
            context_value=mock_query_context,
            variable_values=test_variables,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getFavoriteMaterials"]["success"] is True
        assert actual_value.data["getFavoriteMaterials"]["items"][0][
            "id"
        ] == str(test_material_id)
        assert (
            actual_value.data["getFavoriteMaterials"]["items"][0][
                "linkExpiration"
            ]
            == test_date.isoformat()
        )
        assert (
            actual_value.data["getFavoriteMaterials"]["items"][0]["isFavorite"]
            is True
        )
