from unittest.mock import MagicMock
from uuid import uuid4

import pytest

from tests.integration.graphql_api.classes.queries_test import Request


@pytest.fixture
def mock_query_context():
    request = Request()
    request.headers = {"Time-Zone-Offset": "00:00"}
    mock_context = MagicMock()
    mock_context.user = MagicMock()
    mock_context.user.sub = uuid4()
    mock_context.user.is_authenticated = True
    mock_context.request = request

    return mock_context
