from enum import Enum


from app.graphql_api.enums import OrderDirection


def test_order_direction_enum():
    """Test that OrderDirection enum has the expected values."""
    # Check that OrderDirection is an Enum
    assert issubclass(OrderDirection, Enum)

    # Check enum values
    assert OrderDirection.DESC.value == "-"
    assert OrderDirection.ASC.value == ""

    # Check that it's a strawberry enum - this is added by strawberry.enum decorator
    # but we don't need to check for the specific attribute

    # Check that we can use the enum in comparisons
    assert OrderDirection.DESC != OrderDirection.ASC
    assert OrderDirection.DESC == OrderDirection.DESC

    # Check that we can convert to and from strings
    assert OrderDirection.DESC.name == "DESC"
    assert OrderDirection.ASC.name == "ASC"

    # Check that we can iterate over the enum
    enum_values = list(OrderDirection)
    assert len(enum_values) == 2
    assert OrderDirection.DESC in enum_values
    assert OrderDirection.ASC in enum_values
