import uuid


from app.graphql_api.classes.enums import ClassStatus, ClassTopic
from app.graphql_api.classes.mocks import PRESENTER, UPCOMING_CLASSES


def test_presenter():
    """Test that the PRESENTER mock has the expected structure and values."""
    assert isinstance(PRESENTER, dict)
    assert "id" in PRESENTER
    assert "full_name" in PRESENTER
    assert "first_name" in PRESENTER
    assert "last_name" in PRESENTER
    assert "email" in PRESENTER
    assert "chat_identity" in PRESENTER
    assert "description" in PRESENTER
    assert "avatar_url" in PRESENTER

    # Verify the ID is a valid UUID
    assert uuid.UUID(PRESENTER["id"])

    # Verify other fields have expected values
    assert PRESENTER["full_name"] == "<PERSON>"
    assert PRESENTER["first_name"] == "<PERSON>"
    assert PRESENTER["chat_identity"] == "J. Do<PERSON>"
    assert PRESENTER["description"] == "Health Coach"


def test_upcoming_classes():
    """Test that the UPCOMING_CLASSES mock has the expected structure and values."""
    assert isinstance(UPCOMING_CLASSES, list)
    assert len(UPCOMING_CLASSES) == 5

    # Test the first class
    first_class = UPCOMING_CLASSES[0]
    assert isinstance(first_class, dict)
    assert "id" in first_class
    assert "topic" in first_class
    assert "title" in first_class
    assert "description" in first_class
    assert "date" in first_class
    assert "status" in first_class
    assert "presenter" in first_class

    # Verify the ID is a valid UUID
    assert uuid.UUID(first_class["id"])

    # Verify the topic is a valid ClassTopic enum value
    assert first_class["topic"] == ClassTopic.EDUCATIONAL

    # Verify the status is a valid ClassStatus enum value
    assert first_class["status"] == ClassStatus.FULLY_BOOKED

    # Verify the presenter is the same as the PRESENTER mock
    assert first_class["presenter"] == PRESENTER

    # Test that the dates are in the expected order
    # Since pendulum.now() is used, we can't test exact time differences
    # but we can test that they are in ascending order
    for i in range(1, len(UPCOMING_CLASSES)):
        assert UPCOMING_CLASSES[i]["date"] > UPCOMING_CLASSES[i - 1]["date"]

    # Test that some classes have join links
    assert "join_link" in UPCOMING_CLASSES[1]
    assert "join_link" in UPCOMING_CLASSES[2]
    assert UPCOMING_CLASSES[1]["join_link"] == "https://fakeimg.pl/600x400"

    # Test that the classes have different topics
    topics = [class_data["topic"] for class_data in UPCOMING_CLASSES]
    assert ClassTopic.EDUCATIONAL in topics
    assert ClassTopic.HEALTH_AND_WELLNESS in topics
    assert ClassTopic.MENTAL_HEALTH in topics
    assert ClassTopic.FOOD in topics
    assert ClassTopic.INTRO_SESSION in topics
