from unittest.mock import MagicMock, patch, AsyncMock
from uuid import uuid4

import pendulum
import pytest
from tortoise.queryset import QuerySet, Q

from app.graphql_api.classes.queries.bookings_by_participant import (
    get_requested_page,
    map_response,
    get_one_hour_ago,
    handle_booking_status,
    build_base_query,
    get_bookings,
)
from app.graphql_api.classes.types import (
    BookingFilter,
    ClassStatus,
    ClassElement,
)
from app.graphql_api.pagination import Connection, PageInfo
from ciba_participant.classes.models import (
    Booking,
    LiveSession,
    BookingStatusEnum,
    Webinar,
    TopicEnum,
    TimeOfDayEnum,
)


@pytest.mark.asyncio
async def test_get_requested_page():
    """Test getting the requested page of bookings."""
    # Arrange
    mock_booking = MagicMock(spec=Booking)
    mock_query = MagicMock(spec=QuerySet)
    mock_connection = Connection(
        items=[mock_booking],
        page_info=PageInfo(
            has_next_page=False,
            has_previous_page=False,
            current_page=1,
            per_page=10,
            last_page=1,
            total=1,
        ),
    )

    # Act
    with patch(
        "app.graphql_api.classes.queries.bookings_by_participant.paginate",
        AsyncMock(return_value=mock_connection),
    ) as mock_paginate:
        result = await get_requested_page(mock_query, page=2, per_page=15)

    # Assert
    assert mock_paginate.call_count == 1
    assert mock_paginate.call_args[0][0] == mock_query
    assert mock_paginate.call_args[1]["prefetch_related"] == [
        "live_session",
        "live_session__bookings",
        "live_session__bookings__participant",
        "live_session__webinar",
        "live_session__webinar__host",
    ]
    assert mock_paginate.call_args[1]["params"].page == 2
    assert mock_paginate.call_args[1]["params"].per_page == 15
    assert result == mock_connection


@pytest.mark.parametrize(
    "status, is_in_past, available_to_join, expected_status",
    [
        (BookingStatusEnum.BOOKED, False, True, ClassStatus.AVAILABLE_TO_JOIN),
        (
            BookingStatusEnum.ATTENDED,
            False,
            True,
            ClassStatus.AVAILABLE_TO_JOIN,
        ),
        (BookingStatusEnum.ATTENDED, True, False, ClassStatus.PAST),
        (BookingStatusEnum.WATCHED_RECORDING, True, False, ClassStatus.PAST),
        (
            BookingStatusEnum.BOOKED,
            False,
            False,
            None,
        ),  # Will use get_class_status
    ],
)
def test_map_response(status, is_in_past, available_to_join, expected_status):
    """Test mapping a live session to a class element response."""
    # Arrange
    participant_id = uuid4()

    mock_host = MagicMock()
    mock_host.id = uuid4()
    mock_host.email = "<EMAIL>"
    mock_host.first_name = "John"
    mock_host.last_name = "Doe"
    mock_host.full_name.return_value = "John Doe"
    mock_host.chat_identity = "john_doe"

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.host = mock_host
    mock_webinar.topic = TopicEnum.EDUCATIONAL
    mock_webinar.duration = 60

    mock_live_session = MagicMock(spec=LiveSession)
    mock_live_session.id = uuid4()
    mock_live_session.webinar = mock_webinar
    mock_live_session.title = "Test Session"
    mock_live_session.description = "Test Description"
    mock_live_session.meeting_start_time = pendulum.now()
    mock_live_session.zoom_link = "https://zoom.us/test"
    mock_live_session.recording_url = "https://zoom.us/recording/test"

    # Act
    with (
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.check_join_availability",
            return_value=available_to_join,
        ),
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.pendulum.instance",
            return_value=MagicMock(
                add=MagicMock(
                    return_value=pendulum.now().subtract(
                        hours=1 if is_in_past else -1
                    )
                )
            ),
        ),
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.get_class_status",
            return_value=ClassStatus.BOOKED,
        ) as mock_get_class_status,
    ):
        result = map_response(mock_live_session, participant_id, status)

    # Assert
    assert isinstance(result, ClassElement)
    assert result.id == mock_live_session.id
    assert result.topic == mock_webinar.topic
    assert result.title == mock_live_session.title
    assert result.description == mock_live_session.description
    assert result.started_at == mock_live_session.meeting_start_time
    assert result.join_link == mock_live_session.zoom_link
    assert result.recording_link == mock_live_session.recording_url

    if expected_status:
        assert result.status == expected_status
    else:
        assert result.status == ClassStatus.BOOKED
        mock_get_class_status.assert_called_once_with(
            mock_live_session, participant_id
        )


def test_get_one_hour_ago():
    """Test getting the time one hour ago."""
    # Arrange
    now = pendulum.now()

    # Act
    with patch("pendulum.now", return_value=now):
        result = get_one_hour_ago()

    # Assert
    assert result == now.subtract(hours=1)


@pytest.mark.parametrize(
    "booking_status, expected_query_count",
    [
        ([BookingStatusEnum.BOOKED], 1),
        ([BookingStatusEnum.ATTENDED], 1),
        ([BookingStatusEnum.CANCELED], 1),
        ([BookingStatusEnum.BOOKED, BookingStatusEnum.ATTENDED], 2),
        ([BookingStatusEnum.BOOKED, BookingStatusEnum.CANCELED], 2),
    ],
)
def test_handle_booking_status(booking_status, expected_query_count):
    """Test handling different booking status combinations."""
    # Arrange
    mock_query = MagicMock(spec=QuerySet)
    mock_query.filter.return_value = mock_query
    one_hour_ago = pendulum.now().subtract(hours=1)

    # Act
    with patch(
        "app.graphql_api.classes.queries.bookings_by_participant.get_one_hour_ago",
        return_value=one_hour_ago,
    ):
        result = handle_booking_status(mock_query, booking_status)

    # Assert
    assert mock_query.filter.call_count == 1
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_no_filters():
    """Test building a base query with no filters."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query

    # Act
    with patch.object(
        Booking, "filter", return_value=mock_query
    ) as mock_filter:
        result = await build_base_query(participant_id, timezone_str)

    # Assert
    mock_filter.assert_called_once_with(participant_id=participant_id)
    mock_query.prefetch_related.assert_called_once_with(
        "live_session__webinar"
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_search_filter():
    """Test building a base query with search filter."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"
    search_term = "test"

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    filters = BookingFilter(search=search_term)

    # Act
    with patch.object(Booking, "filter", return_value=mock_query):
        result = await build_base_query(participant_id, timezone_str, filters)

    # Assert
    mock_query.filter.assert_called_with(
        Q(live_session__title__icontains=search_term)
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_topic_filter():
    """Test building a base query with topic filter."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"
    topic = TopicEnum.EDUCATIONAL

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    filters = BookingFilter(topic=topic)

    # Act
    with patch.object(Booking, "filter", return_value=mock_query):
        result = await build_base_query(participant_id, timezone_str, filters)

    # Assert
    mock_query.filter.assert_called_with(live_session__webinar__topic=topic)
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_health_coach_filter():
    """Test building a base query with health coach filter."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"
    health_coach_id = uuid4()

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    filters = BookingFilter(health_coach=health_coach_id)

    # Act
    with patch.object(Booking, "filter", return_value=mock_query):
        result = await build_base_query(participant_id, timezone_str, filters)

    # Assert
    mock_query.filter.assert_called_with(
        live_session__webinar__host_id=health_coach_id
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_recording_filter():
    """Test building a base query with recording filter."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    filters = BookingFilter(with_recording=True)

    # Act
    with patch.object(Booking, "filter", return_value=mock_query):
        result = await build_base_query(participant_id, timezone_str, filters)

    # Assert
    mock_query.filter.assert_called_with(
        live_session__recording_url__isnull=False
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_booking_status_filter():
    """Test building a base query with booking status filter."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"
    booking_status = [BookingStatusEnum.BOOKED]

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    filters = BookingFilter(booking_status=booking_status)

    # Act
    with (
        patch.object(Booking, "filter", return_value=mock_query),
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.handle_booking_status",
            return_value=mock_query,
        ) as mock_handle_booking_status,
    ):
        result = await build_base_query(participant_id, timezone_str, filters)

    # Assert
    mock_handle_booking_status.assert_called_once_with(
        mock_query, booking_status
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_invalid_booking_status():
    """Test building a base query with invalid booking status."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query

    # Use a non-list booking status to trigger the exception
    filters = BookingFilter()
    filters.booking_status = BookingStatusEnum.BOOKED  # Not a list

    # Act & Assert
    with (
        patch.object(Booking, "filter", return_value=mock_query),
        pytest.raises(
            Exception,
            match="Invalid booking status. Expected type: list\\[BookingStatusEnum\\]",
        ),
    ):
        await build_base_query(participant_id, timezone_str, filters)


@pytest.mark.asyncio
async def test_build_base_query_with_date_filters():
    """Test building a base query with date filters."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"
    start_date = pendulum.now()
    end_date = pendulum.now().add(days=7)

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    filters = BookingFilter(start_date=start_date, end_date=end_date)

    # Act
    with patch.object(Booking, "filter", return_value=mock_query):
        result = await build_base_query(participant_id, timezone_str, filters)

    # Assert
    assert mock_query.filter.call_count >= 2
    # Check that both date filters were applied
    mock_query.filter.assert_any_call(
        live_session__meeting_start_time__gte=start_date,
    )
    mock_query.filter.assert_any_call(
        live_session__meeting_start_time__lte=end_date,
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_time_of_day_filter():
    """Test building a base query with time of day filter."""
    # Arrange
    participant_id = uuid4()
    timezone_str = "America/New_York"
    time_of_day = TimeOfDayEnum.MORNING

    mock_query = MagicMock(spec=QuerySet)
    mock_query.prefetch_related.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    mock_session = MagicMock(spec=LiveSession)
    mock_session.id = uuid4()

    filters = BookingFilter(time_of_day=time_of_day)

    # Act
    with (
        patch.object(Booking, "filter", return_value=mock_query),
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.get_sessions_by_time_of_day",
            AsyncMock(return_value=[mock_session]),
        ) as mock_get_sessions,
    ):
        result = await build_base_query(participant_id, timezone_str, filters)

    # Assert
    mock_get_sessions.assert_called_once_with(
        timezone_str=timezone_str, time_of_day=time_of_day
    )
    mock_query.filter.assert_called_with(
        live_session__id__in=[mock_session.id]
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_get_bookings():
    """Test getting bookings for a participant."""
    # Arrange
    participant_id = uuid4()
    timezone = "America/New_York"
    page = 2
    per_page = 15
    filters = BookingFilter(search="test")

    mock_info = MagicMock()
    mock_info.context.request.headers = {"Time-Zone-Offset": "+05:00"}

    mock_booking = MagicMock(spec=Booking)
    mock_booking.live_session = MagicMock(spec=LiveSession)
    mock_booking.status = BookingStatusEnum.BOOKED

    mock_query = MagicMock(spec=QuerySet)

    mock_connection = Connection(
        items=[mock_booking],
        page_info=PageInfo(
            has_next_page=False,
            has_previous_page=False,
            current_page=1,
            per_page=10,
            last_page=1,
            total=1,
        ),
    )

    mock_class_element = MagicMock(spec=ClassElement)

    # Act
    with (
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.get_timezone_name",
            return_value=timezone,
        ) as mock_get_timezone,
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.build_base_query",
            AsyncMock(return_value=mock_query),
        ) as mock_build_query,
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.get_requested_page",
            AsyncMock(return_value=mock_connection),
        ) as mock_get_page,
        patch(
            "app.graphql_api.classes.queries.bookings_by_participant.map_response",
            return_value=mock_class_element,
        ) as mock_map,
    ):
        result = await get_bookings(
            mock_info, participant_id, page, per_page, filters
        )

    # Assert
    mock_get_timezone.assert_called_once_with(mock_info)
    mock_build_query.assert_called_once_with(
        participant_id=participant_id, timezone_str=timezone, filters=filters
    )
    mock_get_page.assert_called_once_with(mock_query, page, per_page)
    mock_map.assert_called_once_with(
        mock_booking.live_session, participant_id, mock_booking.status
    )

    assert isinstance(result, Connection)
    assert result.items == [mock_class_element]
    assert result.page_info == mock_connection.page_info
