from unittest.mock import MagicMock, patch, AsyncMock
from uuid import uuid4

import pendulum
import pytest

from app.graphql_api.classes.queries.intro_sessions import get_intro_sessions
from app.graphql_api.classes.types import ClassElement
from app.graphql_api.classes.enums import ClassStatus, ClassTopic
from ciba_participant.classes.models import Webinar
from ciba_participant.cohort.models import Cohort


@pytest.mark.asyncio
async def test_get_intro_sessions_cohort_not_found():
    """Test getting intro sessions when cohort is not found."""
    # Arrange
    cohort_id = uuid4()

    # Act & Assert
    with (
        patch.object(Cohort, "get_or_none", AsyncMock(return_value=None)),
        pytest.raises(Exception, match="Cohort not found"),
    ):
        await get_intro_sessions(cohort_id)


@pytest.mark.asyncio
async def test_get_intro_sessions_no_webinars():
    """Test getting intro sessions when no webinars are found."""
    # Arrange
    cohort_id = uuid4()
    cohort_start = pendulum.now().subtract(days=2)

    mock_cohort = MagicMock(spec=Cohort)
    mock_cohort.started_at = cohort_start

    # Act
    with (
        patch.object(
            Cohort, "get_or_none", AsyncMock(return_value=mock_cohort)
        ),
        patch.object(
            Webinar,
            "filter",
            return_value=MagicMock(
                prefetch_related=AsyncMock(return_value=[])
            ),
        ),
    ):
        result = await get_intro_sessions(cohort_id)

    # Assert
    assert isinstance(result, list)
    assert len(result) == 0


@pytest.mark.asyncio
async def test_get_intro_sessions_with_sessions_in_range():
    """Test getting intro sessions with sessions in the valid date range."""
    # Arrange
    cohort_id = uuid4()
    cohort_start = pendulum.now().subtract(days=2)

    mock_cohort = MagicMock(spec=Cohort)
    mock_cohort.started_at = cohort_start

    # Create a session within the valid range (between cohort start and a week after)
    session_in_range = MagicMock()
    session_in_range.id = uuid4()
    session_in_range.meeting_start_time = cohort_start.add(days=3)

    # Create a session outside the valid range (before cohort start)
    session_before = MagicMock()
    session_before.id = uuid4()
    session_before.meeting_start_time = cohort_start.subtract(days=1)

    # Create a session outside the valid range (after a week from cohort start)
    session_after = MagicMock()
    session_after.id = uuid4()
    session_after.meeting_start_time = cohort_start.add(days=8)

    mock_host = MagicMock()
    mock_host.id = uuid4()
    mock_host.full_name.return_value = "John Doe"
    mock_host.first_name = "John"
    mock_host.last_name = "Doe"
    mock_host.email = "<EMAIL>"
    mock_host.chat_identity = "john_doe"

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.title = "Intro Session"
    mock_webinar.description = "Introduction to the program"
    mock_webinar.host = mock_host
    mock_webinar.sessions = [session_in_range, session_before, session_after]

    # Act
    with (
        patch.object(
            Cohort, "get_or_none", AsyncMock(return_value=mock_cohort)
        ),
        patch.object(
            Webinar,
            "filter",
            return_value=MagicMock(
                prefetch_related=AsyncMock(return_value=[mock_webinar])
            ),
        ),
    ):
        result = await get_intro_sessions(cohort_id)

    # Assert
    assert isinstance(result, list)
    assert len(result) == 1

    intro_session = result[0]
    assert isinstance(intro_session, ClassElement)
    assert intro_session.id == session_in_range.id
    assert intro_session.topic == ClassTopic.INTRO_SESSION
    assert intro_session.title == mock_webinar.title
    assert intro_session.description == mock_webinar.description
    assert intro_session.started_at == session_in_range.meeting_start_time
    assert intro_session.status == ClassStatus.AVAILABLE_TO_BOOK
    assert intro_session.presenter.id == mock_host.id
    assert intro_session.presenter.full_name == mock_host.full_name()
    assert intro_session.presenter.first_name == mock_host.first_name
    assert intro_session.presenter.last_name == mock_host.last_name
    assert intro_session.presenter.email == mock_host.email
    assert intro_session.presenter.chat_identity == mock_host.chat_identity


@pytest.mark.asyncio
async def test_get_intro_sessions_multiple_webinars():
    """Test getting intro sessions from multiple webinars."""
    # Arrange
    cohort_id = uuid4()
    cohort_start = pendulum.now().subtract(days=2)

    mock_cohort = MagicMock(spec=Cohort)
    mock_cohort.started_at = cohort_start

    # Create sessions for the first webinar
    session1 = MagicMock()
    session1.id = uuid4()
    session1.meeting_start_time = cohort_start.add(days=1)

    session2 = MagicMock()
    session2.id = uuid4()
    session2.meeting_start_time = cohort_start.add(days=2)

    # Create sessions for the second webinar
    session3 = MagicMock()
    session3.id = uuid4()
    session3.meeting_start_time = cohort_start.add(days=3)

    mock_host1 = MagicMock()
    mock_host1.id = uuid4()
    mock_host1.full_name.return_value = "John Doe"
    mock_host1.first_name = "John"
    mock_host1.last_name = "Doe"
    mock_host1.email = "<EMAIL>"
    mock_host1.chat_identity = "john_doe"

    mock_host2 = MagicMock()
    mock_host2.id = uuid4()
    mock_host2.full_name.return_value = "Jane Smith"
    mock_host2.first_name = "Jane"
    mock_host2.last_name = "Smith"
    mock_host2.email = "<EMAIL>"
    mock_host2.chat_identity = "jane_smith"

    mock_webinar1 = MagicMock(spec=Webinar)
    mock_webinar1.title = "Intro Session 1"
    mock_webinar1.description = "Introduction to the program 1"
    mock_webinar1.host = mock_host1
    mock_webinar1.sessions = [session1, session2]

    mock_webinar2 = MagicMock(spec=Webinar)
    mock_webinar2.title = "Intro Session 2"
    mock_webinar2.description = "Introduction to the program 2"
    mock_webinar2.host = mock_host2
    mock_webinar2.sessions = [session3]

    # Act
    with (
        patch.object(
            Cohort, "get_or_none", AsyncMock(return_value=mock_cohort)
        ),
        patch.object(
            Webinar,
            "filter",
            return_value=MagicMock(
                prefetch_related=AsyncMock(
                    return_value=[mock_webinar1, mock_webinar2]
                )
            ),
        ),
    ):
        result = await get_intro_sessions(cohort_id)

    # Assert
    assert isinstance(result, list)
    assert len(result) == 3

    # Check that all sessions are included
    session_ids = [session.id for session in result]
    assert session1.id in session_ids
    assert session2.id in session_ids
    assert session3.id in session_ids

    # Check that the sessions have the correct webinar data
    for session in result:
        if session.id == session1.id or session.id == session2.id:
            assert session.title == mock_webinar1.title
            assert session.description == mock_webinar1.description
            assert session.presenter.id == mock_host1.id
        elif session.id == session3.id:
            assert session.title == mock_webinar2.title
            assert session.description == mock_webinar2.description
            assert session.presenter.id == mock_host2.id
