from unittest.mock import MagicMock, patch, AsyncMock
from uuid import uuid4

import pendulum
import pytest
from tortoise.queryset import Q, QuerySet

from app.graphql_api.classes.queries.upcoming_classes import (
    get_dates_query_filter,
    get_start_date,
    build_base_query,
    get_requested_page,
    map_response,
    get_classes,
)
from app.graphql_api.classes.types import (
    ClassFilter,
    ClassElement,
    ClassStatus,
)
from app.graphql_api.pagination import Connection, PageInfo
from ciba_participant.classes.models import (
    LiveSession,
    Booking,
    TopicEnum,
    TimeOfDayEnum,
)
from ciba_participant.cohort.models import (
    Cohort,
    CohortMembers,
)


def test_get_dates_query_filter():
    """Test getting date query filter."""
    # Arrange
    start_date = pendulum.now()
    end_date = pendulum.now().add(days=7)

    # Act
    result = get_dates_query_filter(start_date, end_date)

    # Assert
    assert result == {
        "meeting_start_time__gte": start_date,
        "meeting_start_time__lte": end_date,
    }


@pytest.mark.parametrize(
    "filter_start_date, cohort_start, expected",
    [
        (None, pendulum.parse("2023-01-01"), pendulum.now()),
        (
            pendulum.parse("2023-02-01"),
            pendulum.parse("2023-01-01"),
            pendulum.parse("2023-02-01"),
        ),
        (
            pendulum.parse("2023-01-01"),
            pendulum.parse("2023-02-01"),
            pendulum.parse("2023-02-01"),
        ),
    ],
)
def test_get_start_date(filter_start_date, cohort_start, expected):
    """Test getting the start date based on filters and cohort start."""
    # Arrange
    filters = None
    if filter_start_date:
        filters = ClassFilter(start_date=filter_start_date)

    # Act
    with patch("pendulum.now", return_value=pendulum.now()):
        result = get_start_date(filters, cohort_start)

    # Assert
    if filter_start_date is None:
        # If no filter start date, we expect pendulum.now()
        assert result.day == expected.day
        assert result.month == expected.month
        assert result.year == expected.year
    else:
        assert result == expected


@pytest.mark.asyncio
async def test_build_base_query_no_cohort_member():
    """Test building a base query when no cohort member is found."""
    # Arrange
    timezone_str = "America/New_York"
    participant_id = uuid4()

    mock_query = MagicMock(spec=QuerySet)

    # Act
    with (
        patch.object(
            CohortMembers,
            "filter",
            return_value=MagicMock(
                prefetch_related=MagicMock(
                    return_value=MagicMock(first=AsyncMock(return_value=None))
                )
            ),
        ),
        patch.object(
            LiveSession, "filter", return_value=mock_query
        ) as mock_filter,
    ):
        result = await build_base_query(timezone_str, participant_id)

    # Assert
    mock_filter.assert_called_once_with(id__isnull=True)
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_cohort_no_intro_booked():
    """Test building a base query with cohort but no intro session booked."""
    # Arrange
    timezone_str = "America/New_York"
    participant_id = uuid4()
    cohort_start = pendulum.now().subtract(days=3)

    mock_cohort = MagicMock(spec=Cohort)
    mock_cohort.started_at = cohort_start

    # Don't use spec=CohortMembers as it tries to access the database
    mock_cohort_member = MagicMock()
    mock_cohort_member.cohort = mock_cohort

    mock_query = MagicMock(spec=QuerySet)
    mock_query.filter.return_value = mock_query

    # Act
    with (
        patch.object(
            CohortMembers,
            "filter",
            return_value=MagicMock(
                prefetch_related=MagicMock(
                    return_value=MagicMock(
                        first=AsyncMock(return_value=mock_cohort_member)
                    )
                )
            ),
        ),
        patch.object(LiveSession, "filter", return_value=mock_query),
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
            return_value={"date_filter": "value"},
        ) as mock_get_dates,
    ):
        result = await build_base_query(timezone_str, participant_id)

    # Assert
    # Check that we're filtering for intro sessions within the first week
    assert mock_get_dates.call_count == 1
    # Check that the first argument is within a reasonable range of cohort_start
    # Allow for a 3-day difference to account for test timing
    assert (
        abs(
            (
                mock_get_dates.call_args[0][0]
                - pendulum.instance(cohort_start)
            ).in_days()
        )
        <= 3
    )
    # Check that the second argument is within a reasonable range of cohort_start + 1 week
    # Allow for a 3-day difference to account for test timing
    assert (
        abs(
            (
                mock_get_dates.call_args[0][1]
                - pendulum.instance(cohort_start).add(weeks=1)
            ).in_days()
        )
        <= 3
    )
    mock_query.filter.assert_called_with(date_filter="value")
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_cohort_and_intro_booked():
    """Test building a base query with cohort and intro session booked."""
    # Arrange
    timezone_str = "America/New_York"
    participant_id = uuid4()
    cohort_start = pendulum.now().subtract(days=3)

    mock_cohort = MagicMock(spec=Cohort)
    mock_cohort.started_at = cohort_start

    # Don't use spec=CohortMembers as it tries to access the database
    mock_cohort_member = MagicMock()
    mock_cohort_member.cohort = mock_cohort

    mock_query = MagicMock(spec=QuerySet)
    mock_query.filter.return_value = mock_query

    # Act
    with (
        patch.object(
            CohortMembers,
            "filter",
            return_value=MagicMock(
                prefetch_related=MagicMock(
                    return_value=MagicMock(
                        first=AsyncMock(return_value=mock_cohort_member)
                    )
                )
            ),
        ),
        patch.object(LiveSession, "filter", return_value=mock_query),
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
            return_value={"date_filter": "value"},
        ) as mock_get_dates,
    ):
        result = await build_base_query(
            timezone_str, participant_id, has_booked_intro=True
        )

    # Assert
    # Check that we're filtering for non-intro sessions within 30 days
    mock_get_dates.assert_called_once()
    # The end date should be approximately 30 days from now
    days_diff = (mock_get_dates.call_args[0][1] - pendulum.now()).days
    assert 29 <= days_diff <= 30  # Allow for small timing differences
    mock_query.filter.assert_called_with(date_filter="value")
    assert result == mock_query


@pytest.mark.asyncio
async def test_build_base_query_with_filters():
    """Test building a base query with various filters."""
    # Arrange
    timezone_str = "America/New_York"
    participant_id = uuid4()
    cohort_start = pendulum.now().subtract(days=3)

    mock_cohort = MagicMock(spec=Cohort)
    mock_cohort.started_at = cohort_start

    # Don't use spec=CohortMembers as it tries to access the database
    mock_cohort_member = MagicMock()
    mock_cohort_member.cohort = mock_cohort

    mock_query = MagicMock(spec=QuerySet)
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query

    mock_session = MagicMock(spec=LiveSession)
    mock_session.id = uuid4()

    filters = ClassFilter(
        topic=TopicEnum.EDUCATIONAL,
        time_of_day=TimeOfDayEnum.MORNING,
        health_coach=uuid4(),
        search="test",
    )

    # Act
    with (
        patch.object(
            CohortMembers,
            "filter",
            return_value=MagicMock(
                prefetch_related=MagicMock(
                    return_value=MagicMock(
                        first=AsyncMock(return_value=mock_cohort_member)
                    )
                )
            ),
        ),
        patch.object(LiveSession, "filter", return_value=mock_query),
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
            return_value={"date_filter": "value"},
        ),
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_sessions_by_time_of_day",
            AsyncMock(return_value=[mock_session]),
        ) as mock_get_sessions,
    ):
        result = await build_base_query(
            timezone_str,
            participant_id,
            filters=filters,
            has_booked_intro=True,
        )

    # Assert
    mock_query.filter.assert_any_call(webinar__topic=filters.topic)
    mock_query.filter.assert_any_call(id__in=[mock_session.id])
    mock_query.filter.assert_any_call(webinar__host_id=filters.health_coach)
    mock_query.filter.assert_any_call(Q(title__icontains=filters.search))
    mock_query.order_by.assert_called_once_with("meeting_start_time")
    mock_get_sessions.assert_called_once_with(
        timezone_str=timezone_str, time_of_day=filters.time_of_day
    )
    assert result == mock_query


@pytest.mark.asyncio
async def test_get_requested_page():
    """Test getting the requested page of live sessions."""
    # Arrange
    mock_live_session = MagicMock(spec=LiveSession)
    mock_query = MagicMock(spec=QuerySet)
    mock_connection = Connection(
        items=[mock_live_session],
        page_info=PageInfo(
            has_next_page=False,
            has_previous_page=False,
            current_page=1,
            per_page=10,
            last_page=1,
            total=1,
        ),
    )

    # Act
    with patch(
        "app.graphql_api.classes.queries.upcoming_classes.paginate",
        AsyncMock(return_value=mock_connection),
    ) as mock_paginate:
        result = await get_requested_page(mock_query, page=2, per_page=15)

    # Assert
    # Check that paginate was called once
    assert mock_paginate.call_count == 1
    # Check that the first argument is mock_query
    assert mock_paginate.call_args[0][0] == mock_query
    # Check that prefetch_related has the expected values
    assert mock_paginate.call_args[1]["prefetch_related"] == [
        "bookings",
        "webinar",
        "bookings__participant",
        "webinar__host",
    ]
    # Check that params has the expected values
    assert mock_paginate.call_args[1]["params"].page == 2
    assert mock_paginate.call_args[1]["params"].per_page == 15
    assert result == mock_connection


def test_map_response():
    """Test mapping a live session to a class element response."""
    # Arrange
    participant_id = uuid4()

    mock_host = MagicMock()
    mock_host.id = uuid4()
    mock_host.email = "<EMAIL>"
    mock_host.first_name = "John"
    mock_host.last_name = "Doe"
    mock_host.full_name.return_value = "John Doe"
    mock_host.chat_identity = "john_doe"

    mock_webinar = MagicMock()
    mock_webinar.host = mock_host
    mock_webinar.topic = TopicEnum.EDUCATIONAL

    mock_live_session = MagicMock(spec=LiveSession)
    mock_live_session.id = uuid4()
    mock_live_session.webinar = mock_webinar
    mock_live_session.title = "Test Session"
    mock_live_session.description = "Test Description"
    mock_live_session.meeting_start_time = pendulum.now()
    mock_live_session.zoom_link = "https://zoom.us/test"
    mock_live_session.recording_url = "https://zoom.us/recording/test"

    # Act
    with patch(
        "app.graphql_api.classes.queries.upcoming_classes.get_class_status",
        return_value=ClassStatus.AVAILABLE_TO_BOOK,
    ) as mock_get_class_status:
        result = map_response(mock_live_session, participant_id)

    # Assert
    assert isinstance(result, ClassElement)
    assert result.id == mock_live_session.id
    assert result.topic == mock_webinar.topic
    assert result.title == mock_live_session.title
    assert result.description == mock_live_session.description
    assert result.started_at == mock_live_session.meeting_start_time
    assert result.join_link == mock_live_session.zoom_link
    assert result.recording_link == mock_live_session.recording_url
    assert result.status == ClassStatus.AVAILABLE_TO_BOOK
    mock_get_class_status.assert_called_once_with(
        mock_live_session, participant_id
    )


@pytest.mark.asyncio
async def test_get_classes_with_intro_booking():
    """Test getting classes when participant has an intro booking."""
    # Arrange
    participant_id = uuid4()
    page = 2
    per_page = 15
    filters = ClassFilter(search="test")

    mock_info = MagicMock()
    mock_info.context.request.headers = {"Time-Zone-Offset": "+05:00"}

    mock_booking = MagicMock(spec=Booking)

    mock_live_session = MagicMock(spec=LiveSession)

    mock_query = MagicMock(spec=QuerySet)

    mock_connection = Connection(
        items=[mock_live_session],
        page_info=PageInfo(
            has_next_page=False,
            has_previous_page=False,
            current_page=1,
            per_page=10,
            last_page=1,
            total=1,
        ),
    )

    mock_class_element = MagicMock(spec=ClassElement)
    mock_class_element.status = ClassStatus.AVAILABLE_TO_BOOK

    # Act
    with (
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(
                filter=MagicMock(
                    return_value=MagicMock(
                        first=AsyncMock(return_value=mock_booking)
                    )
                )
            ),
        ),
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_timezone_name",
            return_value="America/New_York",
        ) as mock_get_timezone,
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.build_base_query",
            AsyncMock(return_value=mock_query),
        ) as mock_build_query,
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_requested_page",
            AsyncMock(return_value=mock_connection),
        ) as mock_get_page,
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.map_response",
            return_value=mock_class_element,
        ) as mock_map,
    ):
        result = await get_classes(
            mock_info, participant_id, page, per_page, filters
        )

    # Assert
    mock_get_timezone.assert_called_once_with(mock_info)
    mock_build_query.assert_called_once_with(
        "America/New_York", participant_id, filters, True
    )
    mock_get_page.assert_called_once_with(mock_query, page, per_page)
    mock_map.assert_called_once_with(mock_live_session, participant_id)

    assert isinstance(result, Connection)
    assert result.items == [mock_class_element]
    assert result.page_info == mock_connection.page_info


@pytest.mark.asyncio
async def test_get_classes_no_intro_booking_no_results_retry():
    """Test getting classes when no intro booking and no results, then retry with ignore_time_restriction."""
    # Arrange
    participant_id = uuid4()
    page = 2
    per_page = 15
    filters = ClassFilter(search="test")

    mock_info = MagicMock()
    mock_info.context.request.headers = {"Time-Zone-Offset": "+05:00"}

    mock_live_session = MagicMock(spec=LiveSession)

    mock_query = MagicMock(spec=QuerySet)

    # First call returns empty items
    mock_connection_empty = Connection(
        items=[],
        page_info=PageInfo(
            has_next_page=False,
            has_previous_page=False,
            current_page=1,
            per_page=10,
            last_page=1,
            total=1,
        ),
    )

    # Second call returns items
    mock_connection_with_items = Connection(
        items=[mock_live_session],
        page_info=PageInfo(
            has_next_page=False,
            has_previous_page=False,
            current_page=1,
            per_page=10,
            last_page=1,
            total=1,
        ),
    )

    mock_class_element = MagicMock(spec=ClassElement)
    mock_class_element.status = ClassStatus.AVAILABLE_TO_BOOK

    # Act
    with (
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(
                filter=MagicMock(
                    return_value=MagicMock(first=AsyncMock(return_value=None))
                )
            ),
        ),
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_timezone_name",
            return_value="America/New_York",
        ),
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.build_base_query",
            AsyncMock(side_effect=[mock_query, mock_query]),
        ) as mock_build_query,
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_requested_page",
            AsyncMock(
                side_effect=[mock_connection_empty, mock_connection_with_items]
            ),
        ) as mock_get_page,
        patch(
            "app.graphql_api.classes.queries.upcoming_classes.map_response",
            return_value=mock_class_element,
        ),
    ):
        result = await get_classes(
            mock_info, participant_id, page, per_page, filters
        )

    # Assert
    # First call with has_booked_intro=False
    mock_build_query.assert_any_call(
        "America/New_York", participant_id, filters, False
    )
    # Second call with has_booked_intro=False and ignore_time_restriction=True
    mock_build_query.assert_any_call(
        timezone_str="America/New_York",
        participant_id=participant_id,
        filters=filters,
        has_booked_intro=False,
        ignore_time_restriction=True,
    )

    assert mock_get_page.call_count == 2
    assert isinstance(result, Connection)
    assert len(result.items) == 1
