from unittest.mock import MagicMock, patch, AsyncMock
from uuid import uuid4

import pytest

from app.graphql_api.classes.mutations.update_booking_status import (
    create_booking,
    handle_booked_status,
    update_status_by_participant_id,
    update_booking_status,
)
from app.graphql_api.classes.types import UpdateBookingResponseType
from ciba_participant.classes.models import (
    Booking,
    BookingStatusEnum,
    LiveSession,
    TopicEnum,
    Webinar,
)


@pytest.mark.asyncio
async def test_create_booking():
    """Test creating a new booking."""
    # Arrange
    live_session_id = uuid4()
    participant_id = uuid4()
    status = BookingStatusEnum.BOOKED
    booking_id = uuid4()

    mock_booking = MagicMock()
    mock_booking.id = booking_id

    # Act
    with patch.object(
        Booking, "create", AsyncMock(return_value=mock_booking)
    ) as mock_create:
        result = await create_booking(live_session_id, participant_id, status)

    # Assert
    mock_create.assert_called_once_with(
        live_session_id=live_session_id,
        participant_id=participant_id,
        status=status,
    )
    assert isinstance(result, UpdateBookingResponseType)
    assert result.success is True
    assert result.booking_id == booking_id


@pytest.mark.asyncio
async def test_handle_booked_status_when_already_booked():
    """Test handling booked status when booking is already made."""
    # Arrange
    booking = MagicMock(spec=Booking)
    booking.status = BookingStatusEnum.BOOKED
    booking.save = AsyncMock()

    # Act
    result = await handle_booked_status(booking, BookingStatusEnum.BOOKED)

    # Assert
    booking.save.assert_not_called()
    assert result.success is False
    assert result.error == "Booking was already made"


@pytest.mark.asyncio
async def test_handle_booked_status_when_canceled():
    """Test handling booked status when booking was canceled."""
    # Arrange
    booking = MagicMock(spec=Booking)
    booking.status = BookingStatusEnum.CANCELED
    booking.id = uuid4()
    booking.save = AsyncMock()

    # Act
    result = await handle_booked_status(booking, BookingStatusEnum.BOOKED)

    # Assert
    booking.save.assert_called_once()
    assert result.success is True
    assert result.booking_id == booking.id
    assert booking.status == BookingStatusEnum.BOOKED


@pytest.mark.asyncio
async def test_update_status_by_participant_id_session_not_found():
    """Test updating status when session is not found."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.BOOKED

    # Act
    with patch(
        "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
        new_callable=AsyncMock,
    ) as mock_get:
        mock_get.return_value = None
        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    mock_get.assert_called_once_with(id=live_session_id)
    assert result.success is False
    assert result.error == "Session not found"


@pytest.mark.asyncio
async def test_update_status_by_participant_id_booking_not_found_session_full():
    """Test updating status when booking not found and session is full."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.BOOKED

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.max_capacity = 1

    mock_session = MagicMock(spec=LiveSession)
    mock_session.webinar = mock_webinar
    mock_session.bookings = [MagicMock()]  # One booking already exists

    # Act
    with (
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
            new_callable=AsyncMock,
        ) as mock_get,
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(first=AsyncMock(return_value=None)),
        ),
    ):
        mock_session = MagicMock(spec=LiveSession)
        mock_session.webinar = mock_webinar
        mock_session.bookings = [MagicMock()]  # One booking already exists

        # Set the return value for the mocked get_or_none
        mock_get.return_value = mock_session
        # Mock the fetch_related method
        mock_session.fetch_related = AsyncMock()

        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    assert result.success is False
    assert result.error == "Session is full"


@pytest.mark.asyncio
async def test_update_status_by_participant_id_booking_not_found_create_booking():
    """Test updating status when booking not found and creating a new booking."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.BOOKED
    booking_id = uuid4()

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.max_capacity = 10

    mock_session = MagicMock(spec=LiveSession)
    mock_session.webinar = mock_webinar
    mock_session.bookings = []  # No bookings yet

    mock_booking = MagicMock(spec=Booking)
    mock_booking.id = booking_id

    # Act
    with (
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
            new_callable=AsyncMock,
        ) as mock_get,
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(first=AsyncMock(return_value=None)),
        ),
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.create_booking",
            AsyncMock(
                return_value=UpdateBookingResponseType(
                    success=True, booking_id=booking_id
                )
            ),
        ) as mock_create_booking,
    ):
        mock_session = MagicMock(spec=LiveSession)
        mock_session.webinar = mock_webinar
        mock_session.bookings = []  # No bookings yet

        # Set the return value for the mocked get_or_none
        mock_get.return_value = mock_session
        # Mock the fetch_related method
        mock_session.fetch_related = AsyncMock()

        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    mock_create_booking.assert_called_once_with(
        live_session_id, participant_id, status
    )
    assert result.success is True
    assert result.booking_id == booking_id


@pytest.mark.asyncio
async def test_update_status_by_participant_id_booking_not_found_not_booked():
    """Test updating status when booking not found and status is not BOOKED."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.CANCELED

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.max_capacity = 10

    mock_session = MagicMock(spec=LiveSession)
    mock_session.webinar = mock_webinar
    mock_session.bookings = []  # No bookings yet

    # Act
    with (
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
            new_callable=AsyncMock,
        ) as mock_get,
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(first=AsyncMock(return_value=None)),
        ),
    ):
        mock_session = MagicMock(spec=LiveSession)
        mock_session.webinar = mock_webinar
        mock_session.bookings = []  # No bookings yet

        # Mock the get_or_none method to return a session
        mock_get.return_value = mock_session
        # Mock the fetch_related method
        mock_session.fetch_related = AsyncMock()

        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    assert result.success is False
    assert result.error == "Booking not found"


@pytest.mark.asyncio
async def test_update_status_by_participant_id_booking_found_to_booked():
    """Test updating status when booking found and changing to BOOKED."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.BOOKED
    booking_id = uuid4()

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.max_capacity = 10

    mock_session = MagicMock(spec=LiveSession)
    mock_session.webinar = mock_webinar

    mock_booking = MagicMock(spec=Booking)
    mock_booking.id = booking_id
    mock_booking.status = BookingStatusEnum.CANCELED

    # Act
    with (
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
            new_callable=AsyncMock,
        ) as mock_get,
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(first=AsyncMock(return_value=mock_booking)),
        ),
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.handle_booked_status",
            AsyncMock(
                return_value=UpdateBookingResponseType(
                    success=True, booking_id=booking_id
                )
            ),
        ) as mock_handle_booked,
    ):
        mock_session = MagicMock(spec=LiveSession)
        mock_session.webinar = mock_webinar

        # Mock the get_or_none method to return a session
        mock_get.return_value = mock_session
        # Mock the fetch_related method
        mock_session.fetch_related = AsyncMock()

        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    mock_handle_booked.assert_called_once_with(mock_booking, status)
    assert result.success is True
    assert result.booking_id == booking_id


@pytest.mark.asyncio
async def test_update_status_by_participant_id_booking_found_already_attended():
    """Test updating status when booking found but already attended."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.CANCELED

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.max_capacity = 10

    mock_session = MagicMock(spec=LiveSession)
    mock_session.webinar = mock_webinar

    mock_booking = MagicMock(spec=Booking)
    mock_booking.status = BookingStatusEnum.ATTENDED

    # Act
    with (
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
            new_callable=AsyncMock,
        ) as mock_get,
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(first=AsyncMock(return_value=mock_booking)),
        ),
    ):
        mock_session = MagicMock(spec=LiveSession)
        mock_session.webinar = mock_webinar

        # Mock the get_or_none method to return a session
        mock_get.return_value = mock_session
        # Mock the fetch_related method
        mock_session.fetch_related = AsyncMock()

        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    assert result.success is False
    assert (
        result.error
        == f"Booking was already {mock_booking.status.value.lower()}"
    )


@pytest.mark.asyncio
async def test_update_status_by_participant_id_booking_found_update_status():
    """Test updating status when booking found and updating to a new status."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.CANCELED
    booking_id = uuid4()

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.max_capacity = 10
    mock_webinar.topic = TopicEnum.INTRO_SESSION

    mock_session = MagicMock(spec=LiveSession)
    mock_session.webinar = mock_webinar

    mock_booking = MagicMock(spec=Booking)
    mock_booking.id = booking_id
    mock_booking.status = BookingStatusEnum.BOOKED
    mock_booking.save = AsyncMock()

    # Act
    with (
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
            new_callable=AsyncMock,
        ) as mock_get,
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(first=AsyncMock(return_value=mock_booking)),
        ),
    ):
        mock_session_with_prefetch = MagicMock(spec=LiveSession)
        mock_session_with_prefetch.webinar = mock_webinar

        mock_get.return_value = mock_session_with_prefetch
        mock_session_with_prefetch.fetch_related = AsyncMock()

        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    mock_booking.save.assert_called_once()
    assert mock_booking.status == status
    assert result.success is True
    assert result.booking_id == booking_id


@pytest.mark.asyncio
async def test_update_status_by_participant_id_booking_found_same_status():
    """Test updating status when booking found but status is the same."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.BOOKED
    booking_id = uuid4()

    mock_webinar = MagicMock(spec=Webinar)
    mock_webinar.max_capacity = 10

    mock_session = MagicMock(spec=LiveSession)
    mock_session.webinar = mock_webinar

    mock_booking = MagicMock(spec=Booking)
    mock_booking.id = booking_id
    mock_booking.status = BookingStatusEnum.BOOKED
    mock_booking.save = AsyncMock()

    # Act
    with (
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.LiveSession.get_or_none",
            new_callable=AsyncMock,
        ) as mock_get,
        patch.object(
            Booking,
            "filter",
            return_value=MagicMock(first=AsyncMock(return_value=mock_booking)),
        ),
        patch(
            "app.graphql_api.classes.mutations.update_booking_status.handle_booked_status",
            AsyncMock(
                return_value=UpdateBookingResponseType(
                    success=True, booking_id=booking_id
                )
            ),
        ),
    ):
        mock_session = MagicMock(spec=LiveSession)
        mock_session.webinar = mock_webinar

        # Mock the get_or_none method to return a session
        mock_get.return_value = mock_session
        # Mock the fetch_related method
        mock_session.fetch_related = AsyncMock()

        result = await update_status_by_participant_id(
            participant_id, live_session_id, status
        )

    # Assert
    mock_booking.save.assert_not_called()
    assert result.success is True
    assert result.booking_id == booking_id


@pytest.mark.asyncio
async def test_update_booking_status():
    """Test the update_booking_status resolver function."""
    # Arrange
    participant_id = uuid4()
    live_session_id = uuid4()
    status = BookingStatusEnum.BOOKED
    booking_id = uuid4()

    mock_info = MagicMock()
    mock_info.context.user.sub = participant_id

    # Act
    with patch(
        "app.graphql_api.classes.mutations.update_booking_status.update_status_by_participant_id",
        AsyncMock(
            return_value=UpdateBookingResponseType(
                success=True, booking_id=booking_id
            )
        ),
    ) as mock_update:
        result = await update_booking_status(
            mock_info, live_session_id, status
        )

    # Assert
    mock_update.assert_called_once_with(
        participant_id, live_session_id, status
    )
    assert result.success is True
    assert result.booking_id == booking_id
