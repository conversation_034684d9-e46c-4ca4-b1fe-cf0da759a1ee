from unittest.mock import MagicMock
from uuid import UUID, uuid4

import pendulum
import pytest
from ciba_participant.classes.models import (
    Booking,
    LiveSession,
    Webinar,
    BookingStatusEnum,
)

from app.graphql_api.classes.enums import ClassStatus
from app.graphql_api.classes.helpers import (
    check_join_availability,
    get_class_status,
)

test_today = pendulum.parse("2020-02-15T08:35:00")
participant_id = UUID("7b35a7e3-09bf-46e4-9e7f-bd587a8b12cc")
mock_date_module = "pendulum.now"


@pytest.fixture
def mock_participant_booking():
    test_booking = MagicMock(
        spec=Booking,
        status=BookingStatusEnum.BOOKED,
        participant_id=participant_id,
    )

    return test_booking


@pytest.mark.parametrize(
    "test_date",
    [
        (pendulum.parse("2020-02-15T07:34:59")),
        (pendulum.parse("2020-02-15T09:06:00")),
    ],
)
def test_check_join_availability(test_date, mocker):
    """
    check_join_availability should return False
    when the class date is outside the valid range
    """

    mocker.patch(mock_date_module, return_value=test_today)
    actual_value = check_join_availability(test_date)

    assert actual_value is False


def test_get_class_status_1(mocker):
    """
    get_class_status should return the PAST status
    when the class date plus one hour happened before the current date
    """
    test_live_session = MagicMock(spec=LiveSession)
    test_live_session.meeting_start_time = pendulum.parse(
        "2020-02-15T07:34:59"
    )

    mocker.patch(mock_date_module, return_value=test_today)
    actual_value = get_class_status(test_live_session, participant_id)

    assert actual_value == ClassStatus.PAST


def test_get_class_status_2(mock_participant_booking, mocker):
    """
    get_class_status should return the BOOKED status
    when the class is in the future and the participant has a booking
    """
    test_live_session = MagicMock(spec=LiveSession)
    test_live_session.meeting_start_time = pendulum.parse(
        "2020-02-16T07:35:00"
    )
    test_live_session.bookings = [mock_participant_booking]

    mocker.patch(mock_date_module, return_value=test_today)
    actual_value = get_class_status(test_live_session, participant_id)

    assert actual_value == ClassStatus.BOOKED


def test_get_class_status_3(mock_participant_booking, mocker):
    """
    get_class_status should return the AVAILABLE_TO_JOIN status
    when the class is happening and the participant has a booking
    """
    test_live_session = MagicMock(spec=LiveSession)
    test_live_session.meeting_start_time = pendulum.parse(
        "2020-02-15T07:45:00"
    )
    test_live_session.bookings = [mock_participant_booking]

    mocker.patch(mock_date_module, return_value=test_today)
    actual_value = get_class_status(test_live_session, participant_id)

    assert actual_value == ClassStatus.AVAILABLE_TO_JOIN


@pytest.mark.parametrize("test_capacity", [1, 0])
def test_get_class_status_4(test_capacity, mocker):
    """
    get_class_status should return the FULLY_BOOKED status
    when the class is in the future, the participant has no booking
    and the class is fully booked
    """
    test_webinar = MagicMock(spec=Webinar, max_capacity=test_capacity)
    test_booking = MagicMock(spec=Booking, status=BookingStatusEnum.BOOKED)
    test_booking.participant_id = uuid4()
    test_live_session = MagicMock(spec=LiveSession)
    test_live_session.webinar = test_webinar
    test_live_session.meeting_start_time = pendulum.parse(
        "2020-02-15T10:15:00"
    )
    test_live_session.bookings = [test_booking]

    mocker.patch(mock_date_module, return_value=test_today)
    actual_value = get_class_status(test_live_session, participant_id)

    assert actual_value == ClassStatus.FULLY_BOOKED


def test_get_class_status_5(mocker):
    """
    get_class_status should return the AVAILABLE_TO_BOOK status
    when the class is in the future, the participant has no booking
    and the class has available places
    """
    test_webinar = MagicMock(spec=Webinar, max_capacity=5)
    test_live_session = MagicMock(spec=LiveSession)
    test_live_session.webinar = test_webinar
    test_live_session.meeting_start_time = pendulum.parse(
        "2020-02-16T18:15:00"
    )
    test_live_session.bookings = []

    mocker.patch(mock_date_module, return_value=test_today)
    actual_value = get_class_status(test_live_session, participant_id)

    assert actual_value == ClassStatus.AVAILABLE_TO_BOOK
