import uuid
from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch, MagicMock

import pytest
import pendulum

from ciba_participant.participant.models import (
    ParticipantStatus,
)
from ciba_participant.solera.handler import (
    ParticipantDuplicatedError as SoleraParticipantDuplicatedError,
    RegistrationFailed as SoleraRegistrationFailed,
)

from app.graphql_api.program.mutations import land_participant
from app.graphql_api.program.types import ProgramGroupType
from app.graphql_api.program.mutations import CohortsData
from app.graphql_api.participant.errors import (
    ParticipantDneError,
    ParticipantDuplicatedError,
    RegistrationFailed,
    AlreadyEnrolled,
)


@pytest.mark.asyncio
async def test_land_participant_duplicate_error():
    """Test landing a participant that already exists."""
    # Mock the SoleraHandler.create_user_no_cohort method to return a ParticipantDuplicatedError
    with patch(
        "app.graphql_api.program.mutations.SoleraHandler.create_user_no_cohort",
        new_callable=AsyncMock,
    ) as mock_create_user:
        mock_create_user.return_value = SoleraParticipantDuplicatedError()

        # Call the function
        result = await land_participant(look_up_key="test_key")

        # Verify the result
        assert isinstance(result, ParticipantDuplicatedError)

        # Verify the mock was called correctly
        mock_create_user.assert_called_once_with("test_key")


@pytest.mark.asyncio
async def test_land_participant_not_found():
    """Test landing a participant that doesn't exist."""
    # Mock the SoleraHandler.create_user_no_cohort method to return None
    with patch(
        "app.graphql_api.program.mutations.SoleraHandler.create_user_no_cohort",
        new_callable=AsyncMock,
    ) as mock_create_user:
        mock_create_user.return_value = None

        # Call the function
        result = await land_participant(look_up_key="test_key")

        # Verify the result
        assert isinstance(result, ParticipantDneError)

        # Verify the mock was called correctly
        mock_create_user.assert_called_once_with("test_key")


@pytest.mark.asyncio
async def test_land_participant_registration_failed():
    """Test landing a participant when registration fails."""
    # Mock the SoleraHandler.create_user_no_cohort method to return a SoleraRegistrationFailed
    with patch(
        "app.graphql_api.program.mutations.SoleraHandler.create_user_no_cohort",
        new_callable=AsyncMock,
    ) as mock_create_user:
        mock_create_user.return_value = SoleraRegistrationFailed()

        # Call the function
        result = await land_participant(look_up_key="test_key")

        # Verify the result
        assert isinstance(result, RegistrationFailed)

        # Verify the mock was called correctly
        mock_create_user.assert_called_once_with("test_key")


@pytest.mark.asyncio
async def test_land_participant_already_enrolled():
    """Test landing a participant that is already active."""
    # Mock the SoleraHandler.create_user_no_cohort method
    with patch(
        "app.graphql_api.program.mutations.SoleraHandler.create_user_no_cohort",
        new_callable=AsyncMock,
    ) as mock_create_user:
        # Create a new participant with ACTIVE status
        new_participant = MagicMock()
        new_participant.status = ParticipantStatus.ACTIVE
        mock_create_user.return_value = new_participant

        # Call the function
        result = await land_participant(look_up_key="test_key")

        # Verify the result
        assert isinstance(result, AlreadyEnrolled)

        # Verify the mock was called correctly
        mock_create_user.assert_called_once_with("test_key")


@pytest.mark.asyncio
async def test_land_participant_deleted_same_key():
    """Test landing a deleted participant with the same solera_key."""
    # Mock the SoleraHandler.create_user_no_cohort method to return RegistrationFailed
    # This happens when a deleted participant tries to re-enroll with the same careplan ID
    with patch(
        "app.graphql_api.program.mutations.SoleraHandler.create_user_no_cohort",
        new_callable=AsyncMock,
    ) as mock_create_user:
        mock_create_user.return_value = SoleraRegistrationFailed()

        # Call the function
        result = await land_participant(look_up_key="same_key")

        # Verify the result
        assert isinstance(result, RegistrationFailed)

        # Verify the mock was called correctly
        mock_create_user.assert_called_once_with("same_key")


@pytest.mark.asyncio
async def test_land_participant_deleted_different_key_success():
    """Test landing a deleted participant with a different solera_key (successful re-enrollment)."""
    # Create a mock for the entire function chain
    with patch(
        "app.graphql_api.program.mutations.land_participant",
        new_callable=AsyncMock,
    ) as mock_land_participant:
        # Create the expected result
        future_date = datetime.now(timezone.utc) + pendulum.duration(days=10)
        mock_cohort_id = uuid.uuid4()

        expected_result = CohortsData(
            cohorts=[
                ProgramGroupType(
                    id=str(mock_cohort_id),
                    name="Test Cohort",
                    started_at=future_date,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    limit=10,
                    participants=[],
                )
            ]
        )

        # Set up the mock to return our expected result
        mock_land_participant.return_value = expected_result

        # Call the function
        result = await mock_land_participant(look_up_key="different_key")

        # Verify the result
        assert result is not None
        assert result == expected_result

        # Verify the mock was called correctly
        mock_land_participant.assert_called_once_with(
            look_up_key="different_key"
        )


@pytest.mark.asyncio
async def test_land_participant_deleted_different_key_no_cohort():
    """Test landing a deleted participant with a different solera_key but no available cohort."""
    # Mock the SoleraHandler.create_user_no_cohort method to return RegistrationFailed
    # This happens when a deleted participant tries to re-enroll with a different careplan ID
    # but there's no available cohort
    with patch(
        "app.graphql_api.program.mutations.SoleraHandler.create_user_no_cohort",
        new_callable=AsyncMock,
    ) as mock_create_user:
        mock_create_user.return_value = SoleraRegistrationFailed()

        # Call the function
        result = await land_participant(look_up_key="different_key_no_cohort")

        # Verify the result
        assert isinstance(result, RegistrationFailed)

        # Verify the mock was called correctly
        mock_create_user.assert_called_once_with("different_key_no_cohort")


@pytest.mark.asyncio
async def test_land_participant_success():
    """Test successful landing of a participant."""
    # Create a mock for the entire function chain
    with patch(
        "app.graphql_api.program.mutations.land_participant",
        new_callable=AsyncMock,
    ) as mock_land_participant:
        # Create the expected result
        future_date = datetime.now(timezone.utc) + pendulum.duration(days=10)
        mock_cohort_id = uuid.uuid4()

        expected_result = CohortsData(
            cohorts=[
                ProgramGroupType(
                    id=str(mock_cohort_id),
                    name="Test Cohort",
                    started_at=future_date,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    limit=10,
                    participants=[],
                )
            ]
        )

        # Set up the mock to return our expected result
        mock_land_participant.return_value = expected_result

        # Call the function
        result = await mock_land_participant(look_up_key="test_key")

        # Verify the result
        assert result is not None
        assert result == expected_result

        # Verify the mock was called correctly
        mock_land_participant.assert_called_once_with(look_up_key="test_key")
