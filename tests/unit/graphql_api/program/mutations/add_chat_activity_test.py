from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.graphql_api.program.mutations import add_chat_activity
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ActivityUnit,
)
from ciba_participant.activity.schemas import ParticipantActivityInput

test_participant_id = "a8a6838b-e24d-48eb-9331-598eb274219e"


@pytest.mark.asyncio
async def test_add_chat_activity_success():
    """
    add_chat_activity should return True when activity is created successfully.
    """
    # Mock the ParticipantActivityRepository.create_participant_activity method
    with patch(
        "app.graphql_api.program.mutations.ParticipantActivityRepository.create_participant_activity",
        new_callable=AsyncMock,
    ) as mock_create_activity:
        # Call the function
        result = await add_chat_activity(participant_id=test_participant_id)

        # Verify the result
        assert result is True

        # Verify the mock was called correctly
        mock_create_activity.assert_called_once()

        # Verify the activity input
        activity_input = mock_create_activity.call_args[0][0]
        assert isinstance(activity_input, ParticipantActivityInput)
        # The participant_id is converted to UUID in the function
        assert str(activity_input.participant_id) == test_participant_id
        assert activity_input.activity_type == ParticipantActivityEnum.COACH
        assert (
            activity_input.activity_category
            == ParticipantActivityCategory.ACTIVITY
        )
        assert (
            activity_input.activity_device
            == ParticipantActivityDevice.MANUAL_INPUT
        )
        assert activity_input.unit == ActivityUnit.ACTION
        assert activity_input.value == "1"


@pytest.mark.asyncio
async def test_add_chat_activity_failure():
    """
    add_chat_activity should return False when an exception occurs.
    """
    # Mock the ParticipantActivityRepository.create_participant_activity method to raise an exception
    with (
        patch(
            "app.graphql_api.program.mutations.ParticipantActivityRepository.create_participant_activity",
            new_callable=AsyncMock,
            side_effect=Exception("Test exception"),
        ) as mock_create_activity,
        patch(
            "app.graphql_api.program.mutations.logger.exception",
            new_callable=MagicMock,
        ) as mock_logger,
    ):
        # Call the function
        result = await add_chat_activity(participant_id=test_participant_id)

        # Verify the result
        assert result is False

        # Verify the mock was called correctly
        mock_create_activity.assert_called_once()

        # Verify the logger was called
        mock_logger.assert_called_once()
        # The format string is used in the logger call
        assert "Got exception:" in mock_logger.call_args[0][0]
        assert "Test exception" in str(mock_logger.call_args)
