from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime, timezone

import pytest
import pendulum
from strawberry.types import Info
from tortoise import Tortoise

from app.graphql_api.program.mutations import make_progress
from app.graphql_api.program.inputs import PersonalSuccessInput
from app.graphql_api.program.errors import DoesNotBelongToCourse
from app.graphql_api.program.types import ModuleProgressType
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
)
from ciba_participant.classes.models import (
    LiveSession,
    BookingStatusEnum,
)


# We'll use pytest-asyncio's built-in event_loop fixture


# Setup Tortoise ORM for testing
@pytest.fixture(autouse=True)
async def initialize_tests():
    # Initialize Tortoise with an in-memory SQLite database for testing
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={
            "models": [
                "ciba_participant.activity.models",
                "ciba_participant.program.models",
                "ciba_participant.cohort.models",
                "ciba_participant.classes.models",
            ]
        },
    )
    await Tortoise.generate_schemas()
    yield
    await Tortoise.close_connections()


@pytest.mark.asyncio
async def test_make_progress_no_cohort():
    """Test make_progress with no cohort."""
    # Create mock info, section_id, and program_module_id
    mock_info = MagicMock(spec=Info)
    mock_info.context.user.sub = uuid4()
    section_id = uuid4()
    program_module_id = uuid4()

    # Mock ParticipantActivity.filter().filter().first()
    with patch(
        "app.graphql_api.program.mutations.ParticipantActivity",
        new_callable=MagicMock,
    ) as mock_activity_class:
        mock_activity_instance = MagicMock()
        mock_activity_instance.filter = MagicMock()
        mock_activity_instance.filter.return_value = MagicMock()
        mock_activity_instance.filter.return_value.filter = MagicMock()
        mock_activity_instance.filter.return_value.filter.return_value = (
            MagicMock()
        )
        mock_activity_instance.filter.return_value.filter.return_value.first = AsyncMock(
            return_value=None
        )
        mock_activity_class.return_value = mock_activity_instance

        # Mock ProgramModuleSection.filter().get_or_none()
        with patch(
            "app.graphql_api.program.mutations.ProgramModuleSection",
            new_callable=MagicMock,
        ) as mock_section_class:
            mock_section_instance = MagicMock()
            mock_section_instance.filter = MagicMock()
            mock_section_instance.filter.return_value = MagicMock()
            mock_section_instance.filter.return_value.get_or_none = AsyncMock(
                return_value=MagicMock()
            )
            mock_section_class.return_value = mock_section_instance

            # Mock CohortMembers.filter().prefetch_related().get_or_none() to return None
            with patch(
                "app.graphql_api.program.mutations.CohortMembers",
                new_callable=MagicMock,
            ) as mock_cohort_members_class:
                mock_cohort_members_instance = MagicMock()
                mock_cohort_members_instance.filter = MagicMock()
                mock_cohort_members_instance.filter.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value.get_or_none = AsyncMock(
                    return_value=None
                )
                mock_cohort_members_class.return_value = (
                    mock_cohort_members_instance
                )

                # Call the function
                result = await make_progress(
                    mock_info, section_id, program_module_id
                )

                # Verify results
                assert isinstance(result, DoesNotBelongToCourse)


@pytest.mark.asyncio
async def test_make_progress_live_session():
    """Test make_progress with live session."""
    # Create mock info, section_id, and program_module_id
    mock_info = MagicMock(spec=Info)
    mock_info.context.user.sub = uuid4()
    section_id = uuid4()
    program_module_id = uuid4()

    # Mock ParticipantActivity.filter().filter().first()
    with patch(
        "app.graphql_api.program.mutations.ParticipantActivity",
        new_callable=MagicMock,
    ) as mock_activity_class:
        mock_activity_instance = MagicMock()
        mock_activity_instance.filter = MagicMock()
        mock_activity_instance.filter.return_value = MagicMock()
        mock_activity_instance.filter.return_value.filter = MagicMock()
        mock_activity_instance.filter.return_value.filter.return_value = (
            MagicMock()
        )
        mock_activity_instance.filter.return_value.filter.return_value.first = AsyncMock(
            return_value=None
        )
        mock_activity_class.return_value = mock_activity_instance

        # Mock ProgramModuleSection.filter().get_or_none() to return None
        with patch(
            "app.graphql_api.program.mutations.ProgramModuleSection",
            new_callable=MagicMock,
        ) as mock_section_class:
            mock_section_instance = MagicMock()
            mock_section_instance.filter = MagicMock()
            mock_section_instance.filter.return_value = MagicMock()
            mock_section_instance.filter.return_value.get_or_none = AsyncMock(
                return_value=None
            )
            mock_section_class.return_value = mock_section_instance

            # Mock CohortMembers.filter().prefetch_related().get_or_none()
            with patch(
                "app.graphql_api.program.mutations.CohortMembers",
                new_callable=MagicMock,
            ) as mock_cohort_members_class:
                # Create a proper mock for CohortMembers
                mock_cohort_member = MagicMock()
                mock_cohort = MagicMock()
                # Use a real datetime object with UTC timezone for started_at
                mock_cohort.started_at = datetime.now(timezone.utc)
                mock_cohort_member.cohort = mock_cohort

                mock_cohort_members_instance = MagicMock()
                mock_cohort_members_instance.filter = MagicMock()
                mock_cohort_members_instance.filter.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value.get_or_none = AsyncMock(
                    return_value=mock_cohort_member
                )
                mock_cohort_members_class.return_value = (
                    mock_cohort_members_instance
                )

                # Mock LiveSession.filter().get_or_none()
                with patch(
                    "app.graphql_api.program.mutations.LiveSession.filter",
                    new_callable=MagicMock,
                ) as mock_live_session_filter:
                    mock_live_session = MagicMock(spec=LiveSession)
                    # Add required attributes for the LiveSession mock
                    mock_live_session.recording_url = (
                        "https://example.com/recording"
                    )
                    mock_live_session.title = "Test Live Session"
                    mock_live_session.description = "Test Description"
                    mock_live_session.meeting_start_time = datetime.now(
                        timezone.utc
                    )
                    mock_live_session.zoom_link = "https://zoom.us/test"
                    mock_live_session_filter.return_value = MagicMock()
                    mock_live_session_filter.return_value.get_or_none = (
                        AsyncMock(return_value=mock_live_session)
                    )

                    # Mock ProgressService.make_progress
                    with patch(
                        "app.graphql_api.program.services.ProgressService.make_progress",
                        new_callable=AsyncMock,
                    ):
                        # Mock update_status_by_participant_id
                        with patch(
                            "app.graphql_api.program.mutations.update_status_by_participant_id",
                            new_callable=AsyncMock,
                        ) as mock_update_status:
                            # Mock get_module_progress
                            with patch(
                                "app.graphql_api.program.mutations.get_module_progress",
                                new_callable=AsyncMock,
                            ) as mock_get_progress:
                                mock_progress = MagicMock(
                                    spec=ModuleProgressType
                                )
                                mock_get_progress.return_value = mock_progress

                                # Call the function
                                result = await make_progress(
                                    mock_info, section_id, program_module_id
                                )

                                # Verify results
                                assert result == mock_progress

                                # Verify mock calls
                                mock_update_status.assert_called_once_with(
                                    participant_id=mock_info.context.user.sub,
                                    live_session_id=section_id,
                                    status=BookingStatusEnum.WATCHED_RECORDING,
                                )
                                mock_get_progress.assert_called_once_with(
                                    program_module_id=program_module_id,
                                    participant_id=mock_info.context.user.sub,
                                    info=mock_info,
                                )


@pytest.mark.asyncio
async def test_make_progress_new_activity():
    """Test make_progress with new activity."""
    # Create mock info, section_id, and program_module_id
    mock_info = MagicMock(spec=Info)
    mock_info.context.user.sub = uuid4()
    section_id = uuid4()
    program_module_id = uuid4()

    # Create mock data
    mock_data = MagicMock(spec=PersonalSuccessInput)

    # Mock ParticipantActivity.filter().filter().first()
    with patch(
        "app.graphql_api.program.mutations.ParticipantActivity",
        new_callable=MagicMock,
    ) as mock_activity_class:
        mock_activity_instance = MagicMock()
        mock_activity_instance.filter = MagicMock()
        mock_activity_instance.filter.return_value = MagicMock()
        mock_activity_instance.filter.return_value.filter = MagicMock()
        mock_activity_instance.filter.return_value.filter.return_value = (
            MagicMock()
        )
        mock_activity_instance.filter.return_value.filter.return_value.first = AsyncMock(
            return_value=None
        )
        mock_activity_class.return_value = mock_activity_instance

        # Mock ProgramModuleSection.filter().get_or_none()
        with patch(
            "app.graphql_api.program.mutations.ProgramModuleSection",
            new_callable=MagicMock,
        ) as mock_section_class:
            # Create mock section without using spec
            mock_section = MagicMock()
            mock_section_instance = MagicMock()
            mock_section_instance.filter = MagicMock()
            mock_section_instance.filter.return_value = MagicMock()
            mock_section_instance.filter.return_value.get_or_none = AsyncMock(
                return_value=mock_section
            )
            mock_section_class.return_value = mock_section_instance

            # Mock CohortMembers.filter().prefetch_related().get_or_none()
            with patch(
                "app.graphql_api.program.mutations.CohortMembers",
                new_callable=MagicMock,
            ) as mock_cohort_members_class:
                # Create a proper mock for CohortMembers without using spec
                mock_cohort_member = MagicMock()
                mock_cohort = MagicMock()
                mock_cohort.started_at = datetime.now(
                    timezone.utc
                ) + pendulum.duration(days=1)  # Future date
                mock_cohort_member.cohort = mock_cohort

                mock_cohort_members_instance = MagicMock()
                mock_cohort_members_instance.filter = MagicMock()
                mock_cohort_members_instance.filter.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value.get_or_none = AsyncMock(
                    return_value=mock_cohort_member
                )
                mock_cohort_members_class.return_value = (
                    mock_cohort_members_instance
                )

                # Mock LiveSession.filter().get_or_none() to return None
                with patch(
                    "app.graphql_api.program.mutations.LiveSession.filter",
                    new_callable=MagicMock,
                ) as mock_live_session_filter:
                    mock_live_session_filter.return_value = MagicMock()
                    mock_live_session_filter.return_value.get_or_none = (
                        AsyncMock(return_value=None)
                    )

                    # Mock ProgressService.make_progress
                    with patch(
                        "app.graphql_api.program.mutations.ProgressService.make_progress",
                        new_callable=AsyncMock,
                    ) as mock_make_progress:
                        # Mock get_module_progress
                        with patch(
                            "app.graphql_api.program.mutations.get_module_progress",
                            new_callable=AsyncMock,
                        ) as mock_get_progress:
                            mock_progress = MagicMock(spec=ModuleProgressType)
                            mock_get_progress.return_value = mock_progress

                            # Call the function
                            result = await make_progress(
                                mock_info,
                                section_id,
                                program_module_id,
                                mock_data,
                            )

                            # Verify results
                            assert result == mock_progress

                            # Verify mock calls
                            mock_make_progress.assert_called_once()
                            mock_get_progress.assert_called_once_with(
                                program_module_id=program_module_id,
                                participant_id=mock_info.context.user.sub,
                                info=mock_info,
                            )


@pytest.mark.asyncio
async def test_make_progress_existing_weight_activity():
    """Test make_progress with existing weight activity."""
    # Create mock info, section_id, and program_module_id
    mock_info = MagicMock(spec=Info)
    mock_info.context.user.sub = uuid4()
    section_id = uuid4()
    program_module_id = uuid4()

    # Create mock data
    mock_data = MagicMock(spec=PersonalSuccessInput)

    # Create mock activity without using spec
    mock_activity = MagicMock()
    mock_activity.activity_type = ParticipantActivityEnum.WEIGHT

    # Mock ParticipantActivity.filter().filter().first()
    with patch(
        "app.graphql_api.program.mutations.ParticipantActivity",
        new_callable=MagicMock,
    ) as mock_activity_class:
        mock_activity_instance = MagicMock()
        mock_activity_instance.filter = MagicMock()
        mock_activity_instance.filter.return_value = MagicMock()
        mock_activity_instance.filter.return_value.filter = MagicMock()
        mock_activity_instance.filter.return_value.filter.return_value = (
            MagicMock()
        )
        mock_activity_instance.filter.return_value.filter.return_value.first = AsyncMock(
            return_value=mock_activity
        )
        mock_activity_class.return_value = mock_activity_instance

        # Mock ProgramModuleSection.filter().get_or_none()
        with patch(
            "app.graphql_api.program.mutations.ProgramModuleSection",
            new_callable=MagicMock,
        ) as mock_section_class:
            # Create mock section without using spec
            mock_section = MagicMock()
            mock_section_instance = MagicMock()
            mock_section_instance.filter = MagicMock()
            mock_section_instance.filter.return_value = MagicMock()
            mock_section_instance.filter.return_value.get_or_none = AsyncMock(
                return_value=mock_section
            )
            mock_section_class.return_value = mock_section_instance

            # Mock CohortMembers.filter().prefetch_related().get_or_none()
            with patch(
                "app.graphql_api.program.mutations.CohortMembers",
                new_callable=MagicMock,
            ) as mock_cohort_members_class:
                # Create a proper mock for CohortMembers without using spec
                mock_cohort_member = MagicMock()
                mock_cohort = MagicMock()
                mock_cohort.started_at = datetime.now(timezone.utc)
                mock_cohort_member.cohort = mock_cohort

                mock_cohort_members_instance = MagicMock()
                mock_cohort_members_instance.filter = MagicMock()
                mock_cohort_members_instance.filter.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value = MagicMock()
                mock_cohort_members_instance.filter.return_value.prefetch_related.return_value.get_or_none = AsyncMock(
                    return_value=mock_cohort_member
                )
                mock_cohort_members_class.return_value = (
                    mock_cohort_members_instance
                )

                # Mock LiveSession.filter().get_or_none() to return None
                with patch(
                    "app.graphql_api.program.mutations.LiveSession.filter",
                    new_callable=MagicMock,
                ) as mock_live_session_filter:
                    mock_live_session_filter.return_value = MagicMock()
                    mock_live_session_filter.return_value.get_or_none = (
                        AsyncMock(return_value=None)
                    )

                    # Mock ProgressService.make_progress
                    with patch(
                        "app.graphql_api.program.mutations.ProgressService.make_progress",
                        new_callable=AsyncMock,
                    ) as mock_make_progress:
                        # Mock get_module_progress
                        with patch(
                            "app.graphql_api.program.mutations.get_module_progress",
                            new_callable=AsyncMock,
                        ) as mock_get_progress:
                            mock_progress = MagicMock(spec=ModuleProgressType)
                            mock_get_progress.return_value = mock_progress

                            # Call the function
                            result = await make_progress(
                                mock_info,
                                section_id,
                                program_module_id,
                                mock_data,
                            )

                            # Verify results
                            assert result == mock_progress

                            # Verify mock calls
                            mock_make_progress.assert_called_once_with(
                                mock_section,
                                mock_info.context.user.sub,
                                mock_data,
                            )
                            mock_get_progress.assert_called_once_with(
                                program_module_id=program_module_id,
                                participant_id=mock_info.context.user.sub,
                                info=mock_info,
                            )
