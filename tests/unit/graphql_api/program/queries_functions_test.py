from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime, timezone

import pytest
import pendulum

from app.graphql_api.program.queries import (
    process_program_module,
    get_activity_type_value,
    filter_activities,
    map_to_progress_data_type,
    calculate_weight_trend,
)
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    TrendEnum,
)
from ciba_participant.program.models import (
    ProgramModule,
    ProgramModuleSection,
)
from app.graphql_api.program.types import ProgramModuleType, ProgressDataType


def test_render_module_with_sections_filtering_logic():
    """Test the filtering logic in render_module_with_sections."""
    # Create mock sections
    mock_section1 = MagicMock(spec=ProgramModuleSection)
    mock_section1.id = uuid4()
    mock_section1.activity_type = ParticipantActivityEnum.ARTICLE
    mock_section1.metadata = {"url": "https://example.com"}

    mock_section2 = MagicMock(spec=ProgramModuleSection)
    mock_section2.id = uuid4()
    mock_section2.activity_type = ParticipantActivityEnum.QUIZ
    mock_section2.metadata = {"form_id": "form123"}

    mock_section3 = MagicMock(spec=ProgramModuleSection)
    mock_section3.id = uuid4()
    mock_section3.activity_type = ParticipantActivityEnum.ARTICLE
    mock_section3.metadata = {}  # Missing url

    # Create a list of sections
    sections = [mock_section1, mock_section2, mock_section3]

    # Extract the filtering logic from render_module_with_sections
    required_fields = {
        ParticipantActivityEnum.ARTICLE: ["url"],
        ParticipantActivityEnum.PLAY: ["url"],
        ParticipantActivityEnum.RECIPES: ["url"],
        ParticipantActivityEnum.QUIZ: ["form_id"],
    }

    # Apply the filtering logic
    for section in sections[:]:
        required_keys = required_fields.get(section.activity_type, [])
        missing_keys = [
            key for key in required_keys if not section.metadata.get(key)
        ]

        if missing_keys:
            sections.remove(section)

    # Verify results
    assert len(sections) == 2
    assert sections[0] == mock_section1
    assert sections[1] == mock_section2
    assert mock_section3 not in sections


def test_render_module_with_sections_different_activity_types():
    """Test the filtering logic with different activity types."""
    # Create mock sections with different activity types
    mock_section1 = MagicMock(spec=ProgramModuleSection)
    mock_section1.id = uuid4()
    mock_section1.activity_type = ParticipantActivityEnum.PLAY
    mock_section1.metadata = {"url": "https://example.com/video"}

    mock_section2 = MagicMock(spec=ProgramModuleSection)
    mock_section2.id = uuid4()
    mock_section2.activity_type = ParticipantActivityEnum.RECIPES
    mock_section2.metadata = {"url": "https://example.com/recipe"}

    mock_section3 = MagicMock(spec=ProgramModuleSection)
    mock_section3.id = uuid4()
    mock_section3.activity_type = ParticipantActivityEnum.PLAY
    mock_section3.metadata = {}  # Missing url

    mock_section4 = MagicMock(spec=ProgramModuleSection)
    mock_section4.id = uuid4()
    mock_section4.activity_type = ParticipantActivityEnum.WEIGHT
    mock_section4.metadata = {}  # No required fields for WEIGHT

    # Create a list of sections
    sections = [mock_section1, mock_section2, mock_section3, mock_section4]

    # Extract the filtering logic from render_module_with_sections
    required_fields = {
        ParticipantActivityEnum.ARTICLE: ["url"],
        ParticipantActivityEnum.PLAY: ["url"],
        ParticipantActivityEnum.RECIPES: ["url"],
        ParticipantActivityEnum.QUIZ: ["form_id"],
    }

    # Apply the filtering logic
    for section in sections[:]:
        required_keys = required_fields.get(section.activity_type, [])
        missing_keys = [
            key for key in required_keys if not section.metadata.get(key)
        ]

        if missing_keys:
            sections.remove(section)

    # Verify results
    assert len(sections) == 3
    assert mock_section1 in sections
    assert mock_section2 in sections
    assert mock_section3 not in sections  # Missing url for PLAY
    assert mock_section4 in sections  # No required fields for WEIGHT


@pytest.mark.asyncio
async def test_process_program_module():
    """Test process_program_module function."""
    # Create mock cohort
    mock_cohort = MagicMock()
    mock_cohort.id = uuid4()

    # Create mock program module
    mock_program_module = MagicMock()
    mock_program_module.id = uuid4()
    mock_program_module.started_at = datetime.now(timezone.utc)
    mock_program_module.ended_at = datetime.now(
        timezone.utc
    ) + pendulum.duration(days=7)

    # Create mock sections
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.id = uuid4()

    # Setup mocks for render_module_with_sections
    with patch(
        "app.graphql_api.program.queries.render_module_with_sections",
        new_callable=AsyncMock,
    ) as mock_render:
        mock_program_module_data = MagicMock(spec=ProgramModule)
        mock_program_module_data.id = mock_program_module.id
        mock_program_module_data.created_at = datetime.now(timezone.utc)
        mock_program_module_data.updated_at = datetime.now(timezone.utc)
        mock_program_module_data.title = "Test Module"
        mock_program_module_data.short_title = "Test"
        mock_program_module_data.description = "Test Description"

        mock_render.return_value = ([mock_section], mock_program_module_data)

        # Call the function
        result = await process_program_module(mock_cohort, mock_program_module)

        # Verify the result
        assert isinstance(result, ProgramModuleType)
        assert result.id == str(mock_program_module_data.id)
        assert result.title == mock_program_module_data.title
        assert result.short_title == mock_program_module_data.short_title
        assert result.description == mock_program_module_data.description
        assert result.started_at == mock_program_module.started_at
        assert result.ended_at == mock_program_module.ended_at
        assert result.sections == [mock_section]
        assert result.program_group == mock_cohort


def test_get_activity_type_value_enum():
    """Test get_activity_type_value with Enum input."""
    # Test with Enum
    result = get_activity_type_value(ParticipantActivityEnum.WEIGHT)
    assert result == ParticipantActivityEnum.WEIGHT.value


def test_get_activity_type_value_string():
    """Test get_activity_type_value with string input."""
    # Test with string
    result = get_activity_type_value("weight_type")
    assert result == "weight_type"


def test_get_activity_type_value_invalid():
    """Test get_activity_type_value with invalid input."""
    # Test with invalid type
    with pytest.raises(
        TypeError, match="activity_type must be an Enum or string"
    ):
        get_activity_type_value(123)


def test_filter_activities():
    """Test filter_activities function."""
    # Create test data
    now = datetime.now(timezone.utc)
    start_date = now - pendulum.duration(days=7)
    end_date = now + pendulum.duration(days=7)

    # Create activities
    activity1 = MagicMock(spec=ParticipantActivity)
    activity1.activity_type = ParticipantActivityEnum.WEIGHT
    activity1.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    activity1.created_at = now

    activity2 = MagicMock(spec=ParticipantActivity)
    activity2.activity_type = ParticipantActivityEnum.ACTIVITY
    activity2.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    activity2.created_at = now

    activity3 = MagicMock(spec=ParticipantActivity)
    activity3.activity_type = ParticipantActivityEnum.WEIGHT
    activity3.activity_device = ParticipantActivityDevice.WITHINGS
    activity3.created_at = now

    activity4 = MagicMock(spec=ParticipantActivity)
    activity4.activity_type = ParticipantActivityEnum.WEIGHT
    activity4.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    activity4.created_at = now - pendulum.duration(
        days=10
    )  # Outside date range

    activities = [activity1, activity2, activity3, activity4]

    # Test filtering
    result = filter_activities(
        activities=activities,
        activity_type=ParticipantActivityEnum.WEIGHT,
        device=ParticipantActivityDevice.MANUAL_INPUT,
        start_date=start_date,
        end_date=end_date,
    )

    # Verify results
    assert len(result) == 1
    assert result[0] == activity1


def test_filter_activities_empty():
    """Test filter_activities with empty input."""
    # Test with empty activities
    result = filter_activities(
        activities=[],
        activity_type=ParticipantActivityEnum.WEIGHT,
        device=ParticipantActivityDevice.MANUAL_INPUT,
        start_date=datetime.now(timezone.utc),
        end_date=datetime.now(timezone.utc),
    )

    # Verify results
    assert result == []


@pytest.mark.asyncio
async def test_map_to_progress_data_type_completed():
    """Test map_to_progress_data_type with completed activity."""
    # Create mock section
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.id = uuid4()
    mock_section.activity_type = ParticipantActivityEnum.WEIGHT

    # Create mock activity
    mock_activity = MagicMock(spec=ParticipantActivity)
    mock_activity.id = uuid4()
    mock_activity.created_at = datetime.now(timezone.utc)
    mock_activity.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity.value = "75.5"
    mock_activity.activity_device = ParticipantActivityDevice.MANUAL_INPUT

    # Call the function
    result = await map_to_progress_data_type(
        section=mock_section,
        activity=mock_activity,
        values=[mock_activity],
    )

    # Verify results
    assert isinstance(result, ProgressDataType)
    assert result.section_id == str(mock_section.id)
    assert result.section_type == ParticipantActivityEnum.WEIGHT.value
    assert result.completed is True
    assert "start_date" in result.metadata.__dict__
    assert "end_date" in result.metadata.__dict__
    assert len(result.values) == 1


@pytest.mark.asyncio
async def test_map_to_progress_data_type_not_completed():
    """Test map_to_progress_data_type with no activity."""
    # Create mock section
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.id = uuid4()
    mock_section.activity_type = ParticipantActivityEnum.WEIGHT

    # Call the function with no activity
    result = await map_to_progress_data_type(
        section=mock_section,
        activity=None,
        values=[],
    )

    # Verify results
    assert isinstance(result, ProgressDataType)
    assert result.section_id == str(mock_section.id)
    assert result.section_type == ParticipantActivityEnum.WEIGHT.value
    assert result.completed is False


@pytest.mark.asyncio
async def test_map_to_progress_data_type_group_no_url():
    """Test map_to_progress_data_type with GROUP activity type and no URL."""
    # Create mock section
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.id = uuid4()
    mock_section.activity_type = ParticipantActivityEnum.GROUP
    mock_section.metadata = {}  # No URL

    # Create mock activity
    mock_activity = MagicMock(spec=ParticipantActivity)
    mock_activity.created_at = datetime.now(timezone.utc)
    mock_activity.activity_type = ParticipantActivityEnum.GROUP

    # Call the function
    result = await map_to_progress_data_type(
        section=mock_section,
        activity=mock_activity,
        values=[],
    )

    # Verify results
    assert result is None


@pytest.mark.asyncio
async def test_calculate_weight_trend_no_activities():
    """Test calculate_weight_trend with no activities."""
    # Create mock participant ID and cohort module
    participant_id = uuid4()
    mock_cohort_module = MagicMock()
    mock_cohort_module.cohort.started_at = datetime.now(timezone.utc)
    mock_cohort_module.started_at = datetime.now(timezone.utc)
    mock_cohort_module.ended_at = datetime.now(
        timezone.utc
    ) + pendulum.duration(days=7)

    # Mock ParticipantActivity.filter to return empty list
    with patch(
        "app.graphql_api.program.queries.ParticipantActivity.filter",
        new_callable=MagicMock,
    ) as mock_filter:
        # Setup the mock to return a mock for all() and order_by().first()
        mock_all = AsyncMock(return_value=[])
        mock_first = AsyncMock(return_value=None)

        mock_order_by = MagicMock()
        mock_order_by.first = mock_first

        mock_filter_instance = MagicMock()
        mock_filter_instance.all = mock_all
        mock_filter_instance.order_by = MagicMock(return_value=mock_order_by)

        mock_filter.return_value = mock_filter_instance

        # Call the function
        result = await calculate_weight_trend(
            participant_id, mock_cohort_module
        )

        # Verify results
        assert result == (None, None)


@pytest.mark.asyncio
async def test_calculate_weight_trend_single_activity():
    """Test calculate_weight_trend with a single activity."""
    # Create mock participant ID and cohort module
    participant_id = uuid4()
    mock_cohort_module = MagicMock()
    mock_cohort_module.cohort.started_at = datetime.now(timezone.utc)
    mock_cohort_module.started_at = datetime.now(timezone.utc)
    mock_cohort_module.ended_at = datetime.now(
        timezone.utc
    ) + pendulum.duration(days=7)

    # Create mock activity
    mock_activity = MagicMock(spec=ParticipantActivity)
    mock_activity.created_at = datetime.now(timezone.utc)
    mock_activity.value = 75.5

    # Mock ParticipantActivity.filter to return single activity
    with patch(
        "app.graphql_api.program.queries.ParticipantActivity.filter",
        new_callable=MagicMock,
    ) as mock_filter:
        # Setup the mock to return a mock for all() and order_by().first()
        mock_all = AsyncMock(return_value=[mock_activity])
        mock_first = AsyncMock(return_value=mock_activity)
        mock_first_prev = AsyncMock(return_value=None)

        mock_order_by = MagicMock()
        mock_order_by.first = mock_first

        mock_order_by_2 = MagicMock()
        mock_order_by_2.first = mock_first_prev

        mock_filter_instance = MagicMock()
        mock_filter_instance.all = mock_all
        mock_filter_instance.order_by = MagicMock(
            side_effect=[mock_order_by, mock_order_by_2]
        )

        mock_filter.return_value = mock_filter_instance

        # Call the function
        result = await calculate_weight_trend(
            participant_id, mock_cohort_module
        )

        # Verify results
        assert result == (TrendEnum.FLAT, TrendEnum.FLAT)


@pytest.mark.asyncio
async def test_calculate_weight_trend_multiple_activities():
    """Test calculate_weight_trend with multiple activities."""
    # Create mock participant ID and cohort module
    participant_id = uuid4()
    mock_cohort_module = MagicMock()
    mock_cohort_module.cohort.started_at = datetime.now(timezone.utc)
    mock_cohort_module.started_at = datetime.now(timezone.utc)
    mock_cohort_module.ended_at = datetime.now(
        timezone.utc
    ) + pendulum.duration(days=7)

    # Create mock activities
    now = datetime.now(timezone.utc)

    mock_activity1 = MagicMock(spec=ParticipantActivity)
    mock_activity1.created_at = now - pendulum.duration(days=5)
    mock_activity1.value = 80.0

    mock_activity2 = MagicMock(spec=ParticipantActivity)
    mock_activity2.created_at = now
    mock_activity2.value = 75.0

    # Mock ParticipantActivity.filter to return multiple activities
    with patch(
        "app.graphql_api.program.queries.ParticipantActivity.filter",
        new_callable=MagicMock,
    ) as mock_filter:
        # Setup the mock to return a mock for all() and order_by().first()
        mock_all = AsyncMock(return_value=[mock_activity1, mock_activity2])
        mock_first = AsyncMock(return_value=mock_activity1)
        mock_first_prev = AsyncMock(return_value=None)

        mock_order_by = MagicMock()
        mock_order_by.first = mock_first

        mock_order_by_2 = MagicMock()
        mock_order_by_2.first = mock_first_prev

        mock_filter_instance = MagicMock()
        mock_filter_instance.all = mock_all
        mock_filter_instance.order_by = MagicMock(
            side_effect=[mock_order_by, mock_order_by_2]
        )

        mock_filter.return_value = mock_filter_instance

        # Call the function
        result = await calculate_weight_trend(
            participant_id, mock_cohort_module
        )

        # Verify results - weight went down from 80 to 75
        assert result == (TrendEnum.DOWN, TrendEnum.FLAT)


@pytest.mark.asyncio
async def test_calculate_weight_trend_with_previous_activity():
    """Test calculate_weight_trend with previous activity."""
    # Create mock participant ID and cohort module
    participant_id = uuid4()
    mock_cohort_module = MagicMock()
    mock_cohort_module.cohort.started_at = datetime.now(timezone.utc)
    mock_cohort_module.started_at = datetime.now(timezone.utc)
    mock_cohort_module.ended_at = datetime.now(
        timezone.utc
    ) + pendulum.duration(days=7)

    # Create mock activities
    now = datetime.now(timezone.utc)

    mock_activity1 = MagicMock(spec=ParticipantActivity)
    mock_activity1.created_at = now - pendulum.duration(days=5)
    mock_activity1.value = 80.0

    mock_activity2 = MagicMock(spec=ParticipantActivity)
    mock_activity2.created_at = now
    mock_activity2.value = 75.0

    mock_previous_activity = MagicMock(spec=ParticipantActivity)
    mock_previous_activity.created_at = now - pendulum.duration(days=10)
    mock_previous_activity.value = 85.0

    # Mock ParticipantActivity.filter to return multiple activities
    with patch(
        "app.graphql_api.program.queries.ParticipantActivity.filter",
        new_callable=MagicMock,
    ) as mock_filter:
        # Setup the mock to return a mock for all() and order_by().first()
        mock_all = AsyncMock(return_value=[mock_activity1, mock_activity2])
        mock_first = AsyncMock(return_value=mock_activity1)
        mock_first_prev = AsyncMock(return_value=mock_previous_activity)

        mock_order_by = MagicMock()
        mock_order_by.first = mock_first

        mock_order_by_2 = MagicMock()
        mock_order_by_2.first = mock_first_prev

        mock_filter_instance = MagicMock()
        mock_filter_instance.all = mock_all
        mock_filter_instance.order_by = MagicMock(
            side_effect=[mock_order_by, mock_order_by_2]
        )

        mock_filter.return_value = mock_filter_instance

        # Call the function
        result = await calculate_weight_trend(
            participant_id, mock_cohort_module
        )

        # Verify results - weight went down from 80 to 75, and previous trend was down from 85 to 80
        assert result == (TrendEnum.DOWN, TrendEnum.DOWN)
