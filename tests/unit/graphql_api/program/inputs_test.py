from datetime import datetime

import pytest

from app.graphql_api.program.inputs import (
    PersonalSuccessInput,
    ProgramModuleUpdateInput,
    SectionPersonalSuccessInput,
    SectionCurriculumInput,
    SectionCoachingCallInput,
    SectionVideoTypeInput,
    SectionRecipeTypeInput,
    WeightTypeInput,
    FoodTypeInput,
    ActivityTypeInput,
    QuizTypeInput,
    ProgramModuleSectionInput,
    ProgramModuleInput,
)
from app.graphql_api.program.pydantic_models import (
    FILE,
    FOOD,
    INPUT,
    TYPE_FORM,
    VIDEO,
    ZOOM,
)
from app.models.program import (
    ModuleDataTypes,
)


def test_personal_success_input():
    """Test PersonalSuccessInput."""
    # Create input with default values
    input_default = PersonalSuccessInput()
    assert input_default.response_id is None
    assert input_default.value is None

    # Create input with custom values
    input_custom = PersonalSuccessInput(response_id="123", value="test")
    assert input_custom.response_id == "123"
    assert input_custom.value == "test"

    # Verify it's a strawberry input type
    assert hasattr(input_default, "__strawberry_definition__")


def test_program_module_update_input():
    """Test ProgramModuleUpdateInput."""
    # Create input
    now = datetime.now()
    input_data = ProgramModuleUpdateInput(
        title="Test Module",
        short_title="Test",
        started_at=now,
        ended_at=now,
        description="Test Description",
    )

    # Verify fields
    assert input_data.title == "Test Module"
    assert input_data.short_title == "Test"
    assert input_data.started_at == now
    assert input_data.ended_at == now
    assert input_data.description == "Test Description"

    # Verify it's a strawberry input type
    assert hasattr(input_data, "__strawberry_definition__")


def test_section_personal_success_input():
    """Test SectionPersonalSuccessInput."""
    # Create input with default values
    input_default = SectionPersonalSuccessInput()
    assert input_default.form_id is None
    assert input_default.type == TYPE_FORM

    # Create input with custom values
    input_custom = SectionPersonalSuccessInput(form_id="form123")
    assert input_custom.form_id == "form123"
    assert input_custom.type == TYPE_FORM

    # Verify it's a strawberry input type
    assert hasattr(input_default, "__strawberry_definition__")


def test_section_curriculum_input():
    """Test SectionCurriculumInput."""
    # Create input with default values
    input_default = SectionCurriculumInput()
    assert input_default.url is None
    assert input_default.type == FILE

    # Create input with custom values
    input_custom = SectionCurriculumInput(url="https://example.com")
    assert input_custom.url == "https://example.com"
    assert input_custom.type == FILE

    # Verify it's a strawberry input type
    assert hasattr(input_default, "__strawberry_definition__")


def test_section_coaching_call_input():
    """Test SectionCoachingCallInput."""
    # Create input with default values
    input_default = SectionCoachingCallInput()
    assert input_default.started_at is None
    assert input_default.url is None
    assert input_default.recording_url is None
    assert input_default.type == ZOOM

    # Create input with custom values
    now = datetime.now()
    input_custom = SectionCoachingCallInput(
        started_at=now,
        url="https://example.com/zoom",
        recording_url="https://example.com/recording",
    )
    assert input_custom.started_at == now
    assert input_custom.url == "https://example.com/zoom"
    assert input_custom.recording_url == "https://example.com/recording"
    assert input_custom.type == ZOOM

    # Verify it's a strawberry input type
    assert hasattr(input_default, "__strawberry_definition__")


def test_section_video_type_input():
    """Test SectionVideoTypeInput."""
    # Create input with default values
    input_default = SectionVideoTypeInput()
    assert input_default.url is None
    assert input_default.type == VIDEO
    assert input_default.is_intro is False

    # Create input with custom values
    input_custom = SectionVideoTypeInput(
        url="https://example.com/video",
        is_intro=True,
    )
    assert input_custom.url == "https://example.com/video"
    assert input_custom.type == VIDEO
    assert input_custom.is_intro is True

    # Verify it's a strawberry input type
    assert hasattr(input_default, "__strawberry_definition__")


def test_section_recipe_type_input():
    """Test SectionRecipeTypeInput."""
    # Create input with default values
    input_default = SectionRecipeTypeInput()
    assert input_default.url is None
    assert input_default.type == FILE

    # Create input with custom values
    input_custom = SectionRecipeTypeInput(url="https://example.com/recipe")
    assert input_custom.url == "https://example.com/recipe"
    assert input_custom.type == FILE

    # Verify it's a strawberry input type
    assert hasattr(input_default, "__strawberry_definition__")


def test_weight_type_input():
    """Test WeightTypeInput."""
    # Create input
    input_data = WeightTypeInput()
    assert input_data.type == INPUT

    # Verify it's a strawberry input type
    assert hasattr(input_data, "__strawberry_definition__")


def test_food_type_input():
    """Test FoodTypeInput."""
    # Create input
    input_data = FoodTypeInput()
    assert input_data.type == FOOD

    # Verify it's a strawberry input type
    assert hasattr(input_data, "__strawberry_definition__")


def test_activity_type_input():
    """Test ActivityTypeInput."""
    # Create input
    input_data = ActivityTypeInput()
    assert input_data.type == INPUT

    # Verify it's a strawberry input type
    assert hasattr(input_data, "__strawberry_definition__")


def test_quiz_type_input():
    """Test QuizTypeInput."""
    # Create input
    input_data = QuizTypeInput()
    assert input_data.type == FILE

    # Verify it's a strawberry input type
    assert hasattr(input_data, "__strawberry_definition__")


def test_program_module_section_input_to_dict_personal_success():
    """Test ProgramModuleSectionInput.to_dict with personal_success."""
    # Create input with personal_success
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        personal_success=SectionPersonalSuccessInput(form_id="form123"),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {"form_id": "form123", "type": TYPE_FORM}
    assert result["type"] == ModuleDataTypes.personal_success.value


def test_program_module_section_input_to_dict_curriculum():
    """Test ProgramModuleSectionInput.to_dict with curriculum."""
    # Create input with curriculum
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        curriculum=SectionCurriculumInput(url="https://example.com"),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {"url": "https://example.com", "type": FILE}
    assert result["type"] == ModuleDataTypes.curriculum.value


def test_program_module_section_input_to_dict_coaching_call():
    """Test ProgramModuleSectionInput.to_dict with coaching_call."""
    # Create input with coaching_call
    now = datetime.now()
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        coaching_call=SectionCoachingCallInput(
            started_at=now,
            url="https://example.com/zoom",
        ),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"]["url"] == "https://example.com/zoom"
    assert result["metadata"]["started_at"] == now.isoformat()
    assert result["metadata"]["recording_url"] is None
    assert result["metadata"]["type"] == ZOOM
    assert result["type"] == ModuleDataTypes.coaching_call.value


def test_program_module_section_input_to_dict_video_type():
    """Test ProgramModuleSectionInput.to_dict with video_type."""
    # Create input with video_type
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        video_type=SectionVideoTypeInput(
            url="https://example.com/video",
            is_intro=True,
        ),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {
        "url": "https://example.com/video",
        "type": VIDEO,
        "is_intro": True,
    }
    assert result["type"] == ModuleDataTypes.video_type.value


def test_program_module_section_input_to_dict_recipe_type():
    """Test ProgramModuleSectionInput.to_dict with recipe_type."""
    # Create input with recipe_type
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        recipe_type=SectionRecipeTypeInput(url="https://example.com/recipe"),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {
        "url": "https://example.com/recipe",
        "type": FILE,
    }
    assert result["type"] == ModuleDataTypes.recipe_type.value


def test_program_module_section_input_to_dict_weight_type():
    """Test ProgramModuleSectionInput.to_dict with weight_type."""
    # Create input with weight_type
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        weight_type=WeightTypeInput(),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {"type": INPUT}
    assert result["type"] == ModuleDataTypes.weight_type.value


def test_program_module_section_input_to_dict_food_type():
    """Test ProgramModuleSectionInput.to_dict with food_type."""
    # Create input with food_type
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        food_type=FoodTypeInput(),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {"type": FOOD}
    assert result["type"] == ModuleDataTypes.food_type.value


def test_program_module_section_input_to_dict_activity_type():
    """Test ProgramModuleSectionInput.to_dict with activity_type."""
    # Create input with activity_type
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        activity_type=ActivityTypeInput(),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {"type": INPUT}
    assert result["type"] == ModuleDataTypes.activity_type.value


def test_program_module_section_input_to_dict_quiz_type():
    """Test ProgramModuleSectionInput.to_dict with quiz_type."""
    # Create input with quiz_type
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
        quiz_type=QuizTypeInput(),
    )

    # Call to_dict
    result = input_data.to_dict()

    # Verify result
    assert result["title"] == "Test Section"
    assert result["description"] == "Test Description"
    assert result["metadata"] == {"type": FILE}
    assert result["type"] == ModuleDataTypes.quiz_type.value


def test_program_module_section_input_to_dict_no_type():
    """Test ProgramModuleSectionInput.to_dict with no type."""
    # Create input with no type
    input_data = ProgramModuleSectionInput(
        title="Test Section",
        description="Test Description",
    )

    # Call to_dict and expect ValueError
    with pytest.raises(
        ValueError,
        match="One of \\[personal_success, curriculum, coaching_call\\] should be provided.",
    ):
        input_data.to_dict()


def test_program_module_input():
    """Test ProgramModuleInput."""
    # Create input
    now = datetime.now()
    input_data = ProgramModuleInput(
        title="Test Module",
        short_title="Test",
        description="Test Description",
        started_at=now,
        ended_at=now,
    )

    # Verify fields
    assert input_data.title == "Test Module"
    assert input_data.short_title == "Test"
    assert input_data.description == "Test Description"
    assert input_data.started_at == now
    assert input_data.ended_at == now

    # Verify it's a strawberry input type
    assert hasattr(input_data, "__strawberry_definition__")
