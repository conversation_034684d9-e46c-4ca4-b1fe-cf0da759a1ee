from unittest.mock import MagicMock, patch
from datetime import datetime, timezone

import pendulum

from app.graphql_api.program.queries import filter_activities
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
    ParticipantActivityDevice,
)


def test_filter_activities_empty_list():
    """Test filter_activities with an empty list."""
    # Set up test parameters
    activity_type = ParticipantActivityEnum.WEIGHT
    device = ParticipantActivityDevice.MANUAL_INPUT
    start_date = datetime.now(timezone.utc) - pendulum.duration(days=7)
    end_date = datetime.now(timezone.utc)

    # Mock logger.error
    with patch("app.graphql_api.program.queries.logger.error") as mock_logger:
        # Call the function
        result = filter_activities(
            activities=[],
            activity_type=activity_type,
            device=device,
            start_date=start_date,
            end_date=end_date,
        )

        # Verify results
        assert result == []

        # Verify logger was called
        mock_logger.assert_called_once()
        assert "No activities found for" in mock_logger.call_args[0][0]


def test_filter_activities_matching_activities():
    """Test filter_activities with matching activities."""
    # Set up test parameters
    activity_type = ParticipantActivityEnum.WEIGHT
    device = ParticipantActivityDevice.MANUAL_INPUT
    start_date = datetime.now(timezone.utc) - pendulum.duration(days=7)
    end_date = datetime.now(timezone.utc)

    # Create mock activities
    mock_activity1 = MagicMock(spec=ParticipantActivity)
    mock_activity1.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity1.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity1.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=3
    )

    mock_activity2 = MagicMock(spec=ParticipantActivity)
    mock_activity2.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity2.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity2.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=5
    )

    # Call the function
    result = filter_activities(
        activities=[mock_activity1, mock_activity2],
        activity_type=activity_type,
        device=device,
        start_date=start_date,
        end_date=end_date,
    )

    # Verify results
    assert len(result) == 2
    assert mock_activity1 in result
    assert mock_activity2 in result


def test_filter_activities_non_matching_type():
    """Test filter_activities with non-matching activity type."""
    # Set up test parameters
    activity_type = ParticipantActivityEnum.WEIGHT
    device = ParticipantActivityDevice.MANUAL_INPUT
    start_date = datetime.now(timezone.utc) - pendulum.duration(days=7)
    end_date = datetime.now(timezone.utc)

    # Create mock activities
    mock_activity1 = MagicMock(spec=ParticipantActivity)
    mock_activity1.activity_type = (
        ParticipantActivityEnum.ACTIVITY
    )  # Different type
    mock_activity1.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity1.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=3
    )

    mock_activity2 = MagicMock(spec=ParticipantActivity)
    mock_activity2.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity2.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity2.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=5
    )

    # Call the function
    result = filter_activities(
        activities=[mock_activity1, mock_activity2],
        activity_type=activity_type,
        device=device,
        start_date=start_date,
        end_date=end_date,
    )

    # Verify results
    assert len(result) == 1
    assert mock_activity1 not in result
    assert mock_activity2 in result


def test_filter_activities_non_matching_device():
    """Test filter_activities with non-matching device."""
    # Set up test parameters
    activity_type = ParticipantActivityEnum.WEIGHT
    device = ParticipantActivityDevice.MANUAL_INPUT
    start_date = datetime.now(timezone.utc) - pendulum.duration(days=7)
    end_date = datetime.now(timezone.utc)

    # Create mock activities
    mock_activity1 = MagicMock(spec=ParticipantActivity)
    mock_activity1.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity1.activity_device = (
        ParticipantActivityDevice.WITHINGS
    )  # Different device
    mock_activity1.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=3
    )

    mock_activity2 = MagicMock(spec=ParticipantActivity)
    mock_activity2.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity2.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity2.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=5
    )

    # Call the function
    result = filter_activities(
        activities=[mock_activity1, mock_activity2],
        activity_type=activity_type,
        device=device,
        start_date=start_date,
        end_date=end_date,
    )

    # Verify results
    assert len(result) == 1
    assert mock_activity1 not in result
    assert mock_activity2 in result


def test_filter_activities_non_matching_date():
    """Test filter_activities with non-matching date range."""
    # Set up test parameters
    activity_type = ParticipantActivityEnum.WEIGHT
    device = ParticipantActivityDevice.MANUAL_INPUT
    start_date = datetime.now(timezone.utc) - pendulum.duration(days=7)
    end_date = datetime.now(timezone.utc) - pendulum.duration(days=2)

    # Create mock activities
    mock_activity1 = MagicMock(spec=ParticipantActivity)
    mock_activity1.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity1.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity1.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=1
    )  # Too recent

    mock_activity2 = MagicMock(spec=ParticipantActivity)
    mock_activity2.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity2.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity2.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=5
    )  # Within range

    mock_activity3 = MagicMock(spec=ParticipantActivity)
    mock_activity3.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity3.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity3.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=10
    )  # Too old

    # Call the function
    result = filter_activities(
        activities=[mock_activity1, mock_activity2, mock_activity3],
        activity_type=activity_type,
        device=device,
        start_date=start_date,
        end_date=end_date,
    )

    # Verify results
    assert len(result) == 1
    assert mock_activity1 not in result
    assert mock_activity2 in result
    assert mock_activity3 not in result


def test_filter_activities_string_activity_type():
    """Test filter_activities with string activity type."""
    # Set up test parameters
    activity_type = "WEIGHT"  # String instead of enum
    device = ParticipantActivityDevice.MANUAL_INPUT
    start_date = datetime.now(timezone.utc) - pendulum.duration(days=7)
    end_date = datetime.now(timezone.utc)

    # Create mock activities
    mock_activity = MagicMock(spec=ParticipantActivity)
    mock_activity.activity_type = (
        "WEIGHT"  # String value to match the mocked return value
    )
    mock_activity.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity.created_at = datetime.now(timezone.utc) - pendulum.duration(
        days=3
    )

    # Call the function directly without mocking get_activity_type_value
    # The function should handle string activity types directly
    result = filter_activities(
        activities=[mock_activity],
        activity_type=activity_type,
        device=device,
        start_date=start_date,
        end_date=end_date,
    )

    # Verify results
    assert len(result) == 1
    assert mock_activity in result
