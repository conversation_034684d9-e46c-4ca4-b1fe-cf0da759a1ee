from unittest.mock import MagicMock
from datetime import datetime, timezone

import pendulum

from app.graphql_api.program.mutations import find_closest_section
from ciba_participant.classes.models import LiveSession


def test_find_closest_section_empty_list():
    """Test find_closest_section with an empty list."""
    # Call the function with an empty list
    result = find_closest_section([])

    # Verify results
    assert result is None


def test_find_closest_section_single_section():
    """Test find_closest_section with a single section."""
    # Create a mock section
    mock_section = MagicMock(spec=LiveSession)
    mock_section.meeting_start_time = datetime.now(timezone.utc)

    # Call the function
    result = find_closest_section([mock_section])

    # Verify results
    assert result == mock_section


def test_find_closest_section_multiple_sections():
    """Test find_closest_section with multiple sections."""
    # Get current time
    now = datetime.now(timezone.utc)

    # Create mock sections with different start times
    mock_section1 = MagicMock(spec=LiveSession)
    mock_section1.meeting_start_time = now - pendulum.duration(
        days=2
    )  # 2 days ago

    mock_section2 = MagicMock(spec=LiveSession)
    mock_section2.meeting_start_time = now + pendulum.duration(
        hours=1
    )  # 1 hour from now

    mock_section3 = MagicMock(spec=LiveSession)
    mock_section3.meeting_start_time = now - pendulum.duration(
        hours=1
    )  # 1 hour ago

    mock_section4 = MagicMock(spec=LiveSession)
    mock_section4.meeting_start_time = now + pendulum.duration(
        days=3
    )  # 3 days from now

    # Call the function with the sections in random order
    result = find_closest_section(
        [mock_section1, mock_section2, mock_section3, mock_section4]
    )

    # Verify results - should return one of the sections closest to now
    # The implementation might choose either mock_section2 or mock_section3 as they're both close to now
    assert result in [mock_section2, mock_section3]


def test_find_closest_section_future_dates():
    """Test find_closest_section with all future dates."""
    # Get current time
    now = datetime.now(timezone.utc)

    # Create mock sections with different future start times
    mock_section1 = MagicMock(spec=LiveSession)
    mock_section1.meeting_start_time = now + pendulum.duration(
        days=5
    )  # 5 days from now

    mock_section2 = MagicMock(spec=LiveSession)
    mock_section2.meeting_start_time = now + pendulum.duration(
        hours=2
    )  # 2 hours from now

    mock_section3 = MagicMock(spec=LiveSession)
    mock_section3.meeting_start_time = now + pendulum.duration(
        days=1
    )  # 1 day from now

    # Call the function
    result = find_closest_section(
        [mock_section1, mock_section2, mock_section3]
    )

    # Verify results - should return the section closest to now (mock_section2)
    assert result == mock_section2


def test_find_closest_section_past_dates():
    """Test find_closest_section with all past dates."""
    # Get current time
    now = datetime.now(timezone.utc)

    # Create mock sections with different past start times
    mock_section1 = MagicMock(spec=LiveSession)
    mock_section1.meeting_start_time = now - pendulum.duration(
        days=5
    )  # 5 days ago

    mock_section2 = MagicMock(spec=LiveSession)
    mock_section2.meeting_start_time = now - pendulum.duration(
        hours=2
    )  # 2 hours ago

    mock_section3 = MagicMock(spec=LiveSession)
    mock_section3.meeting_start_time = now - pendulum.duration(
        days=1
    )  # 1 day ago

    # Call the function
    result = find_closest_section(
        [mock_section1, mock_section2, mock_section3]
    )

    # Verify results - should return the section closest to now (mock_section2)
    assert result == mock_section2


def test_find_closest_section_same_time():
    """Test find_closest_section with sections having the same time."""
    # Get current time
    now = datetime.now(timezone.utc)

    # Create mock sections with the same start time
    mock_section1 = MagicMock(spec=LiveSession)
    mock_section1.meeting_start_time = now

    mock_section2 = MagicMock(spec=LiveSession)
    mock_section2.meeting_start_time = now

    # Call the function
    result = find_closest_section([mock_section1, mock_section2])

    # Verify results - should return one of the sections (doesn't matter which one)
    assert result in [mock_section1, mock_section2]
