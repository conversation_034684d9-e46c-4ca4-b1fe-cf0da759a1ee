from unittest.mock import AsyncMock, MagicMock
from uuid import UUID

import pendulum
import pytest

from app.graphql_api.program.queries import get_participant_program_module

test_participant_id = UUID("a8a6838b-e24d-48eb-9331-598eb274219e")
test_module_id = UUID("fd0d4c70-e665-4472-8386-51660c48a0d6")
test_stared_at = pendulum.parse("2010-01-10")
test_ended_at = pendulum.parse("2010-01-20")


def make_async(value):
    """Create an awaitable that returns the given value."""

    async def coroutine():
        return value

    return coroutine


@pytest.fixture
def mock_data():
    """Fixture to prepare data for program modules"""
    mock_program_module = MagicMock()
    mock_program_module.id = test_module_id
    mock_program_module.started_at = test_stared_at
    mock_program_module.ended_at = test_ended_at
    mock_cohort_member = MagicMock()
    mock_cohort = MagicMock()
    mock_program_modules = MagicMock()
    first_module_mock = AsyncMock(return_value=mock_program_module)
    filter_module_mock = MagicMock(
        return_value=MagicMock(first=first_module_mock)
    )
    mock_program_modules.filter = filter_module_mock
    mock_cohort_member.cohort = make_async(mock_cohort)()
    mock_cohort.program_modules = mock_program_modules
    first_member_mock = AsyncMock(return_value=mock_cohort_member)
    filter_member_mock = MagicMock(
        return_value=MagicMock(first=first_member_mock)
    )

    return {
        "filter_member_mock": filter_member_mock,
        "first_member_mock": first_member_mock,
        "filter_module_mock": filter_module_mock,
        "first_module_mock": first_module_mock,
    }


@pytest.mark.asyncio
async def test_get_participant_program_module_with_cohort_member_not_found(
    mocker, mock_data
):
    """
    get_participant_program_module should raise ValueError
    when the provided participant has no cohort membership.
    """
    mocks = mock_data

    mocker.patch(
        "app.graphql_api.program.queries.CohortMembers.filter",
        mocks["filter_member_mock"],
    )

    mocks["first_member_mock"].return_value = None

    with pytest.raises(
        ValueError,
        match=f"No cohort found for participant: {test_participant_id}",
    ):
        await get_participant_program_module(
            test_participant_id, test_module_id
        )


@pytest.mark.asyncio
async def test_get_participant_program_module_with_program_module_not_found(
    mocker, mock_data
):
    """
    get_participant_program_module should raise ValueError
    when the provided program module is not found.
    """
    mocks = mock_data

    mocker.patch(
        "app.graphql_api.program.queries.CohortMembers.filter",
        mocks["filter_member_mock"],
    )

    mocks["first_module_mock"].return_value = None

    with pytest.raises(
        ValueError,
        match=f"Program module with ID '{test_module_id}' not found.",
    ):
        await get_participant_program_module(
            test_participant_id, test_module_id
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_today, expected_current_value",
    [
        (pendulum.parse("2010-01-15"), True),
        (pendulum.parse("2010-01-30"), False),
    ],
)
async def test_get_participant_program_module_success(
    test_today, expected_current_value, mocker, mock_data
):
    """
    get_participant_program_module should raise ValueError
    when the provided program module is not found.
    """
    mocks = mock_data
    mock_module_data = MagicMock()
    mock_module_data.id = test_module_id
    mock_module_data.created_at = test_stared_at
    mock_module_data.updated_at = test_stared_at
    mock_module_data.title = "Test Title"
    mock_module_data.short_title = "Test"
    mock_module_data.description = "Test Description"

    mocker.patch(
        "app.graphql_api.program.queries.CohortMembers.filter",
        mocks["filter_member_mock"],
    )

    mocker.patch("pendulum.now", return_value=test_today)
    mocker.patch(
        "app.graphql_api.program.queries.render_module_with_sections",
        AsyncMock(return_value=([], mock_module_data)),
    )
    actual_value = await get_participant_program_module(
        test_participant_id, test_module_id
    )

    assert actual_value.id == str(test_module_id)
    assert actual_value.current == expected_current_value
