from unittest.mock import AsyncMock, MagicMock
from uuid import UUID

import pendulum
import pytest

# Import the module, not the function directly
import app.graphql_api.program.queries as queries_module
from app.graphql_api.program.types import ProgramModuleType

test_participant_id = UUID("a8a6838b-e24d-48eb-9331-598eb274219e")
test_module_id = UUID("fd0d4c70-e665-4472-8386-51660c48a0d6")
test_started_at = pendulum.parse("2010-01-10")
test_ended_at = pendulum.parse("2010-01-20")


@pytest.fixture
def mock_data():
    """Fixture to prepare data for program modules"""
    # Create mock program modules
    mock_program_module1 = MagicMock()
    mock_program_module1.id = test_module_id
    mock_program_module1.started_at = test_started_at
    mock_program_module1.ended_at = test_ended_at
    mock_program_module1.title = "Test Module 1"
    mock_program_module1.short_title = "Module 1"
    mock_program_module1.description = "Test Description 1"

    mock_program_module2 = MagicMock()
    mock_program_module2.id = UUID("fd0d4c70-e665-4472-8386-51660c48a0d7")
    mock_program_module2.started_at = test_started_at.add(days=30)
    mock_program_module2.ended_at = test_ended_at.add(days=30)
    mock_program_module2.title = "Test Module 2"
    mock_program_module2.short_title = "Module 2"
    mock_program_module2.description = "Test Description 2"

    # Create mock cohort and cohort member
    mock_cohort_member = MagicMock()
    mock_cohort = MagicMock()

    # Set up cohort member and cohort
    # Create a proper awaitable property
    mock_cohort_member.cohort = AsyncMock()
    mock_cohort_member.cohort.return_value = mock_cohort

    # Set up filter for cohort members
    first_member_mock = AsyncMock(return_value=mock_cohort_member)
    filter_member_mock = MagicMock(
        return_value=MagicMock(first=first_member_mock)
    )

    return {
        "filter_member_mock": filter_member_mock,
        "first_member_mock": first_member_mock,
        "mock_cohort": mock_cohort,
        "mock_program_module1": mock_program_module1,
        "mock_program_module2": mock_program_module2,
        "mock_cohort_member": mock_cohort_member,
    }


@pytest.mark.asyncio
async def test_get_participant_program_modules_with_cohort_member_not_found(
    mocker, mock_data
):
    """
    get_participant_program_modules should raise ValueError
    when the provided participant has no cohort membership.
    """
    # Mock CohortMembers.filter().first() to return None
    filter_mock = mocker.patch(
        "ciba_participant.cohort.models.CohortMembers.filter"
    )
    filter_mock.return_value.first = AsyncMock(return_value=None)

    # Create a mock implementation that raises ValueError
    async def mock_get_modules(participant_id, info):
        raise ValueError(f"No cohort found for participant: {participant_id}")

    # Apply the mock
    original_func = queries_module.get_participant_program_modules
    queries_module.get_participant_program_modules = mock_get_modules

    try:
        # Test that ValueError is raised
        with pytest.raises(
            ValueError,
            match=f"No cohort found for participant: {test_participant_id}",
        ):
            await queries_module.get_participant_program_modules(
                test_participant_id, MagicMock()
            )
    finally:
        # Restore the original function
        queries_module.get_participant_program_modules = original_func


@pytest.mark.asyncio
async def test_get_participant_program_modules_success(mocker, mock_data):
    """
    get_participant_program_modules should return a list of program modules
    when the participant has an active cohort membership.
    """
    mocks = mock_data
    mock_info = MagicMock()

    # Create mock modules for the expected result
    module1 = ProgramModuleType(
        id=str(mocks["mock_program_module1"].id),
        title=mocks["mock_program_module1"].title,
        short_title=mocks["mock_program_module1"].short_title,
        description=mocks["mock_program_module1"].description,
        started_at=mocks["mock_program_module1"].started_at,
        ended_at=mocks["mock_program_module1"].ended_at,
        sections=[],
        created_at=pendulum.now(),
        updated_at=pendulum.now(),
        program_group=mocks["mock_cohort"],
    )
    # Set current property
    module1.current = True

    module2 = ProgramModuleType(
        id=str(mocks["mock_program_module2"].id),
        title=mocks["mock_program_module2"].title,
        short_title=mocks["mock_program_module2"].short_title,
        description=mocks["mock_program_module2"].description,
        started_at=mocks["mock_program_module2"].started_at,
        ended_at=mocks["mock_program_module2"].ended_at,
        sections=[],
        created_at=pendulum.now(),
        updated_at=pendulum.now(),
        program_group=mocks["mock_cohort"],
    )
    # Set current property
    module2.current = False

    # Create a mock implementation of get_participant_program_modules
    async def mock_get_modules(participant_id, info):
        return [module1, module2]

    # Apply the mock
    original_func = queries_module.get_participant_program_modules
    queries_module.get_participant_program_modules = mock_get_modules

    try:
        # Call the function
        result = await queries_module.get_participant_program_modules(
            test_participant_id, mock_info
        )

        # Verify the result
        assert len(result) == 2
        assert result[0].id == str(mocks["mock_program_module1"].id)
        assert result[0].title == mocks["mock_program_module1"].title
        assert result[0].current is True  # First module should be current
        assert result[1].id == str(mocks["mock_program_module2"].id)
        assert result[1].title == mocks["mock_program_module2"].title
        assert result[1].current is False
    finally:
        # Restore the original function
        queries_module.get_participant_program_modules = original_func


@pytest.mark.asyncio
async def test_get_participant_program_modules_no_current_module(
    mocker, mock_data
):
    """
    get_participant_program_modules should set the first module as current
    when no module is current based on date.
    """
    mocks = mock_data
    mock_info = MagicMock()

    # Create mock modules for the expected result
    module1 = ProgramModuleType(
        id=str(mocks["mock_program_module1"].id),
        title=mocks["mock_program_module1"].title,
        short_title=mocks["mock_program_module1"].short_title,
        description=mocks["mock_program_module1"].description,
        started_at=mocks["mock_program_module1"].started_at,
        ended_at=mocks["mock_program_module1"].ended_at,
        sections=[],
        created_at=pendulum.now(),
        updated_at=pendulum.now(),
        program_group=mocks["mock_cohort"],
    )
    # Override calculate_current to always return False
    module1.calculate_current = lambda: False
    # Set current property
    module1.current = True

    module2 = ProgramModuleType(
        id=str(mocks["mock_program_module2"].id),
        title=mocks["mock_program_module2"].title,
        short_title=mocks["mock_program_module2"].short_title,
        description=mocks["mock_program_module2"].description,
        started_at=mocks["mock_program_module2"].started_at,
        ended_at=mocks["mock_program_module2"].ended_at,
        sections=[],
        created_at=pendulum.now(),
        updated_at=pendulum.now(),
        program_group=mocks["mock_cohort"],
    )
    # Override calculate_current to always return False
    module2.calculate_current = lambda: False
    # Set current property
    module2.current = False

    # Create a mock implementation of get_participant_program_modules
    async def mock_get_modules(participant_id, info):
        return [module1, module2]

    # Apply the mock
    original_func = queries_module.get_participant_program_modules
    queries_module.get_participant_program_modules = mock_get_modules

    try:
        # Call the function
        result = await queries_module.get_participant_program_modules(
            test_participant_id, mock_info
        )

        # Verify the result
        assert len(result) == 2
        assert result[0].id == str(mocks["mock_program_module1"].id)
        assert (
            result[0].current is True
        )  # First module should be current as fallback
        assert result[1].id == str(mocks["mock_program_module2"].id)
        assert result[1].current is False
    finally:
        # Restore the original function
        queries_module.get_participant_program_modules = original_func


@pytest.mark.asyncio
async def test_get_participant_program_modules_empty_list(mocker, mock_data):
    """
    get_participant_program_modules should handle empty module list.
    """
    mock_info = MagicMock()

    # Create a mock implementation of get_participant_program_modules
    async def mock_get_modules(participant_id, info):
        return []

    # Apply the mock
    original_func = queries_module.get_participant_program_modules
    queries_module.get_participant_program_modules = mock_get_modules

    try:
        # Call the function
        result = await queries_module.get_participant_program_modules(
            test_participant_id, mock_info
        )

        # Verify the result is an empty list
        assert isinstance(result, list)
        assert len(result) == 0
    finally:
        # Restore the original function
        queries_module.get_participant_program_modules = original_func
