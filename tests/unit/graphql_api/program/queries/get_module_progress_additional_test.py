from unittest.mock import AsyncMock, MagicMock
from uuid import UUID

import pendulum
import pytest

from app.graphql_api.program.queries import get_module_progress
from app.graphql_api.program.types import ModuleProgressType
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityDevice,
)

test_participant_id = UUID("a8a6838b-e24d-48eb-9331-598eb274219e")
test_module_id = UUID("fd0d4c70-e665-4472-8386-51660c48a0d6")
test_started_at = pendulum.parse("2010-01-10")
test_ended_at = pendulum.parse("2010-01-20")


@pytest.fixture
def mock_data():
    """Fixture to prepare data for module progress"""
    # Create mock participant
    mock_participant = MagicMock()
    mock_participant.id = test_participant_id

    # Create mock cohort
    mock_cohort = MagicMock()
    mock_cohort.id = UUID("a8a6838b-e24d-48eb-9331-598eb274219f")
    mock_cohort.started_at = test_started_at

    # Create mock cohort member
    mock_cohort_member = MagicMock()
    mock_cohort_member.cohort_id = mock_cohort.id

    # Create mock cohort program module
    mock_cohort_module = MagicMock()
    mock_cohort_module.cohort_id = mock_cohort.id
    mock_cohort_module.program_module_id = test_module_id
    mock_cohort_module.started_at = test_started_at
    mock_cohort_module.ended_at = test_ended_at
    mock_cohort_module.cohort = mock_cohort

    # Create mock section
    mock_section = MagicMock()
    mock_section.id = UUID("a8a6838b-e24d-48eb-9331-598eb274219d")
    mock_section.activity_type = ParticipantActivityEnum.WEIGHT

    # Create mock activity
    mock_activity = MagicMock()
    mock_activity.activity_type = ParticipantActivityEnum.WEIGHT
    mock_activity.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    mock_activity.created_at = test_started_at.add(days=1)
    mock_activity.value = 70.0
    mock_activity.section_id = mock_section.id

    return {
        "mock_participant": mock_participant,
        "mock_cohort": mock_cohort,
        "mock_cohort_member": mock_cohort_member,
        "mock_cohort_module": mock_cohort_module,
        "mock_section": mock_section,
        "mock_activity": mock_activity,
    }


@pytest.mark.asyncio
async def test_get_module_progress_participant_not_found(mocker, mock_data):
    """
    get_module_progress should return None when participant is not found.
    """
    mock_info = MagicMock()

    # Mock Participant.filter to return None
    mocker.patch(
        "app.graphql_api.program.queries.Participant.filter",
        return_value=MagicMock(
            get=AsyncMock(side_effect=Exception("Participant not found"))
        ),
    )

    result = await get_module_progress(
        program_module_id=test_module_id,
        participant_id=test_participant_id,
        info=mock_info,
    )

    # Verify the result is None
    assert result is None


@pytest.mark.asyncio
async def test_get_module_progress_no_cohort(mocker, mock_data):
    """
    get_module_progress should return a basic ModuleProgressType when participant has no cohort.
    """
    mock_info = MagicMock()
    mocks = mock_data

    # Mock Participant.filter to return a participant
    mocker.patch(
        "app.graphql_api.program.queries.Participant.filter",
        return_value=MagicMock(
            get=AsyncMock(return_value=mocks["mock_participant"])
        ),
    )

    # Mock participant.cohorts to return an empty list
    mocker.patch.object(
        mocks["mock_participant"],
        "cohorts",
        [],
    )

    result = await get_module_progress(
        program_module_id=test_module_id,
        participant_id=test_participant_id,
        info=mock_info,
    )

    # Verify the result is None when participant has no cohort
    assert result is None


@pytest.mark.asyncio
async def test_get_module_progress_no_sections(mocker, mock_data):
    """
    get_module_progress should handle the case when no sections are found.
    """
    mock_info = MagicMock()
    mocks = mock_data

    # Mock Participant.filter to return a participant
    mocker.patch(
        "app.graphql_api.program.queries.Participant.filter",
        return_value=MagicMock(
            get=AsyncMock(return_value=mocks["mock_participant"])
        ),
    )

    # Mock fetch_related to be awaitable
    mocks["mock_participant"].fetch_related = AsyncMock()

    # Mock participant.cohorts to return a list with one cohort member
    mocker.patch.object(
        mocks["mock_participant"],
        "cohorts",
        [mocks["mock_cohort_member"]],
    )

    # Set up the cohort member with the correct status
    from ciba_participant.cohort.models import CohortMembershipStatus

    mocks["mock_cohort_member"].status = CohortMembershipStatus.ACTIVE
    mocks["mock_cohort_member"].cohort_id = mocks["mock_cohort"].id

    # Mock CohortProgramModules.filter to return a cohort module
    mocker.patch(
        "app.graphql_api.program.queries.CohortProgramModules.filter",
        return_value=MagicMock(
            prefetch_related=MagicMock(
                return_value=MagicMock(
                    get=AsyncMock(return_value=mocks["mock_cohort_module"])
                )
            )
        ),
    )

    # Mock render_module_with_sections to return empty sections
    mocker.patch(
        "app.graphql_api.program.queries.render_module_with_sections",
        return_value=([], MagicMock()),
    )

    # Mock calculate_weight_trend to return None, None
    mocker.patch(
        "app.graphql_api.program.queries.calculate_weight_trend",
        return_value=(None, None),
    )

    result = await get_module_progress(
        program_module_id=test_module_id,
        participant_id=test_participant_id,
        info=mock_info,
    )

    # Verify the result
    assert isinstance(result, ModuleProgressType)
    assert result.id == str(test_module_id)
    assert result.completed is False
    assert len(result.sections) == 0
    assert result.program_weight_trend is None
    assert result.previous_module_weight_trend is None
    assert (
        result.class_activities == []
    )  # Ensure class_activities is initialized
    assert (
        result.chat_activities == []
    )  # Ensure chat_activities is initialized
