from unittest.mock import AsyncMock, MagicMock, patch
from uuid import <PERSON>UID

import pendulum
import pytest

from app.graphql_api.program.queries import get_participant_program_courses
from app.graphql_api.program.types import ProgramCourseType

test_participant_id = UUID("a8a6838b-e24d-48eb-9331-598eb274219e")
test_program_id = UUID("fd0d4c70-e665-4472-8386-51660c48a0d6")
test_started_at = pendulum.parse("2010-01-10")


@pytest.fixture
def mock_data():
    """Fixture to prepare data for program courses"""
    # Create mock program
    mock_program1 = MagicMock()
    mock_program1.id = test_program_id
    mock_program1.title = "Test Program 1"
    mock_program1.description = "Test Description 1"
    mock_program1.started_at = test_started_at

    mock_program2 = MagicMock()
    mock_program2.id = UUID("fd0d4c70-e665-4472-8386-51660c48a0d7")
    mock_program2.title = "Test Program 2"
    mock_program2.description = "Test Description 2"
    mock_program2.started_at = test_started_at.add(days=30)

    # Set up Program.filter
    mock_filter = MagicMock()
    mock_all = MagicMock()
    mock_order_by = AsyncMock(return_value=[mock_program1, mock_program2])
    mock_all.order_by = mock_order_by
    mock_filter.all = MagicMock(return_value=mock_all)

    return {
        "mock_filter": mock_filter,
        "mock_program1": mock_program1,
        "mock_program2": mock_program2,
    }


@pytest.mark.asyncio
async def test_get_participant_program_courses_success(mocker, mock_data):
    """
    get_participant_program_courses should return a list of program courses
    when the participant is enrolled in programs.
    """
    mocks = mock_data

    mocker.patch(
        "app.graphql_api.program.queries.Program.filter",
        return_value=mocks["mock_filter"],
    )

    # Mock ProgramCourseType.marshal to return a ProgramCourseType
    async def mock_marshal(program):
        # Create a mock program for the nested program field
        mock_program = MagicMock()
        mock_program.id = program.id
        mock_program.title = program.title
        mock_program.description = program.description

        return ProgramCourseType(
            id=str(program.id),
            created_at=program.created_at,
            updated_at=program.updated_at,
            title=program.title,
            description=program.description,
            program=mock_program,
        )

    with patch.object(ProgramCourseType, "marshal", side_effect=mock_marshal):
        result = await get_participant_program_courses(test_participant_id)

        # Verify the result
        assert len(result) == 2
        assert result[0].id == str(mocks["mock_program1"].id)
        assert result[0].title == mocks["mock_program1"].title
        assert result[1].id == str(mocks["mock_program2"].id)
        assert result[1].title == mocks["mock_program2"].title


@pytest.mark.asyncio
async def test_get_participant_program_courses_empty_list(mocker, mock_data):
    """
    get_participant_program_courses should return an empty list
    when the participant is not enrolled in any programs.
    """
    # Override the program filter to return an empty list
    mock_filter = MagicMock()
    mock_all = MagicMock()
    mock_order_by = AsyncMock(return_value=[])
    mock_all.order_by = mock_order_by
    mock_filter.all = MagicMock(return_value=mock_all)

    mocker.patch(
        "app.graphql_api.program.queries.Program.filter",
        return_value=mock_filter,
    )

    result = await get_participant_program_courses(test_participant_id)

    # Verify the result is an empty list
    assert isinstance(result, list)
    assert len(result) == 0
