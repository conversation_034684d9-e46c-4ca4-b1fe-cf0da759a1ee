from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime, timezone

import pytest

from app.graphql_api.program.services import ProgressService
from app.graphql_api.program.inputs import PersonalSuccessInput
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ActivityUnit,
)
from ciba_participant.program.models import (
    ProgramModuleSection,
)


@pytest.mark.asyncio
async def test_make_progress_weight_type():
    """Test ProgressService.make_progress with weight type."""
    # Create mock section, participant_id, and data
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.activity_type = ParticipantActivityEnum.WEIGHT
    mock_section.id = uuid4()

    participant_id = uuid4()

    # Create a real dataclass instance instead of a mock
    mock_data = PersonalSuccessInput(value="75.5", response_id="123")

    # Mock ParticipantActivity.save
    with patch.object(
        ParticipantActivity, "save", new_callable=AsyncMock
    ) as mock_save:
        # Call the function
        result = await ProgressService.make_progress(
            program_section=mock_section,
            participant_id=participant_id,
            data=mock_data,
        )

        # Verify results
        assert isinstance(result, ParticipantActivity)
        # Check the participant field instead of participant_id
        assert result.participant_id == participant_id
        assert result.activity_type == ParticipantActivityEnum.WEIGHT
        assert result.activity_device == ParticipantActivityDevice.MANUAL_INPUT
        assert result.activity_category == ParticipantActivityCategory.WEIGHT
        assert result.value == "75.5"
        assert result.unit == ActivityUnit.LB
        assert result.section_id == mock_section.id
        assert result.live_session_id is None

        # Verify mock calls
        mock_save.assert_called_once()


@pytest.mark.asyncio
async def test_make_progress_group_type():
    """Test ProgressService.make_progress with group type."""
    # Create mock section, participant_id, and data
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.activity_type = ParticipantActivityEnum.GROUP
    mock_section.id = uuid4()

    participant_id = uuid4()

    # Create a real dataclass instance instead of a mock
    mock_data = PersonalSuccessInput(value="1")

    # Mock ParticipantActivity.save
    with patch.object(
        ParticipantActivity, "save", new_callable=AsyncMock
    ) as mock_save:
        # Call the function
        result = await ProgressService.make_progress(
            program_section=mock_section,
            participant_id=participant_id,
            data=mock_data,
        )

        # Verify results
        assert isinstance(result, ParticipantActivity)
        # Check the participant field instead of participant_id
        assert result.participant_id == participant_id
        assert result.activity_type == ParticipantActivityEnum.GROUP
        assert result.activity_device == ParticipantActivityDevice.MANUAL_INPUT
        assert result.activity_category == ParticipantActivityCategory.ACTIVITY
        assert result.value == "1"
        assert result.unit == ActivityUnit.ACTION
        assert result.section_id is None
        assert result.live_session_id == mock_section.id

        # Verify mock calls
        mock_save.assert_called_once()


@pytest.mark.asyncio
async def test_make_progress_other_type():
    """Test ProgressService.make_progress with other activity type."""
    # Create mock section, participant_id, and data
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.activity_type = ParticipantActivityEnum.ARTICLE
    mock_section.id = uuid4()

    participant_id = uuid4()

    # No data provided
    mock_data = None

    # Mock ParticipantActivity.save
    with patch.object(
        ParticipantActivity, "save", new_callable=AsyncMock
    ) as mock_save:
        # Call the function
        result = await ProgressService.make_progress(
            program_section=mock_section,
            participant_id=participant_id,
            data=mock_data,
        )

        # Verify results
        assert isinstance(result, ParticipantActivity)
        # Check the participant field instead of participant_id
        assert result.participant_id == participant_id
        assert result.activity_type == ParticipantActivityEnum.ARTICLE
        assert result.activity_device == ParticipantActivityDevice.MANUAL_INPUT
        assert result.activity_category == ParticipantActivityCategory.ACTIVITY
        assert result.value == "1"  # Default value
        assert result.unit == ActivityUnit.ACTION
        assert result.section_id == mock_section.id
        assert result.live_session_id is None

        # Verify mock calls
        mock_save.assert_called_once()


@pytest.mark.asyncio
async def test_make_progress_with_custom_date():
    """Test ProgressService.make_progress with custom date."""
    # Create mock section, participant_id, data, and custom_date
    mock_section = MagicMock(spec=ProgramModuleSection)
    mock_section.activity_type = ParticipantActivityEnum.ARTICLE
    mock_section.id = uuid4()

    participant_id = uuid4()
    mock_data = None
    custom_date = datetime.now(timezone.utc)

    # Mock ParticipantActivity.save
    with patch.object(
        ParticipantActivity, "save", new_callable=AsyncMock
    ) as mock_save:
        # Call the function
        result = await ProgressService.make_progress(
            program_section=mock_section,
            participant_id=participant_id,
            data=mock_data,
            custom_date=custom_date,
        )

        # Verify results
        assert isinstance(result, ParticipantActivity)
        assert result.created_at == custom_date

        # Verify mock calls
        mock_save.assert_called_once()
