import pytest
from unittest.mock import patch, MagicMock, call
import sys

# Clear any existing imports before starting tests
for module_name in list(sys.modules.keys()):
    if module_name.startswith("ciba_participant"):
        sys.modules.pop(module_name, None)


# Define test data and fixtures
@pytest.fixture
def mock_settings_class():
    """Mock the Settings class for testing"""

    class MockSettings:
        def __init__(self, **kwargs):
            # Set default values similar to the real Settings
            self.IS_LAMBDA = kwargs.get("IS_LAMBDA", 1)
            self.ENV = kwargs.get("ENV", "dev")
            self.IS_NEW_ENV = kwargs.get("IS_NEW_ENV", 1)
            self.POSTGRES_HOST = kwargs.get("POSTGRES_HOST", "127.0.0.1")
            self.POSTGRES_PORT = kwargs.get("POSTGRES_PORT", 5434)
            # Add all other kwargs as attributes
            for key, value in kwargs.items():
                setattr(self, key, value)

        @property
        def default_db_url(self):
            return f"postgres://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    return MockSettings


@pytest.fixture
def sample_parameters():
    """Sample parameter values for testing"""
    return {
        "POSTGRES_DB": "test_db",
        "POSTGRES_USER": "test_user",
        "POSTGRES_PASSWORD": "test_password",
        "AWS_BUCKET_NAME": "test-bucket",
    }


# Test the build_parameter_paths function
def test_build_parameter_paths_single_prefix():
    from ciba_participant.common.aws_handler import build_parameter_paths

    parameter_names = ["PARAM1", "PARAM2"]
    prefix = "/dev/participant"

    result = build_parameter_paths(parameter_names, prefix)

    assert result == ["/dev/participant/PARAM1", "/dev/participant/PARAM2"]


def test_build_parameter_paths_multiple_prefixes():
    from ciba_participant.common.aws_handler import build_parameter_paths

    parameter_names = ["PARAM1", "PARAM2"]
    prefixes = ["/dev/participant", "/global"]

    result = build_parameter_paths(parameter_names, prefixes)

    expected = [
        "/dev/participant/PARAM1",
        "/global/PARAM1",
        "/dev/participant/PARAM2",
        "/global/PARAM2",
    ]
    assert sorted(result) == sorted(expected)


def test_build_parameter_paths_with_fallback():
    from ciba_participant.common.aws_handler import build_parameter_paths

    parameter_names = ["PARAM1"]
    prefixes = "/dev/participant"
    fallback = "/default"

    result = build_parameter_paths(parameter_names, prefixes, fallback)

    assert result == ["/dev/participant/PARAM1", "/default/PARAM1"]


# Test the get_parameters_batch function
@patch("boto3.client")
def test_get_parameters_batch_success(mock_boto_client):
    from ciba_participant.common.aws_handler import get_parameters_batch

    # Setup mock response
    mock_ssm = MagicMock()
    mock_boto_client.return_value = mock_ssm

    mock_ssm.get_parameters.return_value = {
        "Parameters": [
            {"Name": "/dev/participant/PARAM1", "Value": "value1"},
            {"Name": "/dev/participant/PARAM2", "Value": "value2"},
        ],
        "InvalidParameters": [],
    }

    parameter_names = ["/dev/participant/PARAM1", "/dev/participant/PARAM2"]
    result = get_parameters_batch(parameter_names)

    assert result == {"PARAM1": "value1", "PARAM2": "value2"}
    mock_boto_client.assert_called_once_with("ssm", region_name="us-east-2")
    mock_ssm.get_parameters.assert_called_once_with(
        Names=parameter_names, WithDecryption=True
    )


@patch("boto3.client")
def test_get_parameters_batch_with_invalid_params(mock_boto_client):
    from ciba_participant.common.aws_handler import get_parameters_batch

    # Setup mock response
    mock_ssm = MagicMock()
    mock_boto_client.return_value = mock_ssm

    mock_ssm.get_parameters.return_value = {
        "Parameters": [{"Name": "/dev/participant/PARAM1", "Value": "value1"}],
        "InvalidParameters": ["/dev/participant/INVALID_PARAM"],
    }

    parameter_names = ["/dev/participant/PARAM1", "/dev/participant/INVALID_PARAM"]
    result = get_parameters_batch(parameter_names)

    assert result == {"PARAM1": "value1"}
    mock_boto_client.assert_called_once_with("ssm", region_name="us-east-2")


@patch("boto3.client")
def test_get_parameters_batch_large_batch(mock_boto_client):
    from ciba_participant.common.aws_handler import get_parameters_batch

    # Setup mock for handling batches > 10 parameters
    mock_ssm = MagicMock()
    mock_boto_client.return_value = mock_ssm

    # Create mock responses for two batches
    mock_ssm.get_parameters.side_effect = [
        {
            "Parameters": [
                {"Name": f"/dev/participant/PARAM{i}", "Value": f"value{i}"}
                for i in range(1, 11)
            ],
            "InvalidParameters": [],
        },
        {
            "Parameters": [
                {"Name": f"/dev/participant/PARAM{i}", "Value": f"value{i}"}
                for i in range(11, 15)
            ],
            "InvalidParameters": [],
        },
    ]

    # Create a list of 14 parameters
    parameter_names = [f"/dev/participant/PARAM{i}" for i in range(1, 15)]
    result = get_parameters_batch(parameter_names)

    # Check if all 14 parameters are in the result
    expected = {f"PARAM{i}": f"value{i}" for i in range(1, 15)}
    assert result == expected

    # Check if get_parameters was called twice (first with 10 params, then with 4)
    assert mock_ssm.get_parameters.call_count == 2
    mock_ssm.get_parameters.assert_has_calls(
        [
            call(Names=parameter_names[:10], WithDecryption=True),
            call(Names=parameter_names[10:], WithDecryption=True),
        ]
    )


@patch("boto3.client")
def test_get_parameters_batch_exception(mock_boto_client):
    from ciba_participant.common.aws_handler import get_parameters_batch

    # Setup mock to raise an exception
    mock_ssm = MagicMock()
    mock_boto_client.return_value = mock_ssm
    mock_ssm.get_parameters.side_effect = Exception("AWS error")

    parameter_names = ["/dev/participant/PARAM1", "/dev/participant/PARAM2"]
    result = get_parameters_batch(parameter_names)

    # Should return an empty dict on error
    assert result == {}


# Test the load_specific_parameters function
@patch("ciba_participant.common.aws_handler.get_parameters_batch")
@patch("ciba_participant.common.aws_handler.build_parameter_paths")
def test_load_specific_parameters(mock_build_paths, mock_get_batch):
    from ciba_participant.common.aws_handler import load_specific_parameters

    parameter_names = ["PARAM1", "PARAM2"]
    mock_build_paths.return_value = [
        "/dev/participant/PARAM1",
        "/dev/participant/PARAM2",
    ]
    mock_get_batch.return_value = {"PARAM1": "value1", "PARAM2": "value2"}

    result = load_specific_parameters(
        parameter_names=parameter_names, env="dev", service_prefix="participant"
    )

    assert result == {"PARAM1": "value1", "PARAM2": "value2"}
    mock_build_paths.assert_called_once()
    mock_get_batch.assert_called_once_with(
        ["/dev/participant/PARAM1", "/dev/participant/PARAM2"], region_name="us-east-2"
    )


@patch("ciba_participant.common.aws_handler.get_parameters_batch")
@patch("ciba_participant.common.aws_handler.build_parameter_paths")
def test_load_specific_parameters_with_global(mock_build_paths, mock_get_batch):
    from ciba_participant.common.aws_handler import load_specific_parameters

    parameter_names = ["PARAM1", "GLOBAL_PARAM"]
    mock_build_paths.return_value = [
        "/dev/participant/PARAM1",
        "/global/PARAM1",
        "/dev/participant/GLOBAL_PARAM",
        "/global/GLOBAL_PARAM",
    ]
    mock_get_batch.return_value = {"PARAM1": "value1", "GLOBAL_PARAM": "global_value"}

    result = load_specific_parameters(
        parameter_names=parameter_names,
        env="dev",
        service_prefix="participant",
        global_params_prefix="/global",
    )

    assert result == {"PARAM1": "value1", "GLOBAL_PARAM": "global_value"}
    mock_build_paths.assert_called_once()


# Test the get_settings function
@patch("ciba_participant.settings.load_specific_parameters")
def test_get_settings_local_env(mock_load_params):
    from ciba_participant.settings import (
        get_settings,
        ENV,
        reset_cached_settings,
    )

    # Reset global variables in the settings module
    reset_cached_settings()

    # Test when ENV is LOCAL
    settings = get_settings(ENV=ENV.LOCAL)

    # Should not try to load parameters
    mock_load_params.assert_not_called()
    assert settings.ENV == ENV.LOCAL


@patch("ciba_participant.settings.load_specific_parameters")
def test_get_settings_lambda_env(mock_load_params):
    from ciba_participant.settings import get_settings, ENV, reset_cached_settings

    # Reset global variables
    reset_cached_settings()

    # Setup mock return value
    mock_load_params.return_value = {
        "POSTGRES_DB": "prod_db",
        "POSTGRES_USER": "prod_user",
    }

    # Test with lambda in production
    settings = get_settings(IS_LAMBDA=1, ENV=ENV.PROD)

    # Should load parameters
    mock_load_params.assert_called_once()
    assert settings.POSTGRES_DB == "prod_db"
    assert settings.POSTGRES_USER == "prod_user"


@patch("ciba_participant.settings.load_specific_parameters")
def test_get_settings_cached(mock_load_params, mock_settings_class):
    from ciba_participant.settings import (
        get_settings,
        reset_cached_settings,
        settings_manager,
    )

    # Reset global variables
    reset_cached_settings()

    # Mock Settings class
    with (
        patch.object(settings_manager, "_settings", None),
        patch("ciba_participant.settings.Settings", mock_settings_class),
    ):
        cached_settings = mock_settings_class(
            IS_LAMBDA=1, ENV="prod", POSTGRES_DB="cached_db"
        )

        # Manually set the cached settings in the manager
        settings_manager._settings = cached_settings
        settings_manager._parameters_pulled = True

        # Test with cached settings
        settings = get_settings(IS_LAMBDA=1, ENV="prod")

        # Should not load parameters again
        mock_load_params.assert_not_called()
        assert settings.POSTGRES_DB == "cached_db"


@patch("ciba_participant.settings.load_specific_parameters")
def test_get_settings_with_override(mock_load_params, mock_settings_class):
    from ciba_participant.settings import (
        get_settings,
        reset_cached_settings,
        settings_manager,
    )

    # Reset cached settings before the test
    reset_cached_settings()

    # Mock Settings class
    with (
        patch.object(settings_manager, "_settings", None),
        patch("ciba_participant.settings.Settings", mock_settings_class),
    ):
        cached_settings = mock_settings_class(
            IS_LAMBDA=1, ENV="prod", POSTGRES_DB="cached_db"
        )

        # Manually set the cached settings in the manager
        settings_manager._settings = cached_settings
        settings_manager._parameters_pulled = True

        # Test with override
        settings = get_settings(IS_LAMBDA=1, ENV="prod", POSTGRES_DB="override_db")

        # Ensure load_specific_parameters was not called (since we're using cache)
        mock_load_params.assert_not_called()

        # Check if the override was applied correctly
        assert settings.POSTGRES_DB == "override_db"


@patch("ciba_participant.settings.load_specific_parameters")
def test_get_settings_test_env(mock_load_params):
    from ciba_participant.settings import get_settings, ENV, reset_cached_settings

    # Reset global variables
    reset_cached_settings()

    # Test when ENV is TEST
    settings = get_settings(ENV=ENV.TEST, IS_LAMBDA=1)

    # Should not try to load parameters for TEST environment
    mock_load_params.assert_not_called()
    assert settings.ENV == ENV.TEST
