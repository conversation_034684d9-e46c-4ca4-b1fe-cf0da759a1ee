import string
from unittest.mock import patch

import pytest

from ciba_participant.participant.utils import (
    generate_random_password,
    CouldNotFindValidPassword,
)


class TestGenerateRandomPassword:
    """Test suite for the generate_random_password function."""

    def test_generate_random_password_default_length(self):
        """Test password generation with default length (12 characters)."""
        # Act
        password = generate_random_password()

        # Assert
        assert isinstance(password, str)
        assert len(password) == 12
        self._assert_password_requirements(password)

    def test_generate_random_password_custom_length(self):
        """Test password generation with custom length."""
        # Arrange
        custom_length = 16

        # Act
        password = generate_random_password(pwd_length=custom_length)

        # Assert
        assert isinstance(password, str)
        assert len(password) == custom_length
        self._assert_password_requirements(password)

    def test_generate_random_password_minimum_length(self):
        """Test password generation with minimum viable length (4 characters)."""
        # Arrange
        min_length = 4

        # Act
        password = generate_random_password(pwd_length=min_length)

        # Assert
        assert isinstance(password, str)
        assert len(password) == min_length
        self._assert_password_requirements(password)

    def test_generate_random_password_long_length(self):
        """Test password generation with long length."""
        # Arrange
        long_length = 50

        # Act
        password = generate_random_password(pwd_length=long_length)

        # Assert
        assert isinstance(password, str)
        assert len(password) == long_length
        self._assert_password_requirements(password)

    def test_generate_random_password_uniqueness(self):
        """Test that multiple password generations produce unique results."""
        # Act
        passwords = [generate_random_password() for _ in range(10)]

        # Assert
        # All passwords should be unique
        assert len(set(passwords)) == len(passwords)

        # All passwords should meet requirements
        for password in passwords:
            self._assert_password_requirements(password)

    def test_generate_random_password_character_distribution(self):
        """Test that generated passwords contain characters from all required categories."""
        # Arrange
        password_length = 20
        num_tests = 50

        # Act & Assert
        for _ in range(num_tests):
            password = generate_random_password(pwd_length=password_length)

            # Verify all character types are present
            has_lowercase = any(char in string.ascii_lowercase for char in password)
            has_uppercase = any(char in string.ascii_uppercase for char in password)
            has_digit = any(char in string.digits for char in password)
            has_special = any(char in string.punctuation for char in password)

            assert has_lowercase, f"Password missing lowercase: {password}"
            assert has_uppercase, f"Password missing uppercase: {password}"
            assert has_digit, f"Password missing digit: {password}"
            assert has_special, f"Password missing special character: {password}"

    def test_generate_random_password_edge_case_length_one(self):
        """Test password generation with length 1 (should eventually succeed or fail gracefully)."""
        # This test might fail due to the requirement of having all 4 character types
        # in a single character, which is impossible. The function should raise an exception.
        with pytest.raises(CouldNotFindValidPassword):
            generate_random_password(pwd_length=1)

    def test_generate_random_password_edge_case_length_two(self):
        """Test password generation with length 2 (should fail due to requirements)."""
        # With only 2 characters, it's impossible to have all 4 required character types
        with pytest.raises(CouldNotFindValidPassword):
            generate_random_password(pwd_length=2)

    def test_generate_random_password_edge_case_length_three(self):
        """Test password generation with length 3 (should fail due to requirements)."""
        # With only 3 characters, it's impossible to have all 4 required character types
        with pytest.raises(CouldNotFindValidPassword):
            generate_random_password(pwd_length=3)

    def test_generate_random_password_zero_length(self):
        """Test password generation with zero length."""
        # Act & Assert
        with pytest.raises(CouldNotFindValidPassword):
            generate_random_password(pwd_length=0)

    def test_generate_random_password_negative_length(self):
        """Test password generation with negative length."""
        # Act & Assert
        with pytest.raises(CouldNotFindValidPassword):
            generate_random_password(pwd_length=-1)

    @patch("ciba_participant.participant.utils.secrets.choice")
    def test_generate_random_password_max_attempts_exceeded(self, mock_choice):
        """Test that CouldNotFindValidPassword is raised when max attempts are exceeded."""
        # Arrange
        # Mock secrets.choice to always return lowercase 'a' to ensure password never meets requirements
        mock_choice.return_value = "a"

        # Act & Assert
        with pytest.raises(CouldNotFindValidPassword):
            generate_random_password(pwd_length=12)

        # Verify that secrets.choice was called the expected number of times
        # 100 attempts * 12 characters per attempt = 1200 calls
        assert mock_choice.call_count == 1200

    @patch("ciba_participant.participant.utils.secrets.choice")
    def test_generate_random_password_succeeds_on_retry(self, mock_choice):
        """Test that password generation succeeds after initial failures."""
        # Arrange
        # First 99 attempts return only lowercase 'a', 100th attempt returns valid mix
        call_count = 0

        def side_effect_choice(alphabet):
            nonlocal call_count
            call_count += 1

            # For the first 99 attempts (99 * 12 = 1188 calls), return only 'a'
            if call_count <= 1188:
                return "a"

            # For the 100th attempt, return a mix of character types
            char_position = (call_count - 1189) % 12
            if char_position == 0:
                return "A"  # uppercase
            elif char_position == 1:
                return "a"  # lowercase
            elif char_position == 2:
                return "1"  # digit
            elif char_position == 3:
                return "!"  # special
            else:
                return "a"  # fill rest with lowercase

        mock_choice.side_effect = side_effect_choice

        # Act
        password = generate_random_password(pwd_length=12)

        # Assert
        assert isinstance(password, str)
        assert len(password) == 12
        self._assert_password_requirements(password)

    def test_generate_random_password_uses_all_character_sets(self):
        """Test that the function uses all expected character sets."""
        # This test verifies the alphabet composition
        # We can't directly test the internal alphabet variable, but we can verify
        # that generated passwords can contain characters from all expected sets

        # Arrange
        expected_chars = (
            string.ascii_lowercase
            + string.ascii_uppercase
            + string.digits
            + string.punctuation
        )

        # Act - Generate many passwords to increase chance of seeing all character types
        passwords = [generate_random_password(pwd_length=20) for _ in range(100)]
        all_chars_used = set("".join(passwords))

        # Assert
        # Check that we have characters from each required category
        lowercase_found = any(char in string.ascii_lowercase for char in all_chars_used)
        uppercase_found = any(char in string.ascii_uppercase for char in all_chars_used)
        digit_found = any(char in string.digits for char in all_chars_used)
        special_found = any(char in string.punctuation for char in all_chars_used)

        assert lowercase_found, "No lowercase characters found in generated passwords"
        assert uppercase_found, "No uppercase characters found in generated passwords"
        assert digit_found, "No digits found in generated passwords"
        assert special_found, "No special characters found in generated passwords"

        # Verify no unexpected characters are used
        for char in all_chars_used:
            assert char in expected_chars, f"Unexpected character found: {char}"

    def test_generate_random_password_consistent_behavior(self):
        """Test that the function behaves consistently across multiple calls."""
        # Arrange
        test_length = 15
        num_tests = 20

        # Act & Assert
        for _ in range(num_tests):
            password = generate_random_password(pwd_length=test_length)

            # Each password should meet the same requirements
            assert len(password) == test_length
            self._assert_password_requirements(password)

    @pytest.mark.parametrize("length", [4, 8, 12, 16, 20, 24, 32])
    def test_generate_random_password_various_lengths(self, length):
        """Test password generation with various valid lengths."""
        # Act
        password = generate_random_password(pwd_length=length)

        # Assert
        assert isinstance(password, str)
        assert len(password) == length
        self._assert_password_requirements(password)

    def _assert_password_requirements(self, password: str):
        """Helper method to assert that a password meets all requirements."""
        # Check that password contains at least one character from each required category
        has_lowercase = any(char in string.ascii_lowercase for char in password)
        has_uppercase = any(char in string.ascii_uppercase for char in password)
        has_digit = any(char in string.digits for char in password)
        has_special = any(char in string.punctuation for char in password)

        assert has_lowercase, f"Password missing lowercase letter: {password}"
        assert has_uppercase, f"Password missing uppercase letter: {password}"
        assert has_digit, f"Password missing digit: {password}"
        assert has_special, f"Password missing special character: {password}"

        # Verify all characters are from expected alphabet
        expected_chars = (
            string.ascii_lowercase
            + string.ascii_uppercase
            + string.digits
            + string.punctuation
        )
        for char in password:
            assert char in expected_chars, f"Unexpected character in password: {char}"


class TestCouldNotFindValidPasswordException:
    """Test suite for the CouldNotFindValidPassword exception."""

    def test_exception_can_be_raised(self):
        """Test that the exception can be raised and caught."""
        # Act & Assert
        with pytest.raises(CouldNotFindValidPassword):
            raise CouldNotFindValidPassword()

    def test_exception_with_message(self):
        """Test that the exception can be raised with a custom message."""
        # Arrange
        error_message = "Custom error message"

        # Act & Assert
        with pytest.raises(CouldNotFindValidPassword, match=error_message):
            raise CouldNotFindValidPassword(error_message)

    def test_exception_inheritance(self):
        """Test that CouldNotFindValidPassword inherits from Exception."""
        # Act
        exception = CouldNotFindValidPassword()

        # Assert
        assert isinstance(exception, Exception)
        assert issubclass(CouldNotFindValidPassword, Exception)
