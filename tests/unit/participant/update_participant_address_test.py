import uuid
from contextlib import asynccontextmanager
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest

from ciba_participant.participant.models import ParticipantMeta
from ciba_participant.participant.pydantic_models import (
    ParticipantAddress,
    ParticipantMetaPydantic,
)

# Import at module level to ensure consistent patching
from ciba_participant.participant.crud import ParticipantMetaRepository


@pytest.fixture
def mock_participant_meta():
    """Create a mock ParticipantMeta for testing."""
    participant_id = uuid.uuid4()
    participant_meta = MagicMock()
    participant_meta.id = uuid.uuid4()
    participant_meta.participant_id = participant_id
    participant_meta.metadata = {"phone_number": "************", "enrolled": True}
    participant_meta.save = AsyncMock()
    return participant_meta


@pytest.fixture
def mock_participant_meta_empty():
    """Create a mock ParticipantMeta with empty metadata for testing."""
    participant_id = uuid.uuid4()
    participant_meta = MagicMock()
    participant_meta.id = uuid.uuid4()
    participant_meta.participant_id = participant_id
    participant_meta.metadata = None
    participant_meta.save = AsyncMock()
    return participant_meta


@pytest.fixture
def sample_address():
    """Create a sample ParticipantAddress for testing."""
    return ParticipantAddress(
        street1="123 Main St",
        street2="Apt 4B",
        zipCode="12345",
        city="Anytown",
        state="CA",
    )


@pytest.fixture
def sample_address_minimal():
    """Create a minimal ParticipantAddress for testing."""
    return ParticipantAddress(
        street1="456 Oak Ave", zipCode="67890", city="Springfield", state="NY"
    )


@asynccontextmanager
async def setup_update_address_mocks(mock_participant_meta=None):
    """Set up common mocks for update_participant_address tests."""
    with (
        patch.object(
            ParticipantMeta, "get_or_none", new_callable=AsyncMock
        ) as mock_get,
        patch.object(
            ParticipantMetaPydantic, "from_orm", new_callable=AsyncMock
        ) as mock_from_orm,
    ):
        # Configure mocks
        mock_get.return_value = mock_participant_meta
        expected_result = MagicMock()
        mock_from_orm.return_value = expected_result

        yield {
            "get": mock_get,
            "from_orm": mock_from_orm,
            "expected_result": expected_result,
        }


def get_expected_address_dict(address: ParticipantAddress) -> dict:
    """Helper to get expected address dictionary from ParticipantAddress."""
    return address.model_dump(exclude_unset=True)


def assert_successful_update(
    mocks, participant_meta, participant_id, expected_metadata
):
    """Helper to assert successful address update."""
    assert mocks["get"].call_count == 1
    mocks["get"].assert_called_with(participant_id=participant_id)
    assert participant_meta.metadata == expected_metadata
    participant_meta.save.assert_awaited_once()
    mocks["from_orm"].assert_called_once_with(participant_meta)


@pytest.mark.asyncio
async def test_update_participant_address_success(
    mock_participant_meta, sample_address
):
    """Test successful participant address update with existing metadata."""
    # Arrange
    participant_id = mock_participant_meta.participant_id

    async with setup_update_address_mocks(mock_participant_meta) as mocks:
        # Act
        result = await ParticipantMetaRepository.update_participant_address(
            participant_id, sample_address
        )

        # Assert
        assert result == mocks["expected_result"]
        expected_address = get_expected_address_dict(sample_address)
        expected_metadata = {
            "phone_number": "************",
            "enrolled": True,
            "address": expected_address,
        }
        assert_successful_update(
            mocks, mock_participant_meta, participant_id, expected_metadata
        )


@pytest.mark.asyncio
async def test_update_participant_address_empty_metadata(
    mock_participant_meta_empty, sample_address
):
    """Test participant address update when metadata is initially empty."""
    # Arrange
    participant_id = mock_participant_meta_empty.participant_id

    async with setup_update_address_mocks(mock_participant_meta_empty) as mocks:
        # Act
        result = await ParticipantMetaRepository.update_participant_address(
            participant_id, sample_address
        )

        # Assert
        assert result == mocks["expected_result"]
        expected_address = get_expected_address_dict(sample_address)
        expected_metadata = {"address": expected_address}
        assert_successful_update(
            mocks, mock_participant_meta_empty, participant_id, expected_metadata
        )


@pytest.mark.asyncio
async def test_update_participant_address_minimal_address(
    mock_participant_meta, sample_address_minimal
):
    """Test participant address update with minimal address data."""
    # Arrange
    participant_id = mock_participant_meta.participant_id

    async with setup_update_address_mocks(mock_participant_meta) as mocks:
        # Act
        result = await ParticipantMetaRepository.update_participant_address(
            participant_id, sample_address_minimal
        )

        # Assert
        assert result == mocks["expected_result"]
        # Note: street2 is not included because it wasn't explicitly set and exclude_unset=True
        expected_address = get_expected_address_dict(sample_address_minimal)
        expected_metadata = {
            "phone_number": "************",
            "enrolled": True,
            "address": expected_address,
        }
        assert_successful_update(
            mocks, mock_participant_meta, participant_id, expected_metadata
        )


@pytest.mark.asyncio
async def test_update_participant_address_participant_not_found(sample_address):
    """Test participant address update when participant meta doesn't exist."""
    # Arrange
    participant_id = uuid.uuid4()

    with patch.object(
        ParticipantMeta, "get_or_none", new_callable=AsyncMock
    ) as mock_get:
        # Configure mock to return None (participant not found)
        mock_get.return_value = None

        # Act
        result = await ParticipantMetaRepository.update_participant_address(
            participant_id, sample_address
        )

        # Assert
        assert result is None
        mock_get.assert_called_once_with(participant_id=participant_id)


@pytest.mark.asyncio
async def test_update_participant_address_existing_address_update(
    mock_participant_meta, sample_address
):
    """Test participant address update when address already exists in metadata."""
    # Arrange
    participant_id = mock_participant_meta.participant_id
    # Set up existing address in metadata
    mock_participant_meta.metadata = {
        "phone_number": "************",
        "enrolled": True,
        "address": {
            "street1": "Old Street",
            "street2": "Old Apt",
            "zipCode": "00000",
            "city": "Old City",
            "state": "XX",
        },
    }

    async with setup_update_address_mocks(mock_participant_meta) as mocks:
        # Act
        result = await ParticipantMetaRepository.update_participant_address(
            participant_id, sample_address
        )

        # Assert
        assert result == mocks["expected_result"]
        # Verify address was updated (not just added)
        expected_address = get_expected_address_dict(sample_address)
        expected_metadata = {
            "phone_number": "************",
            "enrolled": True,
            "address": expected_address,
        }
        assert_successful_update(
            mocks, mock_participant_meta, participant_id, expected_metadata
        )


@pytest.mark.asyncio
async def test_update_participant_address_no_address_key(
    mock_participant_meta, sample_address
):
    """Test participant address update when address key doesn't exist in metadata."""
    # Arrange
    participant_id = mock_participant_meta.participant_id
    # Set up metadata without address key
    mock_participant_meta.metadata = {
        "phone_number": "************",
        "enrolled": True,
        # No "address" key
    }

    async with setup_update_address_mocks(mock_participant_meta) as mocks:
        # Act
        result = await ParticipantMetaRepository.update_participant_address(
            participant_id, sample_address
        )

        # Assert
        assert result == mocks["expected_result"]
        # Verify address was added correctly
        expected_address = get_expected_address_dict(sample_address)
        expected_metadata = {
            "phone_number": "************",
            "enrolled": True,
            "address": expected_address,
        }
        assert_successful_update(
            mocks, mock_participant_meta, participant_id, expected_metadata
        )


@pytest.mark.asyncio
async def test_update_participant_address_invalid_address_type(
    mock_participant_meta, sample_address
):
    """Test participant address update when address field is not a dict."""
    # Arrange
    participant_id = mock_participant_meta.participant_id
    # Set up metadata with address as a non-dict value
    mock_participant_meta.metadata = {
        "phone_number": "************",
        "enrolled": True,
        "address": "invalid_address_string",  # This should be replaced with a dict
    }

    async with setup_update_address_mocks(mock_participant_meta) as mocks:
        # Act
        result = await ParticipantMetaRepository.update_participant_address(
            participant_id, sample_address
        )

        # Assert
        assert result == mocks["expected_result"]
        # Verify address was replaced with proper dict structure
        expected_address = get_expected_address_dict(sample_address)
        expected_metadata = {
            "phone_number": "************",
            "enrolled": True,
            "address": expected_address,
        }
        assert_successful_update(
            mocks, mock_participant_meta, participant_id, expected_metadata
        )


@pytest.mark.asyncio
async def test_update_participant_address_save_exception(
    mock_participant_meta, sample_address
):
    """Test handling of exceptions during participant address update."""
    # Arrange
    participant_id = mock_participant_meta.participant_id
    mock_participant_meta.save.side_effect = Exception("Database error")

    with patch.object(
        ParticipantMeta, "get_or_none", new_callable=AsyncMock
    ) as mock_get:
        # Configure mock
        mock_get.return_value = mock_participant_meta

        # Act & Assert
        with pytest.raises(Exception, match="Database error"):
            await ParticipantMetaRepository.update_participant_address(
                participant_id, sample_address
            )

        mock_get.assert_called_once_with(participant_id=participant_id)
        mock_participant_meta.save.assert_awaited_once()
