import base64
import hashlib
import string
from unittest.mock import patch, MagicMock

import pytest
from Crypto.Cipher import AES

from app.utils import (
    AESCipher,
    decrypt,
    generate_random_password,
    CouldNotFindValidPassword,
)


class TestAESCipher:
    def test_init(self):
        """Test AESCipher initialization."""
        key = "test_key"
        cipher = AESCipher(key)

        # Check that the key is properly hashed
        expected_key = hashlib.sha256(key.encode()).digest()
        assert cipher.key == expected_key
        assert cipher.block_size == AES.block_size
        assert cipher.mode == AES.MODE_CBC

    def test_encrypt_decrypt(self):
        """Test that encryption and decryption work correctly."""
        key = "test_key"
        value = "test_value"

        cipher = AESCipher(key)
        encrypted = cipher.encrypt(value)
        decrypted = cipher.decrypt(encrypted)

        assert decrypted == value

    def test_decrypt_with_invalid_data(self):
        """Test that decryption with invalid data raises an exception."""
        key = "test_key"
        cipher = AESCipher(key)

        with pytest.raises(ValueError):
            cipher.decrypt(b"invalid_data")


@patch("app.utils.EncryptionSDKClient")
@patch("app.utils.StrictAwsKmsMasterKeyProvider")
def test_decrypt(mock_provider_class, mock_client_class, monkeypatch):
    """Test the decrypt function."""
    # Setup mocks
    mock_client = MagicMock()
    mock_client_class.return_value = mock_client

    mock_provider = MagicMock()
    mock_provider_class.return_value = mock_provider

    # Mock the decryption result
    decrypted_value = b"decrypted_value"
    decrypted_header = {"header": "value"}
    mock_client.decrypt.return_value = (decrypted_value, decrypted_header)

    # Mock settings
    monkeypatch.setattr("app.utils.settings.COGNITO_KMS_KEY_ARN", "test_arn")

    # Test with a base64 encoded string
    ciphertext = base64.b64encode(b"encrypted_data").decode()
    result, header = decrypt(ciphertext)

    # Verify the result
    assert result == "decrypted_value"
    assert header == decrypted_header

    # Verify the mocks were called correctly
    # We don't need to check the exact commitment_policy value, just that the function was called
    assert mock_client_class.call_count == 1
    mock_provider_class.assert_called_once_with(key_ids=["test_arn"])
    mock_client.decrypt.assert_called_once_with(
        source=base64.b64decode(ciphertext), key_provider=mock_provider
    )


@patch("app.utils.secrets.choice")
def test_generate_random_password(mock_choice):
    """Test the generate_random_password function."""
    # Setup mock to return predictable values
    mock_choice.side_effect = [
        "a",
        "B",
        "1",
        "#",  # First 4 chars cover all required types
        "x",
        "y",
        "z",
        "w",
        "v",
        "u",
        "t",
        "s",  # Remaining chars
    ]

    # Test with default length
    password = generate_random_password()
    assert len(password) == 12
    assert any(c in string.ascii_lowercase for c in password)
    assert any(c in string.ascii_uppercase for c in password)
    assert any(c in string.digits for c in password)
    assert any(c in string.punctuation for c in password)

    # Test with custom length
    mock_choice.reset_mock()
    mock_choice.side_effect = [
        "a",
        "B",
        "1",
        "#",  # First 4 chars cover all required types
        "x",
        "y",
        "z",
        "w",  # Remaining chars
    ]

    password = generate_random_password(8)
    assert len(password) == 8


@patch("app.utils.secrets.choice")
def test_generate_random_password_exception(mock_choice):
    """Test that CouldNotFindValidPassword is raised when no valid password can be generated."""
    # Setup mock to always return the same character, which won't satisfy the requirements
    mock_choice.return_value = "a"

    with pytest.raises(CouldNotFindValidPassword):
        generate_random_password()
