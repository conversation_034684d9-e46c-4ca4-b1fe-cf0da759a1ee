import pytest

from app.helper.validations.pagination import validate_pagination_params


@pytest.mark.parametrize(
    "page, per_page",
    [
        (0, 10),
        (-1, 10),
        (1, 0),
        (1, -5),
        (0, 0),
        (-1, -1),
    ],
)
def test_invalid_pagination_params_parametrized(page, per_page):
    """
    Test all invalid cases using pytest.mark.parametrize for conciseness.
    """
    with pytest.raises(ValueError) as expected_error:
        validate_pagination_params(page, per_page)

    assert (
        expected_error.value.args[0]
        == "Pagination parameters must be greater than 0"
    )
