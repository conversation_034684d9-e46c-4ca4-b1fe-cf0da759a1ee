from datetime import datetime, timezone

import pytest

from app.helper.timeconvert import from_utc_to_pst


def test_from_utc_to_pst_with_naive_datetime():
    """Test conversion from naive UTC datetime to PST."""
    # Create a naive datetime (no timezone info)
    naive_dt = datetime(2023, 1, 1, 12, 0, 0)

    # Convert to PST
    pst_dt = from_utc_to_pst(naive_dt)

    # Verify the result
    assert pst_dt.tzinfo is not None  # Should now have timezone info
    assert pst_dt.tzname() == "PST"  # Should be in PST timezone

    # PST is UTC-8, so 12:00 UTC should be 04:00 PST
    assert pst_dt.hour == 4
    assert pst_dt.minute == 0
    assert pst_dt.day == 1
    assert pst_dt.month == 1
    assert pst_dt.year == 2023


def test_from_utc_to_pst_with_aware_datetime():
    """Test conversion from timezone-aware UTC datetime to PST."""
    # Create a timezone-aware datetime in UTC
    aware_dt = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)

    # Convert to PST
    pst_dt = from_utc_to_pst(aware_dt)

    # Verify the result
    assert pst_dt.tzinfo is not None  # Should have timezone info
    assert pst_dt.tzname() == "PST"  # Should be in PST timezone

    # PST is UTC-8, so 12:00 UTC should be 04:00 PST
    assert pst_dt.hour == 4
    assert pst_dt.minute == 0
    assert pst_dt.day == 1
    assert pst_dt.month == 1
    assert pst_dt.year == 2023


def test_from_utc_to_pst_with_dst():
    """Test conversion from UTC to PDT (PST with daylight saving time)."""
    # Create a datetime during daylight saving time
    dst_dt = datetime(2023, 7, 1, 12, 0, 0)

    # Convert to PST/PDT
    pdt_dt = from_utc_to_pst(dst_dt)

    # Verify the result
    assert pdt_dt.tzinfo is not None  # Should have timezone info
    assert pdt_dt.tzname() == "PDT"  # Should be in PDT timezone

    # PDT is UTC-7, so 12:00 UTC should be 05:00 PDT
    assert pdt_dt.hour == 5
    assert pdt_dt.minute == 0
    assert pdt_dt.day == 1
    assert pdt_dt.month == 7
    assert pdt_dt.year == 2023


def test_from_utc_to_pst_with_none():
    """Test that passing None raises a ValueError."""
    with pytest.raises(
        ValueError, match="Parameter 'started_call' cannot be None"
    ):
        from_utc_to_pst(None)
