from ciba_participant.rpm_api.models import (
    DetailedConnectionStatus,
    DeviceTypeEnum,
    DeviceStatusEnum,
)

test_connection_status = [
    DetailedConnectionStatus(
        status=DeviceStatusEnum.CONNECTED,
        healthy=True,
        device=DeviceTypeEnum.WITHINGS,
        account_id="*********",
    ),
    DetailedConnectionStatus(
        status=DeviceStatusEnum.RECONNECT,
        healthy=False,
        device=DeviceTypeEnum.WITHINGS,
        account_id="random",
    ),
    DetailedConnectionStatus(
        status=DeviceStatusEnum.NOT_CONNECTED,
        healthy=False,
        device=DeviceTypeEnum.WITHINGS,
    ),
]
