import uuid
from http.client import HTT<PERSON>Exception
from unittest.mock import AsyncMock, MagicMock, patch
from contextlib import asynccontextmanager

import pytest
import httpx

from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
)
from ciba_participant.solera.mint_vault.api import delete_solera_activity


@asynccontextmanager
async def setup_delete_solera_activity_mocks(
    mock_activity=None, mock_settings=None, mock_client_response=None
):
    """Set up all the mocks needed for delete_solera_activity tests."""
    # Create unique patch objects for each test run
    activity_patch = patch.object(
        ParticipantActivity, "get_or_none", new_callable=AsyncMock
    )
    settings_patch = patch("ciba_participant.solera.mint_vault.api.settings")
    client_patch = patch("ciba_participant.solera.mint_vault.api.AsyncClient")
    logger_patch = patch("ciba_participant.solera.mint_vault.api.logger")

    # Start all patches
    mock_activity_get = activity_patch.start()
    mock_settings_obj = settings_patch.start()
    mock_client_class = client_patch.start()
    mock_logger = logger_patch.start()

    try:
        # Set up returns - always set return value, even if None
        mock_activity_get.return_value = mock_activity

        if mock_settings is not None:
            mock_settings_obj.MINTVAULT_API_URL = mock_settings.get(
                "MINTVAULT_API_URL", "http://test-api.com"
            )
            mock_settings_obj.MINTVAULT_API_KEY = mock_settings.get(
                "MINTVAULT_API_KEY", "test-api-key"
            )

        # Set up AsyncClient mock
        mock_client_instance = AsyncMock()
        mock_client_class.return_value.__aenter__.return_value = mock_client_instance

        if mock_client_response is not None:
            mock_client_instance.delete.return_value = mock_client_response

        # Create results dictionary
        mocks = {
            "activity_get": mock_activity_get,
            "settings": mock_settings_obj,
            "client_class": mock_client_class,
            "client_instance": mock_client_instance,
            "logger": mock_logger,
        }

        yield mocks
    finally:
        # Stop all patches explicitly to ensure cleanup
        activity_patch.stop()
        settings_patch.stop()
        client_patch.stop()
        logger_patch.stop()


@pytest.fixture
def mock_activity():
    """Create a mock activity for testing."""
    activity_id = uuid.uuid4()
    activity = MagicMock()
    activity.id = activity_id
    # Create a mock for activity_type that has the correct value
    mock_activity_type = MagicMock()
    mock_activity_type.value = "weight_type"
    activity.activity_type = mock_activity_type
    return activity


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    return {
        "MINTVAULT_API_URL": "http://test-mintvault.com",
        "MINTVAULT_API_KEY": "test-key-123",
    }


@pytest.fixture
def mock_successful_response():
    """Create a mock successful HTTP response."""
    response = MagicMock()
    response.raise_for_status.return_value = None
    response.json.return_value = {"success": True, "message": "Activity deleted"}
    return response


@pytest.mark.asyncio
async def test_delete_solera_activity_success(
    mock_activity, mock_settings, mock_successful_response
):
    """Test successful deletion of solera activity."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = str(mock_activity.id)

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=mock_settings,
        mock_client_response=mock_successful_response,
    ) as mocks:
        # Act
        result = await delete_solera_activity(participant_id, activity_id)

        # Assert
        assert result == {"success": True, "message": "Activity deleted"}
        mocks["activity_get"].assert_called_once_with(id=activity_id)

        # Verify AsyncClient was created with correct parameters
        mocks["client_class"].assert_called_once_with(
            base_url="http://test-mintvault.com",
            headers={"Authorization": "Bearer test-key-123"},
            timeout=60,
        )

        # Verify delete request was made with correct URL
        expected_url = f"/api/v1/participants/{participant_id}/solera-activities/{activity_id}/weight_type/correct"
        mocks["client_instance"].delete.assert_called_once_with(expected_url)
        mock_successful_response.raise_for_status.assert_called_once()
        mock_successful_response.json.assert_called_once()


@pytest.mark.asyncio
async def test_delete_solera_activity_not_found():
    """Test deletion when activity is not found."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = "non-existent-activity-id"

    async with setup_delete_solera_activity_mocks(
        mock_activity=None,  # Activity not found
        mock_settings={
            "MINTVAULT_API_URL": "http://test.com",
            "MINTVAULT_API_KEY": "key",
        },
    ) as mocks:
        # Act & Assert
        with pytest.raises(ValueError, match=f"Activity {activity_id} not found"):
            await delete_solera_activity(participant_id, activity_id)

        mocks["activity_get"].assert_called_once_with(id=activity_id)
        # Client should not be called if activity is not found
        mocks["client_class"].assert_not_called()


@pytest.mark.asyncio
async def test_delete_solera_activity_http_error(mock_activity, mock_settings):
    """Test deletion when HTTP error occurs."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = str(mock_activity.id)

    # Create a mock response that raises HTTP error
    mock_error_response = MagicMock()
    mock_error_response.raise_for_status.side_effect = httpx.HTTPStatusError(
        "404 Not Found", request=MagicMock(), response=MagicMock()
    )

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=mock_settings,
        mock_client_response=mock_error_response,
    ) as mocks:
        # Act & Assert
        with pytest.raises(HTTPException, match="Error deleting activity"):
            await delete_solera_activity(participant_id, activity_id)

        mocks["activity_get"].assert_called_once_with(id=activity_id)
        mocks["client_instance"].delete.assert_called_once()
        mock_error_response.raise_for_status.assert_called_once()
        mocks["logger"].exception.assert_called_once()


@pytest.mark.asyncio
async def test_delete_solera_activity_generic_exception(mock_activity, mock_settings):
    """Test deletion when generic exception occurs."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = str(mock_activity.id)

    # Create a mock response that raises generic exception
    mock_error_response = MagicMock()
    mock_error_response.raise_for_status.side_effect = Exception("Network error")

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=mock_settings,
        mock_client_response=mock_error_response,
    ) as mocks:
        # Act & Assert
        with pytest.raises(HTTPException, match="Error deleting activity"):
            await delete_solera_activity(participant_id, activity_id)

        mocks["activity_get"].assert_called_once_with(id=activity_id)
        mocks["client_instance"].delete.assert_called_once()
        mocks["logger"].exception.assert_called_once_with(
            f"Error deleting activity {activity_id}: Network error"
        )


@pytest.mark.asyncio
async def test_delete_solera_activity_different_activity_types(
    mock_settings, mock_successful_response
):
    """Test deletion with different activity types."""
    test_cases = [
        (ParticipantActivityEnum.ENROLL, "enrollment_type"),
        (ParticipantActivityEnum.PLAY, "video_type"),
        (ParticipantActivityEnum.COACH, "chat_type"),
        (ParticipantActivityEnum.RECIPES, "recipe_type"),
        (ParticipantActivityEnum.QUIZ, "personal_success"),
        (ParticipantActivityEnum.ACTIVITY, "activity_type"),
        (ParticipantActivityEnum.ARTICLE, "curriculum"),
        (ParticipantActivityEnum.GROUP, "coaching_call"),
    ]

    participant_id = "test-participant-123"

    for activity_enum, expected_value in test_cases:
        # Create mock activity with specific type
        activity_id = str(uuid.uuid4())
        mock_activity = MagicMock()
        mock_activity.id = activity_id
        # Create a mock for activity_type that has the correct value
        mock_activity_type = MagicMock()
        mock_activity_type.value = expected_value
        mock_activity.activity_type = mock_activity_type

        async with setup_delete_solera_activity_mocks(
            mock_activity=mock_activity,
            mock_settings=mock_settings,
            mock_client_response=mock_successful_response,
        ) as mocks:
            # Act
            result = await delete_solera_activity(participant_id, activity_id)

            # Assert
            assert result == {"success": True, "message": "Activity deleted"}
            expected_url = f"/api/v1/participants/{participant_id}/solera-activities/{activity_id}/{expected_value}/correct"
            mocks["client_instance"].delete.assert_called_once_with(expected_url)


@pytest.mark.asyncio
async def test_delete_solera_activity_timeout_configuration(
    mock_activity, mock_settings, mock_successful_response
):
    """Test that AsyncClient is configured with correct timeout."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = str(mock_activity.id)

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=mock_settings,
        mock_client_response=mock_successful_response,
    ) as mocks:
        # Act
        await delete_solera_activity(participant_id, activity_id)

        # Assert - verify timeout is set to 60 seconds
        mocks["client_class"].assert_called_once_with(
            base_url=mock_settings["MINTVAULT_API_URL"],
            headers={"Authorization": f"Bearer {mock_settings['MINTVAULT_API_KEY']}"},
            timeout=60,
        )


@pytest.mark.asyncio
async def test_delete_solera_activity_authorization_header(
    mock_activity, mock_successful_response
):
    """Test that correct authorization header is set."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = str(mock_activity.id)
    custom_settings = {
        "MINTVAULT_API_URL": "http://custom-api.com",
        "MINTVAULT_API_KEY": "custom-secret-key",
    }

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=custom_settings,
        mock_client_response=mock_successful_response,
    ) as mocks:
        # Act
        await delete_solera_activity(participant_id, activity_id)

        # Assert - verify authorization header
        mocks["client_class"].assert_called_once_with(
            base_url="http://custom-api.com",
            headers={"Authorization": "Bearer custom-secret-key"},
            timeout=60,
        )


@pytest.mark.asyncio
async def test_delete_solera_activity_empty_response_json(mock_activity, mock_settings):
    """Test deletion when response.json() returns empty dict."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = str(mock_activity.id)

    mock_empty_response = MagicMock()
    mock_empty_response.raise_for_status.return_value = None
    mock_empty_response.json.return_value = {}

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=mock_settings,
        mock_client_response=mock_empty_response,
    ) as mocks:
        # Act
        result = await delete_solera_activity(participant_id, activity_id)

        # Assert
        assert result == {}
        mocks["activity_get"].assert_called_once_with(id=activity_id)
        mocks["client_instance"].delete.assert_called_once()
        mock_empty_response.raise_for_status.assert_called_once()
        mock_empty_response.json.assert_called_once()


@pytest.mark.asyncio
async def test_delete_solera_activity_invalid_participant_id(
    mock_activity, mock_settings
):
    """Test deletion with invalid participant ID format."""
    # Arrange
    participant_id = ""  # Empty participant ID
    activity_id = str(mock_activity.id)

    mock_error_response = MagicMock()
    mock_error_response.raise_for_status.side_effect = httpx.HTTPStatusError(
        "400 Bad Request", request=MagicMock(), response=MagicMock()
    )

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=mock_settings,
        mock_client_response=mock_error_response,
    ) as mocks:
        # Act & Assert
        with pytest.raises(HTTPException, match="Error deleting activity"):
            await delete_solera_activity(participant_id, activity_id)

        # Verify the URL was constructed with empty participant_id
        expected_url = f"/api/v1/participants/{participant_id}/solera-activities/{activity_id}/weight_type/correct"
        mocks["client_instance"].delete.assert_called_once_with(expected_url)


@pytest.mark.asyncio
async def test_delete_solera_activity_invalid_activity_id_format():
    """Test deletion with invalid activity ID format."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = "invalid-uuid-format"

    async with setup_delete_solera_activity_mocks(
        mock_activity=None,  # Activity not found due to invalid ID
        mock_settings={
            "MINTVAULT_API_URL": "http://test.com",
            "MINTVAULT_API_KEY": "key",
        },
    ) as mocks:
        # Act & Assert
        with pytest.raises(ValueError, match=f"Activity {activity_id} not found"):
            await delete_solera_activity(participant_id, activity_id)

        mocks["activity_get"].assert_called_once_with(id=activity_id)


@pytest.mark.asyncio
async def test_delete_solera_activity_client_connection_error(
    mock_activity, mock_settings
):
    """Test deletion when client connection fails."""
    # Arrange
    participant_id = "test-participant-123"
    activity_id = str(mock_activity.id)

    # Mock client.delete to raise connection error
    mock_error_response = MagicMock()
    mock_error_response.side_effect = httpx.ConnectError("Connection failed")

    async with setup_delete_solera_activity_mocks(
        mock_activity=mock_activity,
        mock_settings=mock_settings,
        mock_client_response=mock_error_response,
    ) as mocks:
        # Configure the delete method to raise the exception
        mocks["client_instance"].delete.side_effect = httpx.ConnectError(
            "Connection failed"
        )

        # Act & Assert
        with pytest.raises(HTTPException, match="Error deleting activity"):
            await delete_solera_activity(participant_id, activity_id)

        mocks["activity_get"].assert_called_once_with(id=activity_id)
        mocks["client_instance"].delete.assert_called_once()
        mocks["logger"].exception.assert_called_once()
