import pytest
import pendulum

from uuid import uuid4
from httpx import Response

from ciba_participant.schedule_manager.service import (
    Schedule<PERSON><PERSON>ger,
    ScheduleManagerError,
)
from ciba_participant.error_messages.schedule_manager import (
    SCHEDULE_MANAGER_IS_DOWN,
    UNEXPECTED_ERROR_MESSAGE,
)
from ciba_participant.schedule_manager.types import ZoomRecurrenceType

MEETING_CREATION_PAYLOAD = {
    "start_time": pendulum.now(),
    "repeat_type": ZoomRecurrenceType.DAILY,
    "repeat_count": 1,
    "user_id": "123456",
    "alternative_hosts": [],
    "title": "Testing creation of meetings",
    "description": "This is a test.",
}

CREATE_MEETING_RESPONSE = {
    "id": str(uuid4()),
    "start_url": "https://start.url",
    "join_url": "https://join.url",
    "host_email": "<EMAIL>",
    "occurrences": [
        {
            "occurrence_id": "12345",
            "start_time": pendulum.now().to_iso8601_string(),
            "duration": 60,
        }
    ],
}

ENDPOINT = "http://localhost:5000"


@pytest.mark.asyncio
async def test_server_down():
    """
    When the server is down, calling the endpoint should raise
    an Exception with the appropriate message
    """
    schedule_manager = ScheduleManager(ENDPOINT + "0")

    with pytest.raises(ScheduleManagerError) as ex:
        await schedule_manager.create_zoom_meetings(**MEETING_CREATION_PAYLOAD)

    assert SCHEDULE_MANAGER_IS_DOWN in str(ex)


@pytest.mark.asyncio
async def test_empty_error_message(respx_mock):
    """
    When there is an internal server error, but the cause is unknown
    """
    route = respx_mock.post(ENDPOINT + "/meetings").mock(
        return_value=Response(status_code=500)
    )
    schedule_manager = ScheduleManager(ENDPOINT)

    with pytest.raises(ScheduleManagerError) as ex:
        await schedule_manager.create_zoom_meetings(**MEETING_CREATION_PAYLOAD)

    assert route.called
    assert UNEXPECTED_ERROR_MESSAGE in str(ex)


@pytest.mark.asyncio
async def test_server_error(respx_mock):
    """
    When there is an internal server error, the client should raise a
    ScheduleManagerError with the cause
    """
    route = respx_mock.post(ENDPOINT + "/meetings").mock(
        return_value=Response(status_code=500, json={"error": UNEXPECTED_ERROR_MESSAGE})
    )
    schedule_manager = ScheduleManager(ENDPOINT)

    with pytest.raises(ScheduleManagerError) as ex:
        await schedule_manager.create_zoom_meetings(**MEETING_CREATION_PAYLOAD)

    assert route.called
    assert UNEXPECTED_ERROR_MESSAGE in str(ex)


@pytest.mark.asyncio
async def test_create_zoom_meeting(respx_mock):
    """
    No error should be raised when a meeting has been created correctly
    """
    route = respx_mock.post(ENDPOINT + "/meetings").mock(
        return_value=Response(
            status_code=200,
            json=CREATE_MEETING_RESPONSE,
        ),
    )
    schedule_manager = ScheduleManager(ENDPOINT)

    response = await schedule_manager.create_zoom_meetings(**MEETING_CREATION_PAYLOAD)

    assert route.called
    assert response is not None
    assert response.meeting_id == CREATE_MEETING_RESPONSE["id"]


@pytest.mark.asyncio
async def test_update_title_and_description(respx_mock):
    """
    No error should be raised when updating a meeting's title and description.
    Response is empty on patch operations.
    """
    meeting_id = str(uuid4())

    route = respx_mock.patch(f"{ENDPOINT}/meetings/{meeting_id}").mock(
        return_value=Response(status_code=204),
    )

    schedule_manager = ScheduleManager(ENDPOINT)

    await schedule_manager.update_title_and_description(
        meeting_id=meeting_id,
        title="My new shiny title",
        description="A compelling description",
    )

    assert route.called
