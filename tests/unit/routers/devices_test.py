import asyncio
from http import HTTPStatus
from unittest.mock import patch, MagicMock, AsyncMock
from uuid import UUID

import pytest
from fastapi.testclient import TestClient
from fastapi import HTTPException
import pendulum

from app.main import app
from app.routers.devices import set_user_weight_status
from tests.unit.common import test_connection_status
from ciba_participant.activity.models import ParticipantActivityDevice
from ciba_participant.rpm_api.models import (
    DeviceStatusEnum,
    DeviceTypeEnum,
    DetailedConnectionStatus,
    Subscription,
)

client = TestClient(app)
test_email = "<EMAIL>"
test_headers = {"X-Auth-Key": "123"}
test_auth_url = "https://example.com/auth"
test_now = pendulum.parse("2023-01-01T00:00:00Z")
test_not_found = "User not found"


class TestEndpoints:
    STATUS = f"/api/devices/status?mail={test_email}&type_device=withings"
    DISCONNECT = "/api/devices/disconnect"


class MockModules:
    DISCONNECT = "app.routers.devices.RPMRequestHandler.disconnect"
    GET_FROM_EMAIL = "app.routers.devices.get_participant_from_email"
    GET_DEVICE_STATUS = "app.routers.devices.get_single_device_status"
    GET_OR_NONE = "app.routers.devices.Participant.get_or_none"
    SET_WEIGHT_STATUS = "app.routers.devices.set_user_weight_status"


# Helper to set up asyncio event loop for tests
@pytest.fixture(scope="function")
def event_loop():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


def test_get_connection_status_with_errors():
    """
    get_connection_status should return a bad request (400)
    when the provided email doesn't exist.
    """
    mock_error = HTTPException(
        status_code=HTTPStatus.BAD_REQUEST,
        detail=f"Email {test_email} not found.",
    )

    with patch(MockModules.GET_FROM_EMAIL, side_effect=mock_error):
        response = client.get(TestEndpoints.STATUS, headers=test_headers)

        assert response.status_code == mock_error.status_code
        assert response.json() == {"detail": mock_error.detail}


@pytest.mark.parametrize("test_status", test_connection_status)
def test_get_connection_status_with_success(test_status):
    """
    get_connection_status should return an ok request (200)
    containing the connection status details.
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(MockModules.GET_DEVICE_STATUS, return_value=test_status),
    ):
        response = client.get(TestEndpoints.STATUS, headers=test_headers)

        assert response.status_code == HTTPStatus.OK
        assert response.json() == {
            "status": test_status.status.value,
            "healthy": test_status.healthy,
            "device": test_status.device,
            "account_id": test_status.account_id,
            "battery": test_status.battery,
            "signal": test_status.signal,
            "model": test_status.model,
        }


@pytest.mark.asyncio
async def test_set_user_weight_status_success():
    """
    set_user_weight_status should update the participant metadata successfully
    """
    # Create mock participant
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")

    # Create mock participant meta with existing metadata
    mock_participant_meta = MagicMock()
    mock_participant_meta.id = UUID("********-9abc-def0-1234-56789abcdef0")
    mock_participant_meta.metadata = {"existing_key": "existing_value"}

    with (
        patch(
            "app.routers.devices.ParticipantMeta.get_or_none",
            return_value=mock_participant_meta,
            new_callable=AsyncMock,
        ),
        patch("app.routers.devices.ParticipantMeta.filter") as mock_filter,
    ):
        # Mock the filter().update() chain
        mock_update = AsyncMock(return_value=1)  # 1 row updated
        mock_filter.return_value.update = mock_update

        # Call the function
        result = await set_user_weight_status(
            mock_participant, True, "test-correlation-id"
        )

        # Verify the result
        assert result is True

        # Verify that filter was called with correct participant meta id
        mock_filter.assert_called_once_with(id=mock_participant_meta.id)

        # Verify that update was called with correct metadata
        mock_update.assert_called_once()
        call_args = mock_update.call_args[1]  # Get keyword arguments
        updated_metadata = call_args["metadata"]
        assert updated_metadata["is_weight"] is True
        assert (
            updated_metadata["existing_key"] == "existing_value"
        )  # Existing data preserved


@pytest.mark.asyncio
async def test_set_user_weight_status_creates_new_metadata():
    """
    set_user_weight_status should create new metadata when none exists
    """
    # Create mock participant
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")

    # Create mock new participant meta
    mock_new_participant_meta = MagicMock()
    mock_new_participant_meta.id = UUID("********-9abc-def0-1234-56789abcdef0")

    with (
        patch(
            "app.routers.devices.ParticipantMeta.get_or_none",
            return_value=None,
            new_callable=AsyncMock,
        ),
        patch(
            "app.routers.devices.ParticipantMeta.create",
            return_value=mock_new_participant_meta,
            new_callable=AsyncMock,
        ) as mock_create,
    ):
        # Call the function
        result = await set_user_weight_status(
            mock_participant, True, "test-correlation-id"
        )

        # Verify the result
        assert result is True

        # Verify that create was called with correct parameters
        mock_create.assert_called_once_with(
            participant=mock_participant, metadata={"is_weight": True}
        )


@pytest.mark.asyncio
async def test_set_user_weight_status_exception():
    """
    set_user_weight_status should handle exceptions and return False
    """
    # Mock participant and participant_meta
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")
    mock_participant.email = test_email

    mock_participant_meta = MagicMock()
    mock_participant_meta.id = UUID("1234abcd-94aa-4aba-9d9e-0e2866a2c53f")
    mock_participant_meta.metadata = {"existing_key": "value"}

    # Create a mock query result that raises an exception when update is called
    mock_query_result = AsyncMock()
    mock_query_result.update = AsyncMock(
        side_effect=Exception("Database error")
    )

    with (
        patch(
            "app.routers.devices.ParticipantMeta.get_or_none",
            return_value=mock_participant_meta,
            new_callable=AsyncMock,
        ),
        patch(
            "app.routers.devices.ParticipantMeta.filter",
            return_value=mock_query_result,
            new_callable=AsyncMock,
        ),
    ):
        result = await set_user_weight_status(mock_participant, True)

        # Verify the function returned False due to the exception
        assert result is False


def test_get_code_healthy_device():
    """
    get_code should return device status when device is healthy
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")

    # Create a healthy device response
    mock_response = DetailedConnectionStatus(
        healthy=True,
        token="test_token",
        status=DeviceStatusEnum.CONNECTED,
        device=DeviceTypeEnum.WITHINGS,
        account_id="123456",
        subscription=Subscription(expires_in=3600),
        auth_url=test_auth_url,
    )

    with (
        patch(
            MockModules.GET_FROM_EMAIL,
            return_value=mock_participant,
            new_callable=AsyncMock,
        ),
        patch(
            MockModules.GET_DEVICE_STATUS,
            return_value=mock_response,
            new_callable=AsyncMock,
        ),
        patch(
            MockModules.SET_WEIGHT_STATUS,
            return_value=True,
            new_callable=AsyncMock,
        ),
        patch(
            "app.routers.devices.pendulum.now",
            return_value=test_now,
        ),
    ):
        response = client.get(
            f"/api/devices/get_code?type_device=withings&mail={test_email}",
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.OK
        response_data = response.json()

        # Check that auth_url is not in the response
        assert "auth_url" not in response_data
        assert response_data["token"] == "test_token"
        assert response_data["healthy"] is True


def test_get_code_not_connected_device():
    """
    get_code should return auth_url when device is not connected
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")
    mock_participant.fetch_related = AsyncMock()

    # Create a not connected device response
    mock_device_status = DetailedConnectionStatus(
        healthy=False,
        token=None,
        status=DeviceStatusEnum.NOT_CONNECTED,
        device=DeviceTypeEnum.WITHINGS,
        account_id=None,
        subscription=None,
        auth_url=None,
    )

    # Mock cohort data
    mock_cohort = MagicMock()
    mock_cohort.started_at = test_now

    # Mock auth response from RPM API
    mock_auth_response = {"auth_url": test_auth_url}

    with (
        patch(
            MockModules.GET_FROM_EMAIL,
            new_callable=AsyncMock,
            return_value=mock_participant,
        ),
        patch(
            MockModules.GET_DEVICE_STATUS,
            new_callable=AsyncMock,
            return_value=mock_device_status,
        ),
        patch(
            "app.routers.devices.Cohort.filter",
            new_callable=AsyncMock,
            return_value=[mock_cohort],
        ),
        patch(
            "app.routers.devices.RPMRequestHandler.get_device_auth",
            new_callable=AsyncMock,
            return_value=mock_auth_response,
        ),
        patch(
            MockModules.SET_WEIGHT_STATUS,
            new_callable=AsyncMock,
            return_value=True,
        ),
        patch(
            "app.routers.devices.pendulum.now",
            return_value=test_now,
        ),
    ):
        response = client.get(
            f"/api/devices/get_code?type_device=withings&mail={test_email}",
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.OK
        response_data = response.json()

        # Check that auth_url is in the response
        assert "auth_url" in response_data
        assert response_data["auth_url"] == test_auth_url
        assert "token" not in response_data
        assert "account_id" not in response_data
        assert "subscription" not in response_data


def test_get_code_unhealthy_device_with_valid_token():
    """
    get_code should return device status when device is unhealthy but has valid token
    This covers the scenario where healthy=False but token/account_id/subscription exist
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")

    # Create an unhealthy device response with valid credentials (your specific case)
    mock_device_status = DetailedConnectionStatus(
        healthy=False,
        token="5cda5058-5701-4506-96d8-b67ef3d05edf",
        status=DeviceStatusEnum.CONNECTED,  # Device is connected but not healthy
        device=DeviceTypeEnum.WITHINGS,
        account_id="********",
        subscription=Subscription(expires_in=10800),
        auth_url=None,
    )

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(MockModules.GET_DEVICE_STATUS, return_value=mock_device_status),
        patch("pendulum.now") as mock_now,
    ):
        mock_now.return_value.int_timestamp = **********

        response = client.get(
            f"/api/devices/get_code?mail={test_email}&type_device=withings",
            headers=test_headers,
        )

        assert response.status_code == 200
        response_data = response.json()

        # Should return the device status as-is for client handling
        assert response_data["healthy"] is False
        assert response_data["token"] == "5cda5058-5701-4506-96d8-b67ef3d05edf"
        assert response_data["account_id"] == "********"
        assert response_data["subscription"]["expires_in"] == 10800
        assert "auth_url" not in response_data  # Should be removed


def test_get_code_unhealthy_device_no_credentials():
    """
    get_code should return an error when device is unhealthy and has no valid credentials
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")

    # Create an unhealthy device response with no credentials
    mock_device_status = DetailedConnectionStatus(
        healthy=False,
        token=None,
        status=DeviceStatusEnum.CONNECTED,  # Connected but unhealthy and no token
        device=DeviceTypeEnum.WITHINGS,
        account_id=None,
        subscription=None,
        auth_url=None,
    )

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(MockModules.GET_DEVICE_STATUS, return_value=mock_device_status),
        patch("pendulum.now") as mock_now,
    ):
        mock_now.return_value.int_timestamp = **********

        response = client.get(
            f"/api/devices/get_code?mail={test_email}&type_device=withings",
            headers=test_headers,
        )

        assert response.status_code == 503  # SERVICE_UNAVAILABLE
        response_data = response.json()

        # Should return error details
        assert "issue" in response_data["detail"]
        assert "recommendation" in response_data["detail"]
        assert "status" in response_data["detail"]
        assert "healthy" in response_data["detail"]
        assert "issue" in response_data["detail"]
        assert "recommendation" in response_data["detail"]
        assert (
            "WITHINGS connection is not healthy"
            in response_data["detail"]["issue"]
        )
        assert (
            "reconnect your device"
            in response_data["detail"]["recommendation"]
        )


def test_get_codes():
    """
    get_codes should return codes for all devices
    """
    # Mock participant
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")
    mock_participant.email = test_email

    # Mock the RPMRequestHandler.get_data method
    mock_response = {"auth_url": test_auth_url}

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(
            "app.routers.devices.RPMRequestHandler.get_data",
            return_value=mock_response,
            new_callable=AsyncMock,
        ),
    ):
        response = client.get(
            f"/api/devices/get_codes?mail={test_email}", headers=test_headers
        )

        assert response.status_code == HTTPStatus.OK
        response_data = response.json()

        # Should return data for all device types
        assert len(response_data) == len(ParticipantActivityDevice)

        # Each device should have the mock response data
        for device_data in response_data:
            assert "name" in device_data
            assert device_data["auth_url"] == test_auth_url


def test_get_codes_with_site():
    """
    get_codes should include site parameter when provided
    """
    # Mock participant
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")
    mock_participant.email = test_email

    # Mock the RPMRequestHandler.get_data method
    mock_response = {"auth_url": test_auth_url}
    test_site = "test-site"

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(
            "app.routers.devices.RPMRequestHandler.get_data",
            return_value=mock_response,
            new_callable=AsyncMock,
        ) as mock_get_data,
    ):
        response = client.get(
            f"/api/devices/get_codes?mail={test_email}&site={test_site}",
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.OK

        # Verify that site parameter was included in the request
        for call_args in mock_get_data.call_args_list:
            args, _ = call_args
            prepared_data = args[0]
            assert "site" in prepared_data
            assert prepared_data["site"] == test_site


def test_add_user_success():
    """
    add_user should return response from RPM API when user exists
    """
    mock_participant = MagicMock()
    mock_participant.email = test_email
    mock_rpm_response = {"status": "success"}

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(
            "app.routers.devices.RPMRequestHandler.post_data",
            new_callable=AsyncMock,
            return_value=mock_rpm_response,
        ),
    ):
        response = client.get(
            f"/api/devices/add_user?mail={test_email}", headers=test_headers
        )

        assert response.status_code == HTTPStatus.OK
        assert response.json() == mock_rpm_response


def test_add_user_not_found():
    """
    add_user should return 400 when user doesn't exist
    """
    # Mock get_participant_from_email to raise HTTPException for user not found
    with patch(
        MockModules.GET_FROM_EMAIL,
        side_effect=HTTPException(
            status_code=HTTPStatus.BAD_REQUEST, detail=test_not_found
        ),
    ):
        response = client.get(
            f"/api/devices/add_user?mail={test_email}", headers=test_headers
        )

        assert response.status_code == HTTPStatus.BAD_REQUEST
        assert response.json() == {"detail": test_not_found}


def test_sync_success():
    """
    sync should return response from RPM API when user exists
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")
    mock_rpm_response = {"status": "success"}

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(
            "app.routers.devices.RPMRequestHandler.sync",
            return_value=mock_rpm_response,
            new_callable=AsyncMock,
        ),
    ):
        response = client.post(
            "/api/devices/sync",
            json={"type_device": "withings", "email": test_email},
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.OK
        assert response.json() == mock_rpm_response


def test_sync_user_not_found():
    """
    sync should return 400 when user doesn't exist
    """
    with patch(
        MockModules.GET_FROM_EMAIL,
        side_effect=HTTPException(
            status_code=HTTPStatus.BAD_REQUEST, detail=test_not_found
        ),
    ):
        response = client.post(
            "/api/devices/sync",
            json={"type_device": "withings", "email": test_email},
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.BAD_REQUEST
        assert response.json() == {"detail": test_not_found}


def test_disconnect_success():
    """
    disconnect should return success response when disconnection is successful
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")
    mock_rpm_response = {"disconnected": True}

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(
            MockModules.DISCONNECT,
            return_value=mock_rpm_response,
            new_callable=AsyncMock,
        ),
        patch(
            MockModules.SET_WEIGHT_STATUS,
            return_value=True,
            new_callable=AsyncMock,
        ),
    ):
        response = client.post(
            TestEndpoints.DISCONNECT,
            json={"type_device": "withings", "email": test_email},
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.OK
        assert response.json() == {"disconnected": "Device disconnected"}


def test_disconnect_not_disconnected():
    """
    disconnect should return appropriate response when disconnection is not successful
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")
    mock_rpm_response = {"disconnected": False}

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(
            MockModules.DISCONNECT,
            return_value=mock_rpm_response,
            new_callable=AsyncMock,
        ),
    ):
        response = client.post(
            TestEndpoints.DISCONNECT,
            json={"type_device": "withings", "email": test_email},
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.OK
        assert response.json() == {"disconnected": "Device connected"}


def test_disconnect_user_not_found():
    """
    disconnect should return 400 when user doesn't exist
    """
    with patch(
        MockModules.GET_FROM_EMAIL,
        side_effect=HTTPException(
            status_code=HTTPStatus.BAD_REQUEST, detail=test_not_found
        ),
    ):
        response = client.post(
            TestEndpoints.DISCONNECT,
            json={"type_device": "withings", "email": test_email},
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.BAD_REQUEST
        assert response.json() == {"detail": test_not_found}


def test_disconnect_exception():
    """
    disconnect should handle exceptions properly
    """
    mock_participant = MagicMock()
    mock_participant.id = UUID("8990f6ed-94aa-4aba-9d9e-0e2866a2c53f")

    with (
        patch(MockModules.GET_FROM_EMAIL, return_value=mock_participant),
        patch(
            MockModules.DISCONNECT,
            side_effect=Exception("API error"),
            new_callable=AsyncMock,
        ),
    ):
        response = client.post(
            TestEndpoints.DISCONNECT,
            json={"type_device": "withings", "email": test_email},
            headers=test_headers,
        )

        # Now expects 500 Internal Server Error instead of 400 Bad Request
        assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
        assert "Error on disconnecting: API error" in response.json()["detail"]


def test_get_code_exception():
    """
    get_code should handle exceptions properly
    """
    with patch(
        MockModules.GET_FROM_EMAIL,
        side_effect=Exception("API error"),
        new_callable=AsyncMock,
    ):
        response = client.get(
            f"/api/devices/get_code?type_device=withings&mail={test_email}",
            headers=test_headers,
        )

        assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
        assert "Error when getting device status" in response.json()["detail"]
