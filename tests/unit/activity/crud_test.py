import pytest
from unittest.mock import patch, MagicMock

from ciba_participant.activity.crud import (
    ParticipantActivityRepository,
    ProgramModuleSectionOutput,
    ParticipantSectionProgress,
    ParticipantActivityOutput,
)
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
)


@pytest.fixture
def dummy_module():
    section1 = MagicMock(
        spec=ProgramModuleSectionOutput, activity_type="TYPE_A", id="section1"
    )
    section2 = MagicMock(
        spec=ProgramModuleSectionOutput, activity_type="TYPE_B", id="section2"
    )
    program_module = MagicMock(sections=[section1, section2])
    module = MagicMock(program_module=program_module)
    return module, [section1, section2]


@pytest.fixture
def sample_activities():
    act_a = MagicMock(
        spec=ParticipantActivityOutput, activity_type="TYPE_A", section_id="section1"
    )
    act_b = MagicMock(
        spec=ParticipantActivityOutput, activity_type="TYPE_B", section_id="section2"
    )
    act_group = MagicMock(
        spec=ParticipantActivityOutput,
        activity_type=ParticipantActivityEnum.GROUP,
        section_id="section1",
    )
    act_coach = MagicMock(
        spec=ParticipantActivityOutput,
        activity_type=ParticipantActivityEnum.COACH,
        section_id=None,
    )
    return [act_a, act_b, act_group, act_coach]


@pytest.mark.asyncio
@patch.object(
    ProgramModuleSectionOutput,
    "model_validate",
    side_effect=lambda s: s,
)
async def test_no_activities_without_milestones(mock_validate, dummy_module):
    """Test that no activities returns empty activities list when milestones are excluded."""
    module, _ = dummy_module
    result = await ParticipantActivityRepository.get_participant_module_progress(
        module,
        [],
        include_milestone_activities=False,
    )
    assert isinstance(result, list)
    assert len(result) == 2
    for sec_prog in result:
        assert isinstance(sec_prog, ParticipantSectionProgress)
        assert sec_prog.activities == []


@pytest.mark.asyncio
@patch.object(
    ProgramModuleSectionOutput,
    "model_validate",
    side_effect=lambda s: s,
)
async def test_no_activities_with_milestones(mock_validate, dummy_module):
    """Test that no activities returns empty lists for group and coach when milestones are included."""
    module, _ = dummy_module
    (
        sections,
        class_acts,
        chat_acts,
    ) = await ParticipantActivityRepository.get_participant_module_progress(
        module,
        [],
        include_milestone_activities=True,
    )
    assert isinstance(sections, list)
    assert class_acts == []
    assert chat_acts == []


@pytest.mark.asyncio
@patch.object(
    ProgramModuleSectionOutput,
    "model_validate",
    side_effect=lambda s: s,
)
async def test_with_activities_without_milestones(
    mock_validate, dummy_module, sample_activities
):
    """Test that activities are correctly assigned to sections without including milestones."""
    module, _ = dummy_module
    result = await ParticipantActivityRepository.get_participant_module_progress(
        module,
        sample_activities,
        include_milestone_activities=False,
    )
    assert isinstance(result, list)
    sec_prog_a = result[0]
    sec_prog_b = result[1]
    assert sample_activities[0] in sec_prog_a.activities
    assert sample_activities[1] in sec_prog_b.activities
    # Check that activities are assigned to the correct sections
    # Note: The implementation doesn't filter out GROUP or COACH activities
    # when include_milestone_activities=False, it just doesn't return them separately


@pytest.mark.asyncio
@patch.object(
    ProgramModuleSectionOutput,
    "model_validate",
    side_effect=lambda s: s,
)
async def test_with_activities_with_milestones(
    mock_validate, dummy_module, sample_activities
):
    """Test that activities and milestone (group/coach) activities are returned when included."""
    module, _ = dummy_module
    (
        sections,
        class_acts,
        chat_acts,
    ) = await ParticipantActivityRepository.get_participant_module_progress(
        module,
        sample_activities,
        include_milestone_activities=True,
    )
    assert any(act.activity_type == "TYPE_A" for act in sections[0].activities)
    assert any(act.activity_type == "TYPE_B" for act in sections[1].activities)
    assert all(act.activity_type == ParticipantActivityEnum.GROUP for act in class_acts)
    assert all(act.activity_type == ParticipantActivityEnum.COACH for act in chat_acts)


@pytest.mark.asyncio
@patch.object(
    ProgramModuleSectionOutput,
    "model_validate",
    side_effect=lambda s: s,
)
async def test_sections_always_return_list(
    mock_validate, dummy_module, sample_activities
):
    """Test that sections without matching activities receive an empty list, not None."""
    module, _ = dummy_module
    filtered = [act for act in sample_activities if act.activity_type == "TYPE_A"]
    result = await ParticipantActivityRepository.get_participant_module_progress(
        module,
        filtered,
        include_milestone_activities=False,
    )
    assert result[0].activities != []
    assert result[1].activities == []
