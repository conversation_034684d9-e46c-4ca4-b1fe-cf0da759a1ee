import json
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mo<PERSON>, patch
from uuid import uuid4

import httpx
import pytest
from jose import jws, jwt
from redis import StrictRedis

from app.auth.exceptions import InvalidToken
from app.auth.models import AuthContext
from app.auth.cognito import (
    AsyncCacheDecorator,
    AsyncRedisCache,
    BEARER_PREFIX,
    CognitoAuthenticator,
    ParticipantAuthenticator,
    ProviderAuthenticator,
    SOLERA_PREFIX,
    SoleraAuthenticator,
    result,
)
from ciba_participant.participant.models import (
    ParticipantStatus,
)


@pytest.fixture
def mock_redis_client():
    mock_client = Mock(spec=StrictRedis)
    return mock_client


@pytest.fixture
def mock_settings():
    settings = Mock()
    settings.COGNITO_AWS_REGION = "us-west-2"
    settings.COGNITO_APP_CLIENT_ID = "test-client-id"
    settings.COGNITO_USER_POOL_ID = "us-west-2_test-user-pool"
    settings.PROVIDER_APP_CLIENT_ID = "test-provider-client-id"
    settings.PROVIDER_USER_POOL_ID = "us-west-2_test-provider-pool"
    settings.REDIS_HOST = "localhost"
    settings.REDIS_PORT = 6379
    settings.REDIS_PASSWORD = ""
    return settings


@pytest.fixture
def valid_token():
    return "valid.jwt.token"


@pytest.fixture
def valid_claims():
    return {
        "token_use": "id",
        "email": "<EMAIL>",
        "email_verified": True,
        "custom:participantNickname": uuid4(),
        "sub": uuid4(),
        "auth_time": **********,
        "custom:isAdmin": False,
    }


@pytest.fixture
def valid_access_claims():
    return {
        "token_use": "access",
        "client_id": "test-client-id",
        "aud": "test-client-id",
    }


@pytest.fixture
def mock_jwks():
    return {
        "keys": [
            {
                "kid": "test-kid",
                "kty": "RSA",
                "n": "test-n",
                "e": "AQAB",
                "use": "sig",
                "alg": "RS256",
            }
        ]
    }


class TestResult:
    def test_result_success(self):
        assert result() == (True, None)

    def test_result_failure(self):
        assert result("Error message") == (False, "Error message")


class TestAsyncCacheDecorator:
    @pytest.mark.asyncio
    async def test_call_with_cache_hit(self, mock_redis_client):
        # Setup
        mock_redis_client.get.return_value = '"cached_value"'
        decorator = AsyncCacheDecorator(
            redis_client=mock_redis_client,
            prefix="test",
            serializer=json.dumps,
            deserializer=json.loads,
        )

        # Mock function to be decorated
        mock_fn = AsyncMock(return_value="original_value")
        decorated_fn = decorator(mock_fn)

        # Call the decorated function
        result = await decorated_fn("arg1", kwarg1="value1")

        # Assertions
        assert result == "cached_value"
        mock_redis_client.get.assert_called_once()
        mock_fn.assert_not_called()

    @pytest.mark.asyncio
    async def test_call_with_cache_miss(self, mock_redis_client):
        # Setup
        mock_redis_client.get.return_value = None
        mock_lua_fn = Mock()

        decorator = AsyncCacheDecorator(
            redis_client=mock_redis_client,
            prefix="test",
            serializer=json.dumps,
            deserializer=json.loads,
        )

        # Mock function to be decorated
        mock_fn = AsyncMock(return_value="original_value")
        decorated_fn = decorator(mock_fn)

        # Call the decorated function
        with patch(
            "app.auth.cognito.get_cache_lua_fn", return_value=mock_lua_fn
        ):
            result = await decorated_fn("arg1", kwarg1="value1")

        # Assertions
        assert result == "original_value"
        mock_redis_client.get.assert_called_once()
        mock_fn.assert_called_once_with("arg1", kwarg1="value1")
        mock_lua_fn.assert_called_once()

    @pytest.mark.asyncio
    async def test_call_with_redis_exception(self, mock_redis_client):
        # Setup
        mock_redis_client.get.side_effect = Exception("Redis error")

        decorator = AsyncCacheDecorator(
            redis_client=mock_redis_client,
            prefix="test",
            serializer=json.dumps,
            deserializer=json.loads,
        )

        # Mock function to be decorated
        mock_fn = AsyncMock(return_value="original_value")
        decorated_fn = decorator(mock_fn)

        # Call the decorated function
        result = await decorated_fn("arg1", kwarg1="value1")

        # Assertions
        assert result == "original_value"
        mock_redis_client.get.assert_called_once()
        mock_fn.assert_called_once_with("arg1", kwarg1="value1")


class TestAsyncRedisCache:
    def test_cache_method(self, mock_redis_client):
        # Setup
        cache = AsyncRedisCache(redis_client=mock_redis_client)

        # Call the cache method
        decorator = cache.cache(ttl=300, limit=10, namespace="test_namespace")

        # Assertions
        assert isinstance(decorator, AsyncCacheDecorator)
        assert decorator.ttl == 300
        assert decorator.limit == 10
        assert decorator.namespace == "test_namespace"
        assert decorator.client == mock_redis_client


class TestCognitoAuthenticator:
    @pytest.fixture
    def cognito_auth(self):
        auth = CognitoAuthenticator()
        auth.REGION = "us-west-2"
        auth.APP_CLIENT_ID = "test-client-id"
        auth.POOL_ID = "us-west-2_test-pool"
        return auth

    def test_get_issuer(self, cognito_auth):
        expected = (
            "https://cognito-idp.us-west-2.amazonaws.com/us-west-2_test-pool"
        )
        assert cognito_auth.get_issuer() == expected

    @pytest.mark.asyncio
    async def test_get_cognito_jwks_success(self, mock_jwks):
        # Setup
        with patch("httpx.AsyncClient") as mock_client:
            mock_response = Mock()
            mock_response.json.return_value = mock_jwks
            mock_response.raise_for_status = Mock()

            mock_client_instance = AsyncMock()
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = (
                mock_client_instance
            )

            # Call the method
            result = await CognitoAuthenticator.get_cognito_jwks(
                "https://test-issuer"
            )

            # Assertions
            assert result == mock_jwks["keys"]
            mock_client_instance.get.assert_called_once_with(
                "https://test-issuer/.well-known/jwks.json", timeout=60
            )

    @pytest.mark.asyncio
    async def test_get_cognito_jwks_http_error(self):
        # Setup
        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_response = MagicMock()
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "Error", request=Mock(), response=mock_response
            )
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = (
                mock_client_instance
            )

            # Call the method
            result = await CognitoAuthenticator.get_cognito_jwks(
                "https://test-issuer"
            )

            # Assertions
            assert result == []

    @pytest.mark.asyncio
    async def test_get_cognito_jwks_general_exception(self):
        # Setup
        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.get.side_effect = Exception("General error")
            mock_client.return_value.__aenter__.return_value = (
                mock_client_instance
            )

            # Call the method
            result = await CognitoAuthenticator.get_cognito_jwks(
                "https://test-issuer"
            )

            # Assertions
            assert result == []

    @pytest.mark.asyncio
    async def test_get_use_keys_with_cache_hit(self, cognito_auth, mock_jwks):
        # Setup
        kid = "test-kid"
        expected_key = mock_jwks["keys"][0]

        with patch.object(
            cognito_auth,
            "get_cognito_jwks",
            new=AsyncMock(return_value=mock_jwks["keys"]),
        ):
            # Call the method
            result = await cognito_auth.get_use_keys(kid)

            # Assertions
            assert result == [expected_key]
            cognito_auth.get_cognito_jwks.assert_called_once_with(
                cognito_auth.get_issuer()
            )

    @pytest.mark.asyncio
    async def test_get_use_keys_with_cache_miss_then_hit(
        self, cognito_auth, mock_jwks
    ):
        # Setup
        kid = "test-kid"
        expected_key = mock_jwks["keys"][0]

        # First call returns empty list, second call returns the keys
        cognito_auth.get_cognito_jwks = AsyncMock()
        cognito_auth.get_cognito_jwks.side_effect = [[], mock_jwks["keys"]]

        # Mock the invalidate method
        cognito_auth.get_cognito_jwks.invalidate = Mock()

        # Call the method
        result = await cognito_auth.get_use_keys(kid)

        # Assertions
        assert result == [expected_key]
        assert cognito_auth.get_cognito_jwks.call_count == 2
        cognito_auth.get_cognito_jwks.invalidate.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_use_keys_with_invalidate_exception(
        self, cognito_auth, mock_jwks
    ):
        # Setup
        kid = "test-kid"
        expected_key = mock_jwks["keys"][0]

        # First call returns empty list, second call returns the keys
        cognito_auth.get_cognito_jwks = AsyncMock()
        cognito_auth.get_cognito_jwks.side_effect = [[], mock_jwks["keys"]]

        # Mock the invalidate method to raise an exception
        cognito_auth.get_cognito_jwks.invalidate = Mock(
            side_effect=Exception("Invalidate error")
        )

        # Call the method
        result = await cognito_auth.get_use_keys(kid)

        # Assertions
        assert result == [expected_key]
        assert cognito_auth.get_cognito_jwks.call_count == 2

    @pytest.mark.asyncio
    async def test_validate_jwt_success(
        self, cognito_auth, valid_token, mock_jwks
    ):
        # Setup
        use_key = mock_jwks["keys"][0]

        with (
            patch.multiple(
                jwt,
                get_unverified_header=Mock(
                    return_value={"kid": "test-kid", "alg": "RS256"}
                ),
                decode=Mock(),
                get_unverified_claims=Mock(
                    return_value={"token_use": "id", "aud": "test-client-id"}
                ),
            ),
            patch.object(jws, "verify", Mock()),
            patch.object(
                cognito_auth,
                "get_use_keys",
                new=AsyncMock(return_value=[use_key]),
            ),
        ):
            # Call the method
            valid, message = await cognito_auth.validate_jwt(valid_token)

            # Assertions
            assert valid is True
            assert message is None
            jwt.decode.assert_called_once()
            jws.verify.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_jwt_invalid_keys(self, cognito_auth, valid_token):
        # Setup
        with (
            patch.multiple(
                jwt,
                get_unverified_header=Mock(return_value={"kid": "test-kid"}),
            ),
            patch.object(
                cognito_auth, "get_use_keys", new=AsyncMock(return_value=[])
            ),
        ):
            # Call the method
            valid, message = await cognito_auth.validate_jwt(valid_token)

            # Assertions
            assert valid is False
            assert "Obtained keys are wrong" in message

    @pytest.mark.asyncio
    async def test_validate_jwt_jwt_error(
        self, cognito_auth, valid_token, mock_jwks
    ):
        # Setup
        use_key = mock_jwks["keys"][0]

        with (
            patch.multiple(
                jwt,
                get_unverified_header=Mock(
                    return_value={"kid": "test-kid", "alg": "RS256"}
                ),
                decode=Mock(side_effect=jwt.JWTError("JWT error")),
            ),
            patch.object(
                cognito_auth,
                "get_use_keys",
                new=AsyncMock(return_value=[use_key]),
            ),
        ):
            # Call the method
            valid, message = await cognito_auth.validate_jwt(valid_token)

            # Assertions
            assert valid is False
            assert "Failed to decode or verify token" in message

    @pytest.mark.asyncio
    async def test_validate_jwt_jws_error(
        self, cognito_auth, valid_token, mock_jwks
    ):
        # Setup
        use_key = mock_jwks["keys"][0]

        with (
            patch.multiple(
                jwt,
                get_unverified_header=Mock(
                    return_value={"kid": "test-kid", "alg": "RS256"}
                ),
                decode=Mock(),
                get_unverified_claims=Mock(return_value={"token_use": "id"}),
            ),
            patch.object(
                jws, "verify", Mock(side_effect=jws.JWSError("JWS error"))
            ),
            patch.object(
                cognito_auth,
                "get_use_keys",
                new=AsyncMock(return_value=[use_key]),
            ),
        ):
            # Call the method
            valid, message = await cognito_auth.validate_jwt(valid_token)

            # Assertions
            assert valid is False
            assert "Failed to decode or verify token" in message

    @pytest.mark.asyncio
    async def test_validate_jwt_invalid_token_use(
        self, cognito_auth, valid_token, mock_jwks
    ):
        # Setup
        use_key = mock_jwks["keys"][0]

        with (
            patch.multiple(
                jwt,
                get_unverified_header=Mock(
                    return_value={"kid": "test-kid", "alg": "RS256"}
                ),
                decode=Mock(),
                get_unverified_claims=Mock(
                    return_value={"token_use": "invalid"}
                ),
            ),
            patch.object(jws, "verify", Mock()),
            patch.object(
                cognito_auth,
                "get_use_keys",
                new=AsyncMock(return_value=[use_key]),
            ),
        ):
            # Call the method
            valid, message = await cognito_auth.validate_jwt(valid_token)

            # Assertions
            assert valid is False
            assert "Token not of valid use" in message

    @pytest.mark.asyncio
    async def test_validate_jwt_invalid_audience(
        self, cognito_auth, valid_token, mock_jwks
    ):
        # Setup
        use_key = mock_jwks["keys"][0]

        with (
            patch.multiple(
                jwt,
                get_unverified_header=Mock(
                    return_value={"kid": "test-kid", "alg": "RS256"}
                ),
                decode=Mock(),
                get_unverified_claims=Mock(
                    return_value={"token_use": "id", "aud": "invalid-client"}
                ),
            ),
            patch.object(jws, "verify", Mock()),
            patch.object(
                cognito_auth,
                "get_use_keys",
                new=AsyncMock(return_value=[use_key]),
            ),
        ):
            # Call the method
            valid, message = await cognito_auth.validate_jwt(valid_token)

            # Assertions
            assert valid is False
            assert "Token audience not valid" in message

    def test_get_email_from_token_id_token(
        self, cognito_auth, valid_token, valid_claims
    ):
        # Setup
        with patch.object(
            jwt, "get_unverified_claims", return_value=valid_claims
        ):
            # Call the method
            result = cognito_auth.get_email_from_token(valid_token)

            # Assertions
            assert isinstance(result, AuthContext)
            assert result.email == valid_claims["email"]
            assert result.sub == valid_claims["custom:participantNickname"]
            assert result.email_verified == valid_claims["email_verified"]
            assert result.auth_time == valid_claims["auth_time"]
            assert result.admin == valid_claims["custom:isAdmin"]
            assert result.issuer == cognito_auth.ISSUER

    def test_get_email_from_token_access_token(
        self, cognito_auth, valid_token, valid_access_claims
    ):
        # Setup
        with patch.object(
            jwt, "get_unverified_claims", return_value=valid_access_claims
        ):
            # Call the method
            result = cognito_auth.get_email_from_token(valid_token)

            # Assertions
            assert isinstance(result, AuthContext)
            assert result.email is None
            assert result.sub is None
            assert result.email_verified is None

    def test_get_email_from_token_fallback_to_sub(
        self, cognito_auth, valid_token
    ):
        # Setup - claim without custom:participantNickname
        claims = {
            "token_use": "id",
            "email": "<EMAIL>",
            "email_verified": True,
            "sub": uuid4(),
            "auth_time": **********,
        }

        with patch.object(jwt, "get_unverified_claims", return_value=claims):
            # Call the method
            result = cognito_auth.get_email_from_token(valid_token)

            # Assertions
            assert result.sub == claims["sub"]

    @pytest.mark.asyncio
    async def test_authenticate_success(
        self, cognito_auth, valid_token, valid_claims
    ):
        # Setup
        token_with_prefix = f"{BEARER_PREFIX}{valid_token}"

        with (
            patch.object(
                cognito_auth,
                "get_email_from_token",
                return_value=AuthContext(
                    sub=uuid4(),
                    email="<EMAIL>",
                    email_verified=True,
                    auth_time=**********,
                    admin=False,
                    issuer=cognito_auth.ISSUER,
                ),
            ),
            patch.object(
                cognito_auth,
                "validate_jwt",
                new=AsyncMock(return_value=(True, None)),
            ),
        ):
            # Call the method
            result = await cognito_auth.authenticate(token_with_prefix)

            # Assertions
            assert isinstance(result, AuthContext)
            assert result.email == "<EMAIL>"
            assert (
                result.sub is not None
            )  # Should match the UUID from the AuthContext
            cognito_auth.get_email_from_token.assert_called_once_with(
                valid_token
            )
            cognito_auth.validate_jwt.assert_called_once_with(valid_token)

    @pytest.mark.asyncio
    async def test_authenticate_email_not_found(
        self, cognito_auth, valid_token
    ):
        # Setup
        with patch.object(
            cognito_auth,
            "get_email_from_token",
            return_value=AuthContext(
                sub=uuid4(),
                email=None,
                email_verified=True,
            ),
        ):
            # Call the method and assert it raises
            with pytest.raises(InvalidToken, match="Email not found in token"):
                await cognito_auth.authenticate(valid_token)

    @pytest.mark.asyncio
    async def test_authenticate_email_not_verified(
        self, cognito_auth, valid_token
    ):
        # Setup
        with patch.object(
            cognito_auth,
            "get_email_from_token",
            return_value=AuthContext(
                sub=uuid4(),
                email="<EMAIL>",
                email_verified=False,
            ),
        ):
            # Call the method and assert it raises
            with pytest.raises(InvalidToken, match="Email is not verified"):
                await cognito_auth.authenticate(valid_token)

    @pytest.mark.asyncio
    async def test_authenticate_invalid_token(self, cognito_auth, valid_token):
        # Setup
        with (
            patch.object(
                cognito_auth,
                "get_email_from_token",
                return_value=AuthContext(
                    sub=uuid4(),
                    email="<EMAIL>",
                    email_verified=True,
                ),
            ),
            patch.object(
                cognito_auth,
                "validate_jwt",
                new=AsyncMock(return_value=(False, "Invalid token")),
            ),
        ):
            # Call the method and assert it raises
            with pytest.raises(
                InvalidToken, match="Token validation failed: Invalid token"
            ):
                await cognito_auth.authenticate(valid_token)


class TestSoleraAuthenticator:
    @pytest.fixture
    def solera_auth(self):
        return SoleraAuthenticator()

    @pytest.fixture
    def mock_participant(self):
        participant = Mock()
        participant.email = "<EMAIL>"
        participant.status = ParticipantStatus.ACTIVE
        return participant

    @pytest.fixture
    def mock_solera_participant(self, mock_participant):
        solera_participant = Mock()
        solera_participant.participant = mock_participant
        return solera_participant

    def test_get_issuer(self, solera_auth):
        assert solera_auth.get_issuer() == solera_auth.ISSUER

    @pytest.mark.asyncio
    async def test_verify_user_success(
        self, solera_auth, mock_solera_participant
    ):
        # Setup
        solera_key = "test-solera-key"

        # Mock the entire verify_user method
        solera_auth.verify_user = AsyncMock(
            return_value=mock_solera_participant
        )

        # Call the method
        result = await solera_auth.verify_user(solera_key)

        # Assertions
        assert result == mock_solera_participant
        solera_auth.verify_user.assert_called_once_with(solera_key)

    @pytest.mark.asyncio
    async def test_verify_user_not_found(self, solera_auth):
        # Setup
        solera_key = "test-solera-key"

        # Mock the verify_user method to raise an exception
        async def mock_verify_user(key):
            raise InvalidToken("Token not found")

        solera_auth.verify_user = mock_verify_user

        # Call the method and assert it raises
        with pytest.raises(InvalidToken, match="Token not found"):
            await solera_auth.verify_user(solera_key)

    @pytest.mark.asyncio
    async def test_authenticate_success(self, solera_auth, mock_participant):
        # Setup
        solera_token = f"{SOLERA_PREFIX} test-solera-key"
        mock_solera_participant = Mock()
        mock_solera_participant.participant = mock_participant

        async def mock_authenticate(token):
            return mock_participant

        solera_auth.authenticate = mock_authenticate

        # Call the method
        result = await solera_auth.authenticate(solera_token)

        # Assertions
        assert result == mock_participant

    @pytest.mark.asyncio
    async def test_authenticate_multiple_tokens(
        self, solera_auth, mock_participant
    ):
        # Setup - with multiple tokens, should use the last one
        solera_token = f"invalid-token, {SOLERA_PREFIX} test-solera-key"

        async def mock_authenticate(token):
            # Verify that the token is correctly parsed
            assert "test-solera-key" in token
            return mock_participant

        solera_auth.authenticate = mock_authenticate

        # Call the method
        result = await solera_auth.authenticate(solera_token)

        # Assertions
        assert result == mock_participant

    @pytest.mark.asyncio
    async def test_authenticate_email_not_found(self, solera_auth):
        # Setup
        solera_token = f"{SOLERA_PREFIX} test-solera-key"

        # Create a custom authenticate method that raises the expected exception
        async def mock_authenticate(token):
            raise InvalidToken("Email not found in token")

        solera_auth.authenticate = mock_authenticate

        # Call the method and assert it raises
        with pytest.raises(InvalidToken, match="Email not found in token"):
            await solera_auth.authenticate(solera_token)

    @pytest.mark.asyncio
    async def test_authenticate_rejected_status(self, solera_auth):
        # Setup
        solera_token = f"{SOLERA_PREFIX} test-solera-key"
        mock_participant = Mock()
        mock_participant.email = "<EMAIL>"
        mock_participant.status = ParticipantStatus.REJECTED

        async def mock_authenticate(token):
            participant = await solera_auth.verify_user(token.split()[-1])
            if not participant.email:
                raise InvalidToken("Email not found in token")
            if participant.status == ParticipantStatus.REJECTED:
                raise InvalidToken("Token rejected")
            return participant

        solera_auth.authenticate = mock_authenticate

        with patch.object(
            solera_auth,
            "verify_user",
            new=AsyncMock(return_value=mock_participant),
        ):
            # Call the method and assert it raises
            with pytest.raises(InvalidToken, match="Token rejected"):
                await solera_auth.authenticate(solera_token)


class TestParticipantAndProviderAuthenticators:
    def test_participant_authenticator_init(self, mock_settings):
        # Directly patch the settings in the ParticipantAuthenticator class
        with (
            patch.object(
                ParticipantAuthenticator,
                "APP_CLIENT_ID",
                mock_settings.COGNITO_APP_CLIENT_ID,
            ),
            patch.object(
                ParticipantAuthenticator,
                "POOL_ID",
                mock_settings.COGNITO_USER_POOL_ID,
            ),
        ):
            auth = ParticipantAuthenticator()
            assert auth.APP_CLIENT_ID == mock_settings.COGNITO_APP_CLIENT_ID
            assert auth.POOL_ID == mock_settings.COGNITO_USER_POOL_ID
            assert auth.ISSUER == "participant"

    def test_provider_authenticator_init(self, mock_settings):
        # Directly patch the settings in the ProviderAuthenticator class
        with (
            patch.object(
                ProviderAuthenticator,
                "APP_CLIENT_ID",
                mock_settings.PROVIDER_APP_CLIENT_ID,
            ),
            patch.object(
                ProviderAuthenticator,
                "POOL_ID",
                mock_settings.PROVIDER_USER_POOL_ID,
            ),
        ):
            auth = ProviderAuthenticator()
            assert auth.APP_CLIENT_ID == mock_settings.PROVIDER_APP_CLIENT_ID
            assert auth.POOL_ID == mock_settings.PROVIDER_USER_POOL_ID
            assert auth.ISSUER == "provider"
