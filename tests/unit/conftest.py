import pytest
from tortoise.contrib.test import (
    finalizer,
    initializer,
)

import asyncio

MODULES = [
    "aerich.models",
    "ciba_participant.activity.models",
    "ciba_participant.classes.models",
    "ciba_participant.cohort.models",
    "ciba_participant.participant.models",
    "ciba_participant.program.models",
    "ciba_participant.content_library.models",
]


# Configure pytest-asyncio to use function scope for event loops
pytest_asyncio_scope = "function"


@pytest.fixture(scope="function")
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function", autouse=True)
def initialize_tests(request, event_loop):
    """Initialize test database for each test function."""
    initializer(
        modules=MODULES,
        db_url="sqlite://:memory:",
        app_label="models",
        loop=event_loop,
    )
    request.addfinalizer(finalizer)


# Old implementation that used session scope
"""
@pytest.fixture(scope="session", autouse=True)
async def initialize_test_db(request):
    config = getDBConfig(
        modules=MODULES,
        app_label="models",
    )

    await _init_db(config)

    def shutdown():
        # Use a new event loop for cleanup
        loop = asyncio.new_event_loop()
        loop.run_until_complete(Tortoise._drop_databases())
        loop.close()

    request.addfinalizer(shutdown)
"""
