from unittest.mock import patch
from datetime import datetime
from ciba_participant.helpers.timezone_conversion import (
    tz_to_utc,
    apply_tz_offset,
    TIMEZONE_HEADER,
)
from strawberry.types import Info as InfoType
from strawberry import Info
from strawberry.types.field import <PERSON><PERSON><PERSON><PERSON>ield
from freezegun import freeze_time

FROZEN_TIME = "2024-11-20 12:00:01"


@freeze_time(FROZEN_TIME)
def test_no_headers():
    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        info: InfoType = Info(GraphQLResolveInfo, field)

        info.context.request.headers = {}

        converted = apply_tz_offset(datetime.now(), info)
        assert converted.strftime("%Y-%m-%d %H:%M:%S") == FROZEN_TIME


@freeze_time(FROZEN_TIME)
def test_valid_headers():
    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        info: InfoType = Info(GraphQLResolveInfo, field)

        info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}

        converted = apply_tz_offset(datetime.now(), info)
        assert converted.strftime("%Y-%m-%d %H:%M:%S") == FROZEN_TIME.replace(
            "12", "14"
        )


@freeze_time(FROZEN_TIME)
def test_to_utc():
    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        info: InfoType = Info(GraphQLResolveInfo, field)

        info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}

        converted = tz_to_utc(datetime.now(), info)
        assert converted.strftime("%Y-%m-%d %H:%M:%S") == FROZEN_TIME.replace(
            "12", "10"
        )


@freeze_time(FROZEN_TIME)
def test_invalid_headers():
    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        info: InfoType = Info(GraphQLResolveInfo, field)

        info.context.request.headers = {TIMEZONE_HEADER: "000:00"}

        converted = apply_tz_offset(datetime.now(), info)
        assert converted.strftime("%Y-%m-%d %H:%M:%S") == FROZEN_TIME
