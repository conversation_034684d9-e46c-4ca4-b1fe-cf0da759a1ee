import base64
import hashlib
import hmac
import pytest
from unittest.mock import MagicMock, patch
from botocore.exceptions import ClientError

from app.integrations.cognito import (
    get_cognito_client,
    get_secret_hash,
    admin_create_user,
    admin_add_user_to_group,
    admin_set_user_password,
    admin_get_user,
    forgot_password,
    admin_confirm_sign_up,
    admin_disable_user,
    admin_delete_user,
    admin_enable_user,
)


def test_get_cognito_client():
    """Test get_cognito_client function."""
    # Mock boto3 client
    mock_client = MagicMock()

    with patch(
        "app.integrations.cognito.boto3.client", return_value=mock_client
    ) as mock_boto3:
        # Mock settings
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.AWS_REGION = "us-east-1"

            client = get_cognito_client()

            assert client == mock_client
            mock_boto3.assert_called_once_with(
                "cognito-idp", region_name="us-east-1"
            )


def test_get_secret_hash():
    """Test get_secret_hash function."""
    username = "<EMAIL>"
    client_id = "test-client-id"
    client_secret = "test-client-secret"

    with patch("app.integrations.cognito.settings") as mock_settings:
        mock_settings.COGNITO_SERVER_CLIENT_ID = client_id
        mock_settings.COGNITO_SERVER_CLIENT_SECRET = client_secret

        result = get_secret_hash(username)

        # Calculate expected hash manually to verify
        msg = username + client_id
        dig = hmac.new(
            str(client_secret).encode("utf-8"),
            msg=str(msg).encode("utf-8"),
            digestmod=hashlib.sha256,
        ).digest()
        expected_hash = base64.b64encode(dig).decode()

        assert result == expected_hash


def test_admin_create_user_success():
    """Test admin_create_user function when successful."""
    email = "<EMAIL>"
    mock_response = {"User": {"Username": email}}
    mock_client = MagicMock()
    mock_client.admin_create_user.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_create_user(email)

            assert result == mock_response
            mock_client.admin_create_user.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
                UserAttributes=[
                    {"Name": "email", "Value": email},
                    {"Name": "email_verified", "Value": "true"},
                ],
                MessageAction="SUPPRESS",
            )


def test_admin_create_user_user_exists():
    """Test admin_create_user function when user already exists."""
    email = "<EMAIL>"
    mock_user_response = {"Username": email}
    mock_client = MagicMock()

    # Mock the client to raise UsernameExistsException
    error_response = {
        "Error": {
            "Code": "UsernameExistsException",
            "Message": "User already exists",
        }
    }
    mock_client.admin_create_user.side_effect = ClientError(
        error_response, "AdminCreateUser"
    )

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            # Mock admin_get_user to return a user
            with patch(
                "app.integrations.cognito.admin_get_user",
                return_value=mock_user_response,
            ):
                result = admin_create_user(email)

                assert result == {"User": mock_user_response}
                mock_client.admin_create_user.assert_called_once()


def test_admin_create_user_other_error():
    """Test admin_create_user function when other ClientError occurs."""
    email = "<EMAIL>"
    mock_client = MagicMock()

    # Mock the client to raise a different ClientError
    error_response = {
        "Error": {"Code": "OtherError", "Message": "Some other error"}
    }
    mock_client.admin_create_user.side_effect = ClientError(
        error_response, "AdminCreateUser"
    )

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            with pytest.raises(ClientError):
                admin_create_user(email)

            mock_client.admin_create_user.assert_called_once()


def test_admin_add_user_to_group_success():
    """Test admin_add_user_to_group function when successful."""
    email = "<EMAIL>"
    mock_client = MagicMock()

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"
            mock_settings.COGNITO_USER_GROUP = "test-group"

            result = admin_add_user_to_group(email)

            assert result is True
            mock_client.admin_add_user_to_group.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
                GroupName="test-group",
            )


def test_admin_add_user_to_group_resource_not_found():
    """Test admin_add_user_to_group function when group does not exist."""
    email = "<EMAIL>"
    mock_client = MagicMock()

    # Mock the client to raise ResourceNotFoundException
    error_response = {
        "Error": {
            "Code": "ResourceNotFoundException",
            "Message": "Group not found",
        }
    }
    mock_client.admin_add_user_to_group.side_effect = ClientError(
        error_response, "AdminAddUserToGroup"
    )

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"
            mock_settings.COGNITO_USER_GROUP = "test-group"

            result = admin_add_user_to_group(email)

            assert result is False
            mock_client.admin_add_user_to_group.assert_called_once()


def test_admin_add_user_to_group_other_error():
    """Test admin_add_user_to_group function when other ClientError occurs."""
    email = "<EMAIL>"
    mock_client = MagicMock()

    # Mock the client to raise a different ClientError
    error_response = {
        "Error": {"Code": "OtherError", "Message": "Some other error"}
    }
    mock_client.admin_add_user_to_group.side_effect = ClientError(
        error_response, "AdminAddUserToGroup"
    )

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"
            mock_settings.COGNITO_USER_GROUP = "test-group"

            with pytest.raises(ClientError):
                admin_add_user_to_group(email)

            mock_client.admin_add_user_to_group.assert_called_once()


def test_admin_set_user_password():
    """Test admin_set_user_password function."""
    email = "<EMAIL>"
    password = "TestPassword123!"
    mock_response = {"ResponseMetadata": {"RequestId": "test-request-id"}}
    mock_client = MagicMock()
    mock_client.admin_set_user_password.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_set_user_password(email, password, True)

            assert result == mock_response
            mock_client.admin_set_user_password.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
                Password=password,
                Permanent=True,
            )


def test_admin_get_user_success():
    """Test admin_get_user function when successful."""
    email = "<EMAIL>"
    mock_response = {"Username": email}
    mock_client = MagicMock()
    mock_client.admin_get_user.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_get_user(email)

            assert result == mock_response
            mock_client.admin_get_user.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
            )


def test_admin_get_user_not_found():
    """Test admin_get_user function when user does not exist."""
    email = "<EMAIL>"
    mock_client = MagicMock()

    # Mock the client to raise UserNotFoundException
    error_response = {
        "Error": {
            "Code": "UserNotFoundException",
            "Message": "User does not exist",
        }
    }
    mock_client.admin_get_user.side_effect = ClientError(
        error_response, "AdminGetUser"
    )

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_get_user(email)

            assert result == {}
            mock_client.admin_get_user.assert_called_once()


def test_admin_get_user_other_error():
    """Test admin_get_user function when other ClientError occurs."""
    email = "<EMAIL>"
    mock_client = MagicMock()

    # Mock the client to raise a different ClientError
    error_response = {
        "Error": {"Code": "OtherError", "Message": "Some other error"}
    }
    mock_client.admin_get_user.side_effect = ClientError(
        error_response, "AdminGetUser"
    )

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            # The function should re-raise other errors
            with pytest.raises(ClientError):
                admin_get_user(email)

            mock_client.admin_get_user.assert_called_once()


def test_forgot_password():
    """Test forgot_password function."""
    email = "<EMAIL>"
    mock_response = {"CodeDeliveryDetails": {"Destination": email}}
    mock_client = MagicMock()
    mock_client.forgot_password.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_SERVER_CLIENT_ID = "test-client-id"

            # Mock get_secret_hash
            secret_hash = "test-secret-hash"
            with patch(
                "app.integrations.cognito.get_secret_hash",
                return_value=secret_hash,
            ):
                result = forgot_password(email)

                assert result == mock_response
                mock_client.forgot_password.assert_called_once_with(
                    ClientId="test-client-id",
                    SecretHash=secret_hash,
                    Username=email,
                )


def test_admin_confirm_sign_up():
    """Test admin_confirm_sign_up function."""
    email = "<EMAIL>"
    mock_response = {"ResponseMetadata": {"RequestId": "test-request-id"}}
    mock_client = MagicMock()
    mock_client.admin_update_user_attributes.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_confirm_sign_up(email)

            assert result == mock_response
            mock_client.admin_update_user_attributes.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
                UserAttributes=[
                    {"Name": "email_verified", "Value": "true"},
                ],
            )


def test_admin_disable_user():
    """Test admin_disable_user function."""
    email = "<EMAIL>"
    mock_response = {"ResponseMetadata": {"RequestId": "test-request-id"}}
    mock_client = MagicMock()
    mock_client.admin_disable_user.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_disable_user(email)

            assert result == mock_response
            mock_client.admin_disable_user.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
            )


def test_admin_delete_user():
    """Test admin_delete_user function."""
    email = "<EMAIL>"
    mock_response = {"ResponseMetadata": {"RequestId": "test-request-id"}}
    mock_client = MagicMock()
    mock_client.admin_delete_user.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_delete_user(email)

            assert result == mock_response
            mock_client.admin_delete_user.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
            )


def test_admin_enable_user():
    """Test admin_enable_user function."""
    email = "<EMAIL>"
    mock_response = {"ResponseMetadata": {"RequestId": "test-request-id"}}
    mock_client = MagicMock()
    mock_client.admin_enable_user.return_value = mock_response

    with patch(
        "app.integrations.cognito.get_cognito_client", return_value=mock_client
    ):
        with patch("app.integrations.cognito.settings") as mock_settings:
            mock_settings.COGNITO_USER_POOL_ID = "test-pool-id"

            result = admin_enable_user(email)

            assert result == mock_response
            mock_client.admin_enable_user.assert_called_once_with(
                UserPoolId="test-pool-id",
                Username=email,
            )
