from unittest.mock import AsyncMock, MagicMock, Mock, patch
from uuid import UUID

import pytest
from httpx import HTT<PERSON><PERSON>rror, HTTPStatusError, Response, codes

from ciba_participant.rpm_api.api import (
    sync_measures,
    pair_transtek_device,
    get_carrier_list,
    update_transtek_tracking_data,
    get_transtek_device_info,
    disconnect_device,
    get_single_device_status,
)
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.models import (
    TranstekTrackingData,
    TranstekDeviceInfo,
    DeviceTypeEnum,
    DeviceModelEnum,
    DeviceStatusEnum,
    DetailedConnectionStatus,
)

test_participant_id = UUID("8f844654-e2b1-4d75-bbd7-84d492b327a7")
test_participant_email = "<EMAIL>"
test_device_id = UUID("12345678-1234-1234-1234-123456789012")
GET_SYNC_DATE = "ciba_participant.rpm_api.api.get_sync_start_date"
POST_METHOD = "ciba_participant.rpm_api.api.AsyncClient.post"
GET_METHOD = "ciba_participant.rpm_api.api.AsyncClient.get"


@pytest.mark.asyncio
async def test_sync_measures_with_api_call_error():
    """
    sync_measures should raise an RPMCallError
    when the endpoint call raises an HTTPError.
    """
    test_error = HTTPError("test network error.")

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error),
    ):
        with pytest.raises(RPMCallError):
            await sync_measures(test_participant_id)


@pytest.mark.asyncio
async def test_sync_measures_with_error_in_response():
    """
    sync_measures should raise an RPMCallError
    when the api call return an unsuccessful response.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Invalid device."})
    test_response.status_code = codes.BAD_REQUEST
    test_error = HTTPStatusError(
        "Bad Request", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        with pytest.raises(RPMCallError) as expected_error:
            await sync_measures(test_participant_id)

        assert expected_error.value.args[0] == "Invalid device."


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_json, expected_value",
    [
        ({"synced": True}, True),
        ({"synced": False}, False),
    ],
)
async def test_get_latest_data_success(test_json, expected_value):
    """
    get_latest_data should return the latest data from RPM.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value=test_json)

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=1749168000),
        patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        actual_value = await sync_measures(test_participant_id)

        assert actual_value.success == expected_value


# Tests for pair_transtek_device function
@pytest.mark.asyncio
async def test_pair_transtek_device_success_with_device_id():
    """
    pair_transtek_device should return pairing result when successful with device_id.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={"status": "paired", "device_id": "test_device_123"}
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await pair_transtek_device(
            participant_id=test_participant_id,
            participant_email=test_participant_email,
            serial_number="test_device_123",
        )

        assert result == {"status": "paired", "device_id": "test_device_123"}


@pytest.mark.asyncio
async def test_pair_transtek_device_success_with_imei():
    """
    pair_transtek_device should return pairing result when successful with imei.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={"status": "paired", "imei": "123456789012345"}
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await pair_transtek_device(
            participant_id=test_participant_id,
            participant_email=test_participant_email,
            imei="123456789012345",
        )

        assert result == {"status": "paired", "imei": "123456789012345"}


@pytest.mark.asyncio
async def test_pair_transtek_device_success_with_both_parameters():
    """
    pair_transtek_device should return pairing result when successful with both device_id and imei.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "status": "paired",
            "device_id": "test_device_123",
            "imei": "123456789012345",
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await pair_transtek_device(
            participant_id=test_participant_id,
            participant_email=test_participant_email,
            serial_number="test_device_123",
            imei="123456789012345",
        )

        assert result == {
            "status": "paired",
            "device_id": "test_device_123",
            "imei": "123456789012345",
        }


@pytest.mark.asyncio
async def test_pair_transtek_device_with_http_status_error():
    """
    pair_transtek_device should raise RPMCallError when API returns HTTP status error.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Device already paired"})
    test_response.status_code = codes.BAD_REQUEST
    test_error = HTTPStatusError(
        "Bad Request", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await pair_transtek_device(
                participant_id=test_participant_id,
                participant_email=test_participant_email,
                serial_number="test_device_123",
            )

        assert expected_error.value.args[0] == "Device already paired"


@pytest.mark.asyncio
async def test_pair_transtek_device_with_http_error():
    """
    pair_transtek_device should raise RPMCallError when HTTP error occurs.
    """
    test_error = HTTPError("Network connection failed")

    with patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await pair_transtek_device(
                participant_id=test_participant_id,
                participant_email=test_participant_email,
                serial_number="test_device_123",
            )

        assert (
            expected_error.value.args[0]
            == "An error occurred pairing device through RPM service"
        )


# Tests for get_carrier_list function
@pytest.mark.asyncio
async def test_get_carrier_list_success():
    """
    get_carrier_list should return list of carriers when successful.
    """
    test_carriers = [
        {"id": 1, "name": "FedEx", "code": "fedex"},
        {"id": 2, "name": "UPS", "code": "ups"},
        {"id": 3, "name": "USPS", "code": "usps"},
    ]
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value={"carriers": test_carriers})

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_carrier_list()

        assert result == test_carriers
        assert len(result) == 3
        assert result[0]["name"] == "FedEx"


@pytest.mark.asyncio
async def test_get_carrier_list_success_empty_list():
    """
    get_carrier_list should return empty list when no carriers available.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value={"carriers": []})

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_carrier_list()

        assert result == []


@pytest.mark.asyncio
async def test_get_carrier_list_with_http_status_error():
    """
    get_carrier_list should raise RPMCallError when API returns HTTP status error.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Service unavailable"})
    test_response.status_code = codes.SERVICE_UNAVAILABLE
    test_error = HTTPStatusError(
        "Service Unavailable", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await get_carrier_list()

        assert expected_error.value.args[0] == "Service unavailable"


@pytest.mark.asyncio
async def test_get_carrier_list_with_http_error():
    """
    get_carrier_list should raise RPMCallError when HTTP error occurs.
    """
    test_error = HTTPError("Connection timeout")

    with patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await get_carrier_list()

        assert (
            expected_error.value.args[0]
            == "An error occurred getting carrier list through RPM service"
        )


# Tests for update_transtek_tracking_data function
@pytest.mark.asyncio
async def test_update_transtek_tracking_data_success_with_imei():
    """
    update_transtek_tracking_data should return tracking URL when successful with imei.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="1Z999AA1234567890", carrier="ups"
    )
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "tracking_url": "https://tracking.example.com/track/1Z999AA1234567890"
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await update_transtek_tracking_data(
            imei="123456789012345", tracking_data=tracking_data
        )

        assert result == {
            "tracking_url": "https://tracking.example.com/track/1Z999AA1234567890"
        }


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_success_with_serial_number():
    """
    update_transtek_tracking_data should return tracking URL when successful with serial_number.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="1234567890123456", carrier="fedex"
    )
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "tracking_url": "https://tracking.example.com/track/1234567890123456"
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await update_transtek_tracking_data(
            serial_number="SN123456789", tracking_data=tracking_data
        )

        assert result == {
            "tracking_url": "https://tracking.example.com/track/1234567890123456"
        }


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_success_with_both_identifiers():
    """
    update_transtek_tracking_data should return tracking URL when successful with both imei and serial_number.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="9400111899562123456789", carrier="usps"
    )
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "tracking_url": "https://tracking.example.com/track/9400111899562123456789",
            "status": "updated",
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await update_transtek_tracking_data(
            imei="123456789012345",
            serial_number="SN123456789",
            tracking_data=tracking_data,
        )

        assert result == {
            "tracking_url": "https://tracking.example.com/track/9400111899562123456789",
            "status": "updated",
        }


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_with_http_status_error():
    """
    update_transtek_tracking_data should raise RPMCallError when API returns HTTP status error.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="INVALID_TRACKING", carrier="invalid_carrier"
    )
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Invalid tracking number format"})
    test_response.status_code = codes.BAD_REQUEST
    test_error = HTTPStatusError(
        "Bad Request", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await update_transtek_tracking_data(
                imei="123456789012345", tracking_data=tracking_data
            )

        assert expected_error.value.args[0] == "Invalid tracking number format"


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_with_http_error():
    """
    update_transtek_tracking_data should raise RPMCallError when HTTP error occurs.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="1Z999AA1234567890", carrier="ups"
    )
    test_error = HTTPError("Request timeout")

    with patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await update_transtek_tracking_data(
                imei="123456789012345", tracking_data=tracking_data
            )

        assert (
            expected_error.value.args[0]
            == "An error occurred updating tracking data through RPM service"
        )


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_with_none_tracking_data():
    """
    update_transtek_tracking_data should handle None tracking_data gracefully.
    """
    # This test verifies the function behavior when tracking_data is None
    # The function should fail when trying to access tracking_data.tracking_number
    with pytest.raises(AttributeError):
        await update_transtek_tracking_data(imei="123456789012345", tracking_data=None)


# Tests for get_transtek_device_info function
@pytest.mark.asyncio
async def test_get_transtek_device_info_success():
    """
    get_transtek_device_info should return device info when successful.
    """
    test_device_info_json = {
        "id": "device_123",
        "device_id": "test_device_123",
        "imei": "123456789012345",
        "model": "BP Monitor",
        "device_type": "transtek",
        "status": "active",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "tracking_number": "1Z999AA1234567890",
        "carrier": "ups",
        "timezone": "UTC",
        "member_id": str(test_participant_id),
    }
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value=test_device_info_json)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_transtek_device_info(participant_id=test_participant_id)

        assert isinstance(result, TranstekDeviceInfo)
        assert result.device_id == "test_device_123"
        assert result.imei == "123456789012345"
        assert result.status == "active"
        assert result.model == "BP Monitor"
        assert result.tracking_number == "1Z999AA1234567890"


@pytest.mark.asyncio
async def test_get_transtek_device_info_success_minimal_response():
    """
    get_transtek_device_info should return device info when successful with minimal required fields.
    """
    test_device_info_json = {
        "id": "minimal_device_456",
        "device_id": "minimal_device",
        "imei": "987654321098765",
        "model": "Basic Monitor",
        "device_type": "transtek",
        "status": "inactive",
        "created_at": "2024-01-10T08:00:00Z",
        "updated_at": "2024-01-10T08:00:00Z",
    }
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value=test_device_info_json)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_transtek_device_info(participant_id=test_participant_id)

        assert isinstance(result, TranstekDeviceInfo)
        assert result.device_id == "minimal_device"
        assert result.status == "inactive"
        assert result.tracking_number is None
        assert result.carrier is None


@pytest.mark.asyncio
async def test_get_transtek_device_info_with_http_status_error():
    """
    get_transtek_device_info should raise RPMCallError when API returns HTTP status error.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Device not found"})
    test_response.status_code = codes.NOT_FOUND
    test_error = HTTPStatusError(
        "Not Found", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await get_transtek_device_info(participant_id=test_participant_id)

        assert expected_error.value.args[0] == "Device not found"


@pytest.mark.asyncio
async def test_get_transtek_device_info_with_http_error():
    """
    get_transtek_device_info should raise RPMCallError when HTTP error occurs.
    """
    test_error = HTTPError("Connection timeout")

    with patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await get_transtek_device_info(participant_id=test_participant_id)

        assert (
            expected_error.value.args[0]
            == "An error occurred getting device info through RPM service"
        )


# Tests for disconnect_device function
@pytest.mark.asyncio
async def test_disconnect_device_success_transtek():
    """
    disconnect_device should return disconnect result when successful with transtek device.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={"status": "disconnected", "device_id": str(test_device_id)}
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await disconnect_device(
            participant_id=test_participant_id,
            device_id=test_device_id,
            device_type=DeviceTypeEnum.TRANSTEK,
        )

        assert result == {"status": "disconnected", "device_id": str(test_device_id)}


@pytest.mark.asyncio
async def test_disconnect_device_success_withings():
    """
    disconnect_device should return disconnect result when successful with withings device.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={"status": "disconnected", "device_type": "withings"}
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await disconnect_device(
            participant_id=test_participant_id,
            device_id=test_device_id,
            device_type=DeviceTypeEnum.WITHINGS,
        )

        assert result == {"status": "disconnected", "device_type": "withings"}


@pytest.mark.asyncio
async def test_disconnect_device_with_http_status_error():
    """
    disconnect_device should raise RPMCallError when API returns HTTP status error.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Device not found"})
    test_response.status_code = codes.NOT_FOUND
    test_error = HTTPStatusError(
        "Not Found", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await disconnect_device(
                participant_id=test_participant_id,
                device_id=test_device_id,
                device_type=DeviceTypeEnum.TRANSTEK,
            )

        assert expected_error.value.args[0] == "Device not found"


@pytest.mark.asyncio
async def test_disconnect_device_with_http_error():
    """
    disconnect_device should raise RPMCallError when HTTP error occurs.
    """
    test_error = HTTPError("Network connection failed")

    with patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await disconnect_device(
                participant_id=test_participant_id,
                device_id=test_device_id,
                device_type=DeviceTypeEnum.FITBIT,
            )

        assert (
            expected_error.value.args[0]
            == "An error occurred disconnecting device through RPM service"
        )


# Tests for get_single_device_status function
@pytest.mark.asyncio
async def test_get_single_device_status_success_healthy_device():
    """
    get_single_device_status should return DetailedConnectionStatus with CONNECTED status
    when the device is healthy and API call is successful.
    """
    test_response_data = {
        "token": "test_token_123",
        "account_id": "account_456",
        "subscription": {"expires_in": 3600},
        "healthy": True,
        "model": "scale",
        "device_metadata": {"battery": 85, "signal": 95},
    }
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_123",
            device_type=DeviceTypeEnum.WITHINGS,
            device_model=DeviceModelEnum.SCALE,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.token == "test_token_123"
        assert result.account_id == "account_456"
        assert result.healthy is True
        assert result.device == DeviceTypeEnum.WITHINGS
        assert result.status == DeviceStatusEnum.CONNECTED
        assert result.model == "scale"
        assert result.battery == 85
        assert result.signal == 95


@pytest.mark.asyncio
async def test_get_single_device_status_success_unhealthy_device():
    """
    get_single_device_status should return DetailedConnectionStatus with RECONNECT status
    when the device is not healthy but API call is successful.
    """
    test_response_data = {
        "token": "test_token_456",
        "account_id": "account_789",
        "subscription": None,
        "healthy": False,
        "model": "bpm",
        "device_metadata": {"battery": 25, "signal": 45},
    }
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_456",
            device_type=DeviceTypeEnum.TRANSTEK,
            device_model=DeviceModelEnum.BPM,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.token == "test_token_456"
        assert result.account_id == "account_789"
        assert result.healthy is False
        assert result.device == DeviceTypeEnum.TRANSTEK
        assert result.status == DeviceStatusEnum.RECONNECT
        assert result.model == "bpm"
        assert result.battery == 25
        assert result.signal == 45


@pytest.mark.asyncio
async def test_get_single_device_status_success_minimal_response():
    """
    get_single_device_status should handle minimal response data gracefully.
    """
    test_response_data = {"healthy": True}
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_minimal", device_type=DeviceTypeEnum.FITBIT
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.token is None
        assert result.account_id is None
        assert result.subscription is None
        assert result.healthy is True
        assert result.device == DeviceTypeEnum.FITBIT
        assert result.status == DeviceStatusEnum.CONNECTED
        assert result.model is None
        assert result.battery is None
        assert result.signal is None


@pytest.mark.asyncio
async def test_get_single_device_status_success_empty_device_metadata():
    """
    get_single_device_status should handle empty device_metadata gracefully.
    """
    test_response_data = {
        "token": "test_token_empty",
        "healthy": True,
        "device_metadata": {},
    }
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_empty_meta",
            device_type=DeviceTypeEnum.DEXCOM,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.token == "test_token_empty"
        assert result.healthy is True
        assert result.device == DeviceTypeEnum.DEXCOM
        assert result.status == DeviceStatusEnum.CONNECTED
        assert result.battery is None
        assert result.signal is None


@pytest.mark.asyncio
async def test_get_single_device_status_success_null_device_metadata():
    """
    get_single_device_status should handle null device_metadata gracefully.
    """
    test_response_data = {
        "token": "test_token_null",
        "healthy": False,
        "device_metadata": None,
    }
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_null_meta",
            device_type=DeviceTypeEnum.OURARING,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.token == "test_token_null"
        assert result.healthy is False
        assert result.device == DeviceTypeEnum.OURARING
        assert result.status == DeviceStatusEnum.RECONNECT
        assert result.battery is None
        assert result.signal is None


@pytest.mark.asyncio
async def test_get_single_device_status_with_device_model_none():
    """
    get_single_device_status should handle device_model=None by not including it in params.
    """
    test_response_data = {"healthy": True, "token": "test_token_no_model"}
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch(
        GET_METHOD, new_callable=AsyncMock, return_value=test_response
    ) as mock_get:
        result = await get_single_device_status(
            participant_id="test_participant_no_model",
            device_type=DeviceTypeEnum.WITHINGS,
            device_model=None,
        )

        # Verify the request was made without device_model parameter
        mock_get.assert_called_once_with("/devices/get_status")

        assert isinstance(result, DetailedConnectionStatus)
        assert result.healthy is True
        assert result.device == DeviceTypeEnum.WITHINGS
        assert result.status == DeviceStatusEnum.CONNECTED


@pytest.mark.asyncio
async def test_get_single_device_status_non_ok_status_code():
    """
    get_single_device_status should return NOT_CONNECTED when API returns non-OK status code.
    """
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.NOT_FOUND

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_not_found",
            device_type=DeviceTypeEnum.FITBIT,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.healthy is False
        assert result.device == DeviceTypeEnum.FITBIT
        assert result.status == DeviceStatusEnum.NOT_CONNECTED
        assert result.token is None
        assert result.account_id is None
        assert result.subscription is None
        assert result.model is None
        assert result.battery is None
        assert result.signal is None


@pytest.mark.asyncio
async def test_get_single_device_status_http_error():
    """
    get_single_device_status should return NOT_CONNECTED when HTTP error occurs.
    """
    test_error = HTTPError("Connection timeout")

    with patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error):
        result = await get_single_device_status(
            participant_id="test_participant_error", device_type=DeviceTypeEnum.DEXCOM
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.healthy is False
        assert result.device == DeviceTypeEnum.DEXCOM
        assert result.status == DeviceStatusEnum.NOT_CONNECTED
        assert result.token is None
        assert result.account_id is None


@pytest.mark.asyncio
async def test_get_single_device_status_http_status_error():
    """
    get_single_device_status should return NOT_CONNECTED when HTTPStatusError occurs.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Unauthorized"})
    test_response.status_code = codes.UNAUTHORIZED
    test_error = HTTPStatusError(
        "Unauthorized", request=MagicMock(), response=test_response
    )

    with patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error):
        result = await get_single_device_status(
            participant_id="test_participant_unauthorized",
            device_type=DeviceTypeEnum.TRANSTEK,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.healthy is False
        assert result.device == DeviceTypeEnum.TRANSTEK
        assert result.status == DeviceStatusEnum.NOT_CONNECTED


@pytest.mark.asyncio
async def test_get_single_device_status_generic_exception():
    """
    get_single_device_status should return NOT_CONNECTED when any other exception occurs.
    """
    test_error = ValueError("Unexpected error")

    with patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error):
        result = await get_single_device_status(
            participant_id="test_participant_generic_error",
            device_type=DeviceTypeEnum.OURARING,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.healthy is False
        assert result.device == DeviceTypeEnum.OURARING
        assert result.status == DeviceStatusEnum.NOT_CONNECTED


@pytest.mark.asyncio
async def test_get_single_device_status_json_decode_error():
    """
    get_single_device_status should return NOT_CONNECTED when JSON decode fails.
    """
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(side_effect=ValueError("Invalid JSON"))

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_json_error",
            device_type=DeviceTypeEnum.WITHINGS,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.healthy is False
        assert result.device == DeviceTypeEnum.WITHINGS
        assert result.status == DeviceStatusEnum.NOT_CONNECTED


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "device_type, device_model",
    [
        (DeviceTypeEnum.WITHINGS, DeviceModelEnum.SCALE),
        (DeviceTypeEnum.WITHINGS, DeviceModelEnum.BPM),
        (DeviceTypeEnum.TRANSTEK, DeviceModelEnum.SCALE),
        (DeviceTypeEnum.TRANSTEK, DeviceModelEnum.BPM),
        (DeviceTypeEnum.FITBIT, DeviceModelEnum.SCALE),
        (DeviceTypeEnum.DEXCOM, None),
        (DeviceTypeEnum.OURARING, None),
    ],
)
async def test_get_single_device_status_different_device_types_and_models(
    device_type, device_model
):
    """
    get_single_device_status should work correctly with different device types and models.
    """
    test_response_data = {
        "healthy": True,
        "token": f"token_for_{device_type.value}",
        "model": device_model.value if device_model else None,
    }
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_single_device_status(
            participant_id="test_participant_parametrized",
            device_type=device_type,
            device_model=device_model,
        )

        assert isinstance(result, DetailedConnectionStatus)
        assert result.healthy is True
        assert result.device == device_type
        assert result.status == DeviceStatusEnum.CONNECTED
        assert result.token == f"token_for_{device_type.value}"
        assert result.model == (device_model.value if device_model else None)


@pytest.mark.asyncio
async def test_get_single_device_status_request_parameters():
    """
    get_single_device_status should make request with correct parameters.
    """
    test_response_data = {"healthy": True}
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch("ciba_participant.rpm_api.api.AsyncClient") as mock_client_class:
        mock_client = AsyncMock()
        mock_client.__aenter__ = AsyncMock(return_value=mock_client)
        mock_client.__aexit__ = AsyncMock(return_value=None)
        mock_client.get = AsyncMock(return_value=test_response)
        mock_client_class.return_value = mock_client

        await get_single_device_status(
            participant_id="test_participant_params",
            device_type=DeviceTypeEnum.WITHINGS,
            device_model=DeviceModelEnum.SCALE,
        )

        # Verify AsyncClient was initialized with correct parameters
        expected_params = {
            "type_device": "withings",
            "member_type": "participant",
            "member_id": "test_participant_params",
            "device_model": "scale",
        }

        mock_client_class.assert_called_once()
        call_kwargs = mock_client_class.call_args[1]
        assert call_kwargs["params"] == expected_params
        assert (
            "RPM_API_URL" in str(call_kwargs["base_url"])
            or call_kwargs["base_url"] is not None
        )
        assert "X-Auth-Key" in call_kwargs["headers"]
        assert call_kwargs["timeout"] == 60

        # Verify the GET request was made to correct endpoint
        mock_client.get.assert_called_once_with("/devices/get_status")


@pytest.mark.asyncio
async def test_get_single_device_status_request_parameters_without_device_model():
    """
    get_single_device_status should make request without device_model when it's None.
    """
    test_response_data = {"healthy": True}
    test_response = MagicMock(spec=Response)
    test_response.status_code = codes.OK
    test_response.json = Mock(return_value=test_response_data)

    with patch("ciba_participant.rpm_api.api.AsyncClient") as mock_client_class:
        mock_client = AsyncMock()
        mock_client.__aenter__ = AsyncMock(return_value=mock_client)
        mock_client.__aexit__ = AsyncMock(return_value=None)
        mock_client.get = AsyncMock(return_value=test_response)
        mock_client_class.return_value = mock_client

        await get_single_device_status(
            participant_id="test_participant_no_model",
            device_type=DeviceTypeEnum.DEXCOM,
            device_model=None,
        )

        # Verify AsyncClient was initialized with correct parameters (no device_model)
        expected_params = {
            "type_device": "dexcom",
            "member_type": "participant",
            "member_id": "test_participant_no_model",
        }

        call_kwargs = mock_client_class.call_args[1]
        assert call_kwargs["params"] == expected_params
        assert "device_model" not in call_kwargs["params"]
