from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID

import pendulum
import pytest

from ciba_participant.rpm_api.helpers import get_sync_start_date

test_member_id = UUID("35783c61-0c2a-46df-bb92-993a3dbb032c")
COHORT_MEMBERS_MODEL = "ciba_participant.rpm_api.helpers.CohortMembers"
ACTIVITIES_MODEL = "ciba_participant.rpm_api.helpers.ParticipantActivity"


def mock_model():
    test_model = MagicMock()
    filter_mock = MagicMock()
    order_mock = MagicMock
    test_model.filter.return_value = filter_mock
    filter_mock.order_by.return_value = order_mock

    actions = {
        "filter": filter_mock,
        "order": order_mock,
    }

    return test_model, actions


async def mock_async_property(test_response):
    """Function to mock an async property for testing purposes"""
    return test_response


@pytest.mark.asyncio
async def test_get_sync_start_date_with_no_dates():
    """
    get_sync_start_date should return None
    when no activity and cohort was found.
    """
    activity_mock, activity_actions = mock_model()
    activity_actions["order"].first = AsyncMock(return_value=None)
    cohort_membership_mock, membership_actions = mock_model()
    membership_actions["filter"].first = AsyncMock(return_value=None)

    with (
        patch(COHORT_MEMBERS_MODEL, cohort_membership_mock),
        patch(ACTIVITIES_MODEL, activity_mock),
    ):
        actual_value = await get_sync_start_date(test_member_id)

        assert actual_value is None


@pytest.mark.asyncio
async def test_get_sync_start_date_with_no_activities():
    """
    get_sync_start_date should return cohort start date
    when no activity found.
    """
    test_cohort = MagicMock()
    test_cohort.started_at = pendulum.parse("2025-03-01")
    test_cohort_membership = MagicMock()
    test_cohort_membership.cohort = mock_async_property(test_cohort)
    activity_mock, activity_actions = mock_model()
    activity_actions["order"].first = AsyncMock(return_value=None)
    cohort_membership_mock, membership_actions = mock_model()
    membership_actions["filter"].first = AsyncMock(return_value=test_cohort_membership)

    with (
        patch(COHORT_MEMBERS_MODEL, cohort_membership_mock),
        patch(ACTIVITIES_MODEL, activity_mock),
    ):
        actual_value = await get_sync_start_date(test_member_id)

        assert actual_value == test_cohort.started_at.int_timestamp


@pytest.mark.asyncio
async def test_get_sync_start_date_with_activities():
    """
    get_sync_start_date should return the latest activity timestamp.
    """
    test_activity = MagicMock()
    test_activity.created_at = pendulum.parse("2025-03-30")
    activity_mock, activity_actions = mock_model()
    activity_actions["order"].first = AsyncMock(return_value=test_activity)

    with patch(ACTIVITIES_MODEL, activity_mock):
        actual_value = await get_sync_start_date(test_member_id)

        assert actual_value == test_activity.created_at.int_timestamp
