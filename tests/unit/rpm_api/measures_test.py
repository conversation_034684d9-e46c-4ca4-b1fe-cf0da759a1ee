from unittest.mock import MagicMock, AsyncMock, patch, Mock
from uuid import <PERSON><PERSON><PERSON>

import pytest
from httpx import <PERSON>TTP<PERSON>rror, HTTPStatusError, Response

from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.measures import get_latest_data

test_participant_id = UUID("1a83867f-80e8-4159-9862-8b9245f62a52")
GET_METHOD = "ciba_participant.rpm_api.measures.AsyncClient.get"
GET_SYNC_DATE = "ciba_participant.rpm_api.measures.get_sync_start_date"


@pytest.fixture
def mock_activity_model():
    test_model = MagicMock()
    filter_mock = MagicMock()
    test_model.filter.return_value = filter_mock

    return test_model, filter_mock


@pytest.mark.asyncio
async def test_get_latest_data_with_call_error(mock_activity_model):
    """
    get_latest_data should raise an Exception
    when the api call fails.
    """
    test_error = HTTPError("Test call error.")

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error),
    ):
        with pytest.raises(RPMCallError):
            await get_latest_data(test_participant_id)


@pytest.mark.asyncio
async def test_get_latest_data_with_error_in_response(mock_activity_model):
    """
    get_latest_data should raise an Exception
    when the api call return an unsuccessful response.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "test"})
    test_response.status_code = 400
    test_error = HTTPStatusError("test", request=MagicMock(), response=test_response)
    test_response.raise_for_status = Mock(side_effect=test_error)

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        with pytest.raises(RPMCallError) as expected_error:
            await get_latest_data(test_participant_id)

        assert expected_error.value.args[0] == "test"


@pytest.mark.asyncio
async def test_get_latest_data_success(mock_activity_model):
    """
    get_latest_data should return the latest data from RPM.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "last_ciba_sync": None,
            "last_device_sync": None,
            "measures": [],
            "devices": [],
        }
    )

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=1749168000),
        patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        actual_value = await get_latest_data(test_participant_id)

        assert actual_value.last_ciba_sync is None
        assert actual_value.last_device_sync is None
        assert actual_value.measures == []
        assert actual_value.devices == []
