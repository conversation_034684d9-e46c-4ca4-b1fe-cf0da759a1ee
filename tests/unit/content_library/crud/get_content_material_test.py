from uuid import uuid4
from unittest.mock import Magic<PERSON>ock

import pendulum
import pytest
from tortoise.queryset import Q

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.content_library.crud.get_content_material import (
    MaterialFilters,
    get_filters,
    get_prefetch_related_data,
)
from ciba_participant.content_library.enums import ContentMaterialStatus, MaterialTag

test_id = uuid4()
test_date = pendulum.parse("2025-01-01")


@pytest.fixture
def mock_filters(request):
    if request.param == "mocked":
        filters = MagicMock(spec=MaterialFilters)
        filters.favorites_for = None
        return filters

    return request.param


def test_get_prefetch_related_data_including_interactions():
    """
    get_prefetch_related_data should include interactions on the prefetch data
    when the filters include the favorites_for value
    """
    filters = MagicMock(spec=MaterialFilters)
    filters.favorites_for = uuid4()

    actual_value = get_prefetch_related_data(filters)

    assert "interactions" in actual_value


@pytest.mark.parametrize("mock_filters", [None, "mocked"], indirect=["mock_filters"])
def test_get_prefetch_related_data_with_default_response(mock_filters):
    """
    get_prefetch_related_data should return the default prefetch data
    when the filters are None or does not include the favorites_for value
    """
    actual_value = get_prefetch_related_data(mock_filters)

    assert actual_value == ["programs", "activity_types", "tags"]


@pytest.mark.parametrize(
    "test_filters, expected_value",
    [
        (None, []),
        (MaterialFilters(start_date=test_date), [Q(created_at__gte=test_date)]),
        (MaterialFilters(end_date=test_date), [Q(created_at__lte=test_date)]),
        (MaterialFilters(program_id=test_id), [Q(programs__program_id=test_id)]),
        (
            MaterialFilters(search="test"),
            [Q(Q(title__icontains="test") | Q(description__icontains="test"))],
        ),
        (
            MaterialFilters(favorites_for=test_id),
            [
                Q(
                    Q(interactions__is_favorite=True)
                    & Q(interactions__participant_id=test_id)
                )
            ],
        ),
        (
            MaterialFilters(status=ContentMaterialStatus.ACTIVE),
            [Q(status=ContentMaterialStatus.ACTIVE)],
        ),
        (MaterialFilters(programs=[test_id]), [Q(programs__program_id__in=[test_id])]),
        (
            MaterialFilters(tags=[MaterialTag.ACTIVITY]),
            [Q(tags__tag__in=[MaterialTag.ACTIVITY])],
        ),
        (
            MaterialFilters(activity_types=[ParticipantActivityEnum.RECIPES]),
            [Q(activity_types__activity_type__in=[ParticipantActivityEnum.RECIPES])],
        ),
    ],
)
def test_get_filters_return_right_filters_list(test_filters, expected_value):
    """
    get_filters should return the applicable query filters
    based on the provided filter values
    """
    actual_value = get_filters(test_filters)

    assert actual_value == expected_value
