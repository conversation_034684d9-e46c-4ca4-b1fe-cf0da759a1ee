from unittest.mock import MagicMock, AsyncMock, patch

import pytest

from ciba_participant.content_library.crud.update_archive_status import process
from ciba_participant.content_library.enums import ContentMaterialStatus
from ciba_participant.content_library.exceptions import (
    ContentMaterialNotFoundError,
    ContentMaterialForbiddenError,
)
from ciba_participant.content_library.models import ContentMaterial
from ciba_participant.participant.crud import AuthorizedRepository
from tests.unit.content_library.crud.common import test_material_id, test_author_id

mocked_material = MagicMock(spec=ContentMaterial)
mocked_material.save = AsyncMock()
test_authorized = MagicMock()
test_authorized.id = test_author_id


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "current_status, expected_status",
    [
        (ContentMaterialStatus.ACTIVE, ContentMaterialStatus.ARCHIVED),
        (ContentMaterialStatus.ARCHIVED, ContentMaterialStatus.ACTIVE),
    ],
)
async def test_process_with_correct_status_change(current_status, expected_status):
    """
    process should change the status between active and archived
    """
    mocked_material.status = current_status
    mock_filter = MagicMock()
    mock_filter.first = AsyncMock(return_value=mocked_material)

    with (
        patch.object(ContentMaterial, "filter", return_value=mock_filter),
        patch.object(
            AuthorizedRepository,
            "get_content_library_admins",
            new_callable=AsyncMock,
            return_value=[test_authorized],
        ),
    ):
        await process(test_material_id, test_author_id)

        assert mocked_material.status == expected_status
        assert mocked_material.updated_by == test_author_id


@pytest.mark.asyncio
async def test_process_with_not_found_error():
    """
    process should raise an exception when the requested material does not exist.
    """
    mock_filter = MagicMock()
    mock_filter.first = AsyncMock(return_value=None)

    with (
        patch.object(ContentMaterial, "filter", return_value=mock_filter),
        patch.object(
            AuthorizedRepository,
            "get_content_library_admins",
            new_callable=AsyncMock,
            return_value=[test_authorized],
        ),
    ):
        with pytest.raises(ContentMaterialNotFoundError) as error_info:
            await process(test_material_id, test_author_id)

        assert error_info.type is ContentMaterialNotFoundError


@pytest.mark.asyncio
async def test_process_with_unauthorized_error():
    """
    process should raise a permissions error
    when the user is not in the authorized list.
    """
    with patch.object(
        AuthorizedRepository,
        "get_content_library_admins",
        new_callable=AsyncMock,
        return_value=[],
    ):
        with pytest.raises(ContentMaterialForbiddenError) as error_info:
            await process(test_material_id, test_author_id)

        assert error_info.type is ContentMaterialForbiddenError
