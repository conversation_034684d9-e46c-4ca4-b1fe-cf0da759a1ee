from unittest.mock import MagicMock, patch, AsyncMock

import pytest

from ciba_participant.content_library.crud.create_content_material import (
    NewMaterialData,
)
from ciba_participant.content_library.crud.edit_content_material import (
    get_file_upload_flag,
    get_update_difference,
    process,
)
from ciba_participant.content_library.exceptions import ContentMaterialForbiddenError
from ciba_participant.content_library.models import ContentMaterial
from ciba_participant.participant.crud import AuthorizedRepository
from tests.unit.content_library.crud.common import test_material_id, test_author_id

mock_content = MagicMock(spec=ContentMaterial)
mock_content.file_name = "test.pdf"
mock_content.file_location = "pdf"
mock_content.file_size = 256


def test_get_file_upload_flag_1():
    """
    get_file_upload_flag should return False when the material updated is a form.
    """
    test_data = MagicMock(spec=NewMaterialData)
    test_data.content_url = None
    test_data.form_id = "X7Y7E8"

    actual_value = get_file_upload_flag(mock_content, test_data)

    assert actual_value is False


def test_get_file_upload_flag_2():
    """
    get_file_upload_flag should return False when the material updated is an url.
    """
    test_data = MagicMock(spec=NewMaterialData)
    test_data.form_id = None
    test_data.content_url = "https://video.com"

    actual_value = get_file_upload_flag(mock_content, test_data)

    assert actual_value is False


@pytest.mark.parametrize(
    "test_size, test_name, test_location",
    [
        (1024, mock_content.file_name, mock_content.file_location),
        (mock_content.file_size, "mock.pdf", mock_content.file_location),
        (mock_content.file_size, mock_content.file_name, "docs"),
    ],
)
def test_get_file_upload_flag_3(test_size, test_name, test_location):
    """
    get_file_upload_flag should return True when the material updated is an url
    and the file related values changed.
    """
    test_data = MagicMock(spec=NewMaterialData)
    test_data.content_url = None
    test_data.form_id = None
    test_data.file_location = test_location
    test_data.file_size = test_size
    test_data.file_name = test_name

    actual_value = get_file_upload_flag(mock_content, test_data)

    assert actual_value is True


def test_get_file_upload_flag_4():
    """
    get_file_upload_flag should return False when the material is a file
    and the related values did not change.
    """
    test_data = MagicMock(spec=NewMaterialData)
    test_data.content_url = None
    test_data.form_id = None
    test_data.file_location = mock_content.file_location
    test_data.file_size = mock_content.file_size
    test_data.file_name = mock_content.file_name

    actual_value = get_file_upload_flag(mock_content, test_data)

    assert actual_value is False


@pytest.mark.parametrize(
    "test_current, test_new, expected_value",
    [
        ({1, 2, 3}, {3, 4, 5}, ({4, 5}, {1, 2})),
        ({"a", "b", "c"}, {"b", "c"}, (set(), {"a"})),
        ({9, 8, 7}, {9, 8, 7, 6}, ({6}, set())),
    ],
)
def test_get_update_difference(test_current, test_new, expected_value):
    """
    get_update_difference should return the values to_delete and to_insert
    based on the provided sets.
    """
    actual_value = get_update_difference(test_current, test_new)

    assert actual_value == expected_value


@pytest.mark.asyncio
async def test_process_edition_with_unauthorized_error():
    """
    process should raise a permissions error
    when the user is not in the authorized list
    """
    with patch.object(
        AuthorizedRepository,
        "get_content_library_admins",
        new_callable=AsyncMock,
    ):
        with pytest.raises(ContentMaterialForbiddenError) as error_info:
            await process(
                test_material_id, MagicMock(spec=NewMaterialData), test_author_id
            )

        assert error_info.type is ContentMaterialForbiddenError
