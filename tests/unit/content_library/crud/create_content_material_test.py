from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from ciba_participant.content_library.crud.create_content_material import (
    process,
    NewMaterialData,
)
from ciba_participant.content_library.exceptions import ContentMaterialForbiddenError
from ciba_participant.participant.crud import AuthorizedRepository
from tests.unit.content_library.crud.common import test_author_id


@pytest.mark.asyncio
async def test_process_with_unauthorized_error():
    """
    process should raise a permissions error
    when the user is not in the authorized list.
    """
    with patch.object(
        AuthorizedRepository,
        "get_content_library_admins",
        new_callable=AsyncMock,
    ) as mock_get_admins:
        mock_get_admins.return_value = []

        with pytest.raises(ContentMaterialForbiddenError) as error_info:
            await process(MagicMock(spec=NewMaterialData), test_author_id)

        assert error_info.type is ContentMaterialForbiddenError
