from unittest.mock import patch, AsyncMock

import pytest

from ciba_participant.content_library.crud.delete_content_material import process
from ciba_participant.content_library.exceptions import ContentMaterialForbiddenError
from ciba_participant.participant.crud import AuthorizedRepository
from tests.unit.content_library.crud.common import test_material_id, test_author_id


@pytest.mark.asyncio
async def test_process():
    """
    process should raise a permissions error
    when the author is not in the authorized list.
    """
    with patch.object(
        AuthorizedRepository,
        "get_content_library_admins",
        new_callable=AsyncMock,
    ):
        with pytest.raises(ContentMaterialForbiddenError) as error_info:
            await process(test_material_id, test_author_id)

        assert error_info.type is ContentMaterialForbiddenError
