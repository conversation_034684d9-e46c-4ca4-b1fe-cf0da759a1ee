from datetime import timed<PERSON><PERSON>
from unittest.mock import Mock, patch, MagicMock
from uuid import uuid4

import pendulum
import pytest

from ciba_participant.content_library.helpers import (
    adjust_mime_type,
    get_file_url,
    EXPIRATION,
    sanitize_file_name,
)

url_mocked_module = "ciba_participant.content_library.helpers.sign_cloudfront_url"


@pytest.fixture(scope="function")
def prepare_url_data():
    test_id = uuid4()
    test_location = "/test"
    test_file_name = "file.test"
    test_now = pendulum.parse("1990-06-15T00:00:00")
    mock_url_generator = Mock()

    return test_id, test_location, test_file_name, test_now, mock_url_generator


@pytest.fixture
def mock_settings():
    fake_settings = MagicMock()
    fake_settings.ENV = "production"

    with patch("ciba_participant.content_library.helpers.settings", fake_settings):
        yield fake_settings


@pytest.mark.parametrize(
    "test_url, test_form_id, expected_value",
    [
        (None, "ABC123", "form/type_form"),
        ("https://youtube.com/v/123", None, "url/video"),
        ("https://test.com/file.pdf", None, "application/pdf"),
        ("https://test.com/a1b2c3", None, "url/article"),
    ],
)
def test_adjust_mime_type(test_url, test_form_id, expected_value):
    """
    adjust_mime_type should return the proper value
    depending on the input values.
    """
    actual_value = adjust_mime_type(test_url, test_form_id)

    assert expected_value == actual_value


def test_get_file_url_on_local_environments(prepare_url_data):
    """
    get_file_url should return a fake url when the execution is on a local environment.
    """
    test_id, test_location, test_file_name, test_now, mock_url_generator = (
        prepare_url_data
    )

    with (
        patch("pendulum.now", return_value=test_now),
        patch(url_mocked_module, mock_url_generator),
    ):
        actual_url, actual_expiration = get_file_url(
            test_id, test_location, test_file_name
        )

        assert actual_url == f"{test_location}/{test_id}-{test_file_name}"
        assert actual_expiration == test_now + timedelta(days=EXPIRATION)
        mock_url_generator.assert_not_called()


def test_get_file_url_on_cloud_environments(prepare_url_data, mock_settings):
    """
    get_file_url should return an aws url when the execution is on a cloud environment.
    """
    from ciba_participant.content_library.helpers import get_file_url, EXPIRATION

    test_id, test_location, test_file_name, test_now, mock_url_generator = (
        prepare_url_data
    )
    mock_url_generator.return_value = "https://mock.storage"

    with (
        patch("pendulum.now", return_value=test_now),
        patch(url_mocked_module, mock_url_generator),
    ):
        actual_url, actual_expiration = get_file_url(
            test_id, test_location, test_file_name
        )

        assert actual_url == mock_url_generator.return_value
        assert actual_expiration == test_now + timedelta(days=EXPIRATION)
        mock_url_generator.assert_called_once()


@pytest.mark.parametrize(
    "test_error",
    [
        ValueError("Invalid signature padding"),
        IndexError(),
    ],
)
def test_get_file_url_with_handled_exception(
    test_error, prepare_url_data, mock_settings
):
    """
    get_file_url should return None values when an aws client exception raises.
    """
    from ciba_participant.content_library.helpers import get_file_url

    test_id, test_location, test_file_name, _, mock_url_generator = prepare_url_data
    mock_url_generator.side_effect = test_error

    with patch(url_mocked_module, mock_url_generator):
        actual_url, actual_expiration = get_file_url(
            test_id, test_location, test_file_name
        )

        assert actual_url is None
        assert actual_expiration is None


@pytest.mark.parametrize(
    "test_value, expected_value",
    [
        ("", None),
        (None, None),
        ("random string", "random-string"),
        ("Mr Doe push ups routine.mov", "Mr-Doe-push-ups-routine.mov"),
        ("gluten free.pdf", "gluten-free.pdf"),
        ("meal/vegan + vegetarian=.pdf", "meal~vegan---vegetarian_.pdf"),
    ],
)
def test_sanitize_file_name(test_value, expected_value):
    """
    sanitize_file_name should return a filename without unsafe characters
    or None for falsy values.
    """
    actual_value = sanitize_file_name(test_value)

    assert actual_value == expected_value
