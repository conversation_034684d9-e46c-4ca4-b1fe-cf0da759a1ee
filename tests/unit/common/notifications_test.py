from unittest.mock import patch
import pendulum
import pytest
from ciba_participant.notifications.email.send_grid_email import EmailHandler


@pytest.mark.asyncio
async def test_send_cancellation_email():
    with patch(
        "ciba_participant.notifications.email.send_grid_email.EmailHandler.send_email"
    ) as mock_send:
        handler = EmailHandler()

        email = "<EMAIL>"
        first_name = "Tester"
        class_name = "Test Class"
        class_date = pendulum.now().to_day_datetime_string()

        handler.send_cancelled_session_email(email, first_name, class_name, class_date)
        mock_send.assert_called_once()
