# from unittest.mock import patch, Mock
#
# import pytest
#
# from ciba_participant.common.signatures import make_signature_safe_for_cloudfront, sign_cloudfront_url
#
#
# @pytest.mark.parametrize("test_value, expected_value", [
#     (b"hello world", "aGVsbG8gd29ybGQ_"),
#     (b"a\xfbb\xffc\xfe", "Yfti~2P-"),
# ])
# def test_make_signature_safe_for_cloudfront_success(test_value, expected_value):
#     """
#     make_signature_safe_for_cloudfront should return a string with valid characters
#     that's safe for cloudfront usage
#     """
#     actual_value = make_signature_safe_for_cloudfront(test_value)
#
#     assert actual_value == expected_value
#
#
# def test_sign_cloudfront_url_success():
#     """
#     sign_cloudfront_url should return a cloudfront URL with signature
#     """
#     mocked_signer = Mock()
#     mocked_signer.return_value = b"test"
#     test_key = "KEY1234"
#     test_key_id = "ID1234"
#     test_url = "https://test.cloudfront.net/pdf/a.pdf"
#     test_expiration = 1767225599
#
#     with patch("ciba_participant.common.signatures.get_rsa_signature", mocked_signer):
#         actual_value = sign_cloudfront_url(
#             url=test_url,
#             key_id=test_key_id,
#             sign_key=test_key,
#             expires_in=test_expiration,
#         )
#
#         assert actual_value == (
#             f"{test_url}?Expires={test_expiration}&Signature=dGVzdA__&Key-Pair-Id={test_key_id}"
#         )
#         mocked_signer.assert_called_once_with(
#             f'{{"Statement":[{{"Resource":"{test_url}",'
#             f'"Condition":{{"DateLessThan":{{"AWS:EpochTime":{test_expiration}}}}}}}]}}',
#             test_key,
#         )
