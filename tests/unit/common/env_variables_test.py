from ciba_participant.common.env_variables import parse_multiline_env_variable

# placed here for readability purposes
expected_Value = """---BEGIN MULTILINE---
value 1
value 2
---END MULTILINE---"""


def test_parse_multiline_env_variable():
    """
    parse_multiline_env_variable should return a multi-line string
    from an "adapted" muli-line env variable.
    :return:
    """
    test_value = "---BEGIN MULTILINE---\\nvalue 1\\nvalue 2\\n---END MULTILINE---"
    actual_value = parse_multiline_env_variable(test_value)

    assert actual_value == expected_Value
