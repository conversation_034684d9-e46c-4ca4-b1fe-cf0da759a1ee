import pytest
from unittest.mock import patch, MagicMock
from uuid import uuid4
from datetime import datetime

from ciba_participant.cohort.crud.get_paginated_cohorts import (
    FullCohort,
    RawParticipant,
    RawProgram,
    RawAuthorized,
    RawCohortProgramModules,
)


class TestFullCohortCreation:
    """Test the creation of FullCohort objects with various edge cases."""

    @pytest.mark.asyncio
    async def test_fullcohort_with_all_fields(self):
        """Test that FullCohort can be created with all fields."""
        # Create mock objects for relations
        program = RawProgram(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            title="Test Program",
            description="Test Program Description",
        )

        created_by = RawAuthorized(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            email="<EMAIL>",
            first_name="Admin",
            last_name="User",
            status="active",
            cognito_sub=None,
            is_test=False,
            role="health_coach",
            chat_identity="admin_chat_identity",
            api_id=None,
            support_in_chat=False,
            classes_admin=False,
        )

        participant = RawParticipant(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            group_id=uuid4(),
            member_id=uuid4(),
            status="active",
            cognito_sub=None,
            medical_record=None,
            is_test=False,
            last_reset=None,
            chat_identity="test_chat_identity",
        )

        # Create a FullCohort with all fields
        cohort = FullCohort(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            name="Test Cohort",
            started_at=datetime.now(),
            limit=10,
            program_id=program.id,
            created_by_id=created_by.id,
            program=program,
            participants=[participant],
            created_by=created_by,
            program_modules=[],
        )

        # Check that the cohort was created correctly
        assert cohort.program == program
        assert cohort.created_by == created_by
        assert len(cohort.participants) == 1
        assert cohort.participants[0] == participant
        assert cohort.program_modules == []

    @pytest.mark.asyncio
    async def test_fullcohort_with_none_program(self):
        """Test that FullCohort handles None program correctly."""
        # Create a FullCohort with None program
        cohort = FullCohort(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            name="Test Cohort",
            started_at=datetime.now(),
            limit=10,
            program_id=uuid4(),
            created_by_id=uuid4(),
            program=None,
            participants=[],
            created_by=None,
            program_modules=[],
        )

        # Check that the cohort was created correctly
        assert cohort.program is None

    @pytest.mark.asyncio
    async def test_fullcohort_with_none_created_by(self):
        """Test that FullCohort handles None created_by correctly."""
        # Create a FullCohort with None created_by
        cohort = FullCohort(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            name="Test Cohort",
            started_at=datetime.now(),
            limit=10,
            program_id=uuid4(),
            created_by_id=uuid4(),
            program=None,
            participants=[],
            created_by=None,
            program_modules=[],
        )

        # Check that the cohort was created correctly
        assert cohort.created_by is None

    @pytest.mark.asyncio
    async def test_fullcohort_with_none_participants(self):
        """Test that FullCohort handles None participants correctly."""
        # Create a FullCohort with None participants
        cohort = FullCohort(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            name="Test Cohort",
            started_at=datetime.now(),
            limit=10,
            program_id=uuid4(),
            created_by_id=uuid4(),
            program=None,
            participants=None,
            created_by=None,
            program_modules=[],
        )

        # Check that the cohort was created correctly
        assert cohort.participants is None

    @pytest.mark.asyncio
    async def test_fullcohort_with_empty_participants(self):
        """Test that FullCohort handles empty participants list correctly."""
        # Create a FullCohort with empty participants list
        cohort = FullCohort(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            name="Test Cohort",
            started_at=datetime.now(),
            limit=10,
            program_id=uuid4(),
            created_by_id=uuid4(),
            program=None,
            participants=[],
            created_by=None,
            program_modules=[],
        )

        # Check that the cohort was created correctly
        assert cohort.participants == []

    @pytest.mark.asyncio
    async def test_fullcohort_with_none_program_modules(self):
        """Test that FullCohort handles None program_modules correctly."""
        # Create a FullCohort with None program_modules
        cohort = FullCohort(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            name="Test Cohort",
            started_at=datetime.now(),
            limit=10,
            program_id=uuid4(),
            created_by_id=uuid4(),
            program=None,
            participants=[],
            created_by=None,
            program_modules=None,
        )

        # Check that the cohort was created correctly
        assert cohort.program_modules is None

    @pytest.mark.asyncio
    async def test_fullcohort_with_empty_program_modules(self):
        """Test that FullCohort handles empty program_modules list correctly."""
        # Create a FullCohort with empty program_modules list
        cohort = FullCohort(
            id=uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            name="Test Cohort",
            started_at=datetime.now(),
            limit=10,
            program_id=uuid4(),
            created_by_id=uuid4(),
            program=None,
            participants=[],
            created_by=None,
            program_modules=[],
        )

        # Check that the cohort was created correctly
        assert cohort.program_modules == []


class TestModelValidation:
    """Test the model_validate methods of the various models."""

    @pytest.mark.asyncio
    async def test_rawparticipant_model_validate_success(self):
        """Test that RawParticipant.model_validate works correctly with valid data."""
        # Create a mock participant with valid data
        mock_participant = MagicMock()
        mock_participant.id = uuid4()
        mock_participant.created_at = datetime.now()
        mock_participant.updated_at = datetime.now()
        mock_participant.email = "<EMAIL>"
        mock_participant.first_name = "Test"
        mock_participant.last_name = "User"
        mock_participant.group_id = uuid4()
        mock_participant.member_id = uuid4()
        mock_participant.status = "active"
        mock_participant.cognito_sub = None
        mock_participant.medical_record = None
        mock_participant.is_test = False
        mock_participant.last_reset = None
        mock_participant.chat_identity = "test_chat_identity"

        # Call the model_validate method
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawParticipant.model_validate",
            return_value=mock_participant,
        ):
            result = RawParticipant.model_validate(mock_participant)

            try:
                status = result.status.value
            except AttributeError:
                status = result.status

            # Check that the result is correct
            assert result.email == mock_participant.email
            assert result.first_name == mock_participant.first_name
            assert result.last_name == mock_participant.last_name
            assert result.group_id == mock_participant.group_id
            assert result.member_id == mock_participant.member_id
            assert status == mock_participant.status
            assert result.cognito_sub == mock_participant.cognito_sub
            assert result.medical_record == mock_participant.medical_record
            assert result.is_test == mock_participant.is_test
            assert result.last_reset == mock_participant.last_reset
            assert result.chat_identity == mock_participant.chat_identity

    @pytest.mark.asyncio
    async def test_rawparticipant_model_validate_error(self):
        """Test that RawParticipant.model_validate handles validation errors correctly."""
        # Create a mock participant that will cause a validation error
        mock_participant = MagicMock()

        # Mock the model_validate method to raise an exception
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawParticipant.model_validate"
        ) as mock_validate:
            mock_validate.side_effect = Exception("Validation error")

            # Call the model_validate method
            with pytest.raises(Exception):
                RawParticipant.model_validate(mock_participant)

    @pytest.mark.asyncio
    async def test_rawprogram_model_validate_success(self):
        """Test that RawProgram.model_validate works correctly with valid data."""
        # Create a mock program with valid data
        mock_program = MagicMock()
        mock_program.id = uuid4()
        mock_program.created_at = datetime.now()
        mock_program.updated_at = datetime.now()
        mock_program.title = "Test Program"
        mock_program.description = "Test Program Description"

        # Call the model_validate method
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawProgram.model_validate",
            return_value=mock_program,
        ):
            result = RawProgram.model_validate(mock_program)

            # Check that the result is correct
            assert result.id == mock_program.id
            assert result.created_at == mock_program.created_at
            assert result.updated_at == mock_program.updated_at
            assert result.title == mock_program.title
            assert result.description == mock_program.description

    @pytest.mark.asyncio
    async def test_rawprogram_model_validate_error(self):
        """Test that RawProgram.model_validate handles validation errors correctly."""
        # Create a mock program that will cause a validation error
        mock_program = MagicMock()

        # Mock the model_validate method to raise an exception
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawProgram.model_validate"
        ) as mock_validate:
            mock_validate.side_effect = Exception("Validation error")

            # Call the model_validate method
            with pytest.raises(Exception):
                RawProgram.model_validate(mock_program)

    @pytest.mark.asyncio
    async def test_rawauthorized_model_validate_success(self):
        """Test that RawAuthorized.model_validate works correctly with valid data."""
        # Create a mock authorized with valid data
        mock_authorized = MagicMock()
        mock_authorized.id = uuid4()
        mock_authorized.created_at = datetime.now()
        mock_authorized.updated_at = datetime.now()
        mock_authorized.email = "<EMAIL>"
        mock_authorized.first_name = "Admin"
        mock_authorized.last_name = "User"
        mock_authorized.status = "active"
        mock_authorized.cognito_sub = None
        mock_authorized.is_test = False
        mock_authorized.role = "health_coach"
        mock_authorized.chat_identity = "admin_chat_identity"
        mock_authorized.api_id = None
        mock_authorized.support_in_chat = False
        mock_authorized.classes_admin = False

        # Call the model_validate method
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawAuthorized.model_validate",
            return_value=mock_authorized,
        ):
            result = RawAuthorized.model_validate(mock_authorized)

            try:
                status = result.status.value
                role = result.role.value
            except AttributeError:
                status = result.status
                role = result.role

            # Check that the result is correct
            assert result.id == mock_authorized.id
            assert result.created_at == mock_authorized.created_at
            assert result.updated_at == mock_authorized.updated_at
            assert result.email == mock_authorized.email
            assert result.first_name == mock_authorized.first_name
            assert result.last_name == mock_authorized.last_name
            assert status == mock_authorized.status
            assert result.cognito_sub == mock_authorized.cognito_sub
            assert result.is_test == mock_authorized.is_test
            assert role == mock_authorized.role
            assert result.chat_identity == mock_authorized.chat_identity
            assert result.api_id == mock_authorized.api_id
            assert result.support_in_chat == mock_authorized.support_in_chat
            assert result.classes_admin == mock_authorized.classes_admin

    @pytest.mark.asyncio
    async def test_rawauthorized_model_validate_error(self):
        """Test that RawAuthorized.model_validate handles validation errors correctly."""
        # Create a mock authorized that will cause a validation error
        mock_authorized = MagicMock()

        # Mock the model_validate method to raise an exception
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawAuthorized.model_validate"
        ) as mock_validate:
            mock_validate.side_effect = Exception("Validation error")

            # Call the model_validate method
            with pytest.raises(Exception):
                RawAuthorized.model_validate(mock_authorized)

    @pytest.mark.asyncio
    async def test_rawcohortprogrammodules_model_validate_success(self):
        """Test that RawCohortProgramModules.model_validate works correctly with valid data."""
        # Create a mock program module with valid data
        mock_program_module = MagicMock()
        mock_program_module.id = uuid4()
        mock_program_module.started_at = datetime.now()
        mock_program_module.ended_at = datetime.now()
        mock_program_module.metadata = {}
        mock_program_module.cohort_id = uuid4()
        mock_program_module.program_module_id = uuid4()

        # Call the model_validate method
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawCohortProgramModules.model_validate",
            return_value=mock_program_module,
        ):
            result = RawCohortProgramModules.model_validate(mock_program_module)

            # Check that the result is correct
            assert result.id == mock_program_module.id
            assert result.started_at == mock_program_module.started_at
            assert result.ended_at == mock_program_module.ended_at
            assert result.metadata == mock_program_module.metadata
            assert result.cohort_id == mock_program_module.cohort_id
            assert result.program_module_id == mock_program_module.program_module_id

    @pytest.mark.asyncio
    async def test_rawcohortprogrammodules_model_validate_error(self):
        """Test that RawCohortProgramModules.model_validate handles validation errors correctly."""
        # Create a mock program module that will cause a validation error
        mock_program_module = MagicMock()

        # Mock the model_validate method to raise an exception
        with patch(
            "ciba_participant.cohort.crud.get_paginated_cohorts.RawCohortProgramModules.model_validate"
        ) as mock_validate:
            mock_validate.side_effect = Exception("Validation error")

            # Call the model_validate method
            with pytest.raises(Exception):
                RawCohortProgramModules.model_validate(mock_program_module)
