import pytest
from unittest.mock import patch, MagicMock
from uuid import uuid4
from datetime import datetime

from ciba_participant.cohort.crud.get_paginated_cohorts import (
    FullCohort,
    RawParticipant,
    RawProgram,
    RawAuthorized,
)


@pytest.mark.asyncio
async def test_fullcohort_with_none_program():
    """Test that FullCohort handles None program correctly."""
    # Create a FullCohort with None program
    cohort = FullCohort(
        id=uuid4(),
        created_at=datetime.now(),
        updated_at=datetime.now(),
        name="Test Cohort",
        started_at=datetime.now(),
        limit=10,
        program_id=uuid4(),
        created_by_id=uuid4(),
        program=None,
        participants=[],
        created_by=None,
        program_modules=[],
    )

    # Check that the cohort was created correctly
    assert cohort.program is None


@pytest.mark.asyncio
async def test_fullcohort_with_none_created_by():
    """Test that FullCohort handles None created_by correctly."""
    # Create a FullCohort with None created_by
    cohort = FullCohort(
        id=uuid4(),
        created_at=datetime.now(),
        updated_at=datetime.now(),
        name="Test Cohort",
        started_at=datetime.now(),
        limit=10,
        program_id=uuid4(),
        created_by_id=uuid4(),
        program=None,
        participants=[],
        created_by=None,
        program_modules=[],
    )

    # Check that the cohort was created correctly
    assert cohort.created_by is None


@pytest.mark.asyncio
async def test_fullcohort_with_none_participants():
    """Test that FullCohort handles None participants correctly."""
    # Create a FullCohort with None participants
    cohort = FullCohort(
        id=uuid4(),
        created_at=datetime.now(),
        updated_at=datetime.now(),
        name="Test Cohort",
        started_at=datetime.now(),
        limit=10,
        program_id=uuid4(),
        created_by_id=uuid4(),
        program=None,
        participants=None,
        created_by=None,
        program_modules=[],
    )

    # Check that the cohort was created correctly
    assert cohort.participants is None


@pytest.mark.asyncio
async def test_fullcohort_with_none_program_modules():
    """Test that FullCohort handles None program_modules correctly."""
    # Create a FullCohort with None program_modules
    cohort = FullCohort(
        id=uuid4(),
        created_at=datetime.now(),
        updated_at=datetime.now(),
        name="Test Cohort",
        started_at=datetime.now(),
        limit=10,
        program_id=uuid4(),
        created_by_id=uuid4(),
        program=None,
        participants=[],
        created_by=None,
        program_modules=None,
    )

    # Check that the cohort was created correctly
    assert cohort.program_modules is None


@pytest.mark.asyncio
async def test_rawparticipant_model_validate():
    """Test that RawParticipant.model_validate handles validation errors correctly."""
    # Create a mock participant that will cause a validation error
    mock_participant = MagicMock()

    # Mock the model_validate method to raise an exception
    with patch(
        "ciba_participant.cohort.crud.get_paginated_cohorts.RawParticipant.model_validate"
    ) as mock_validate:
        mock_validate.side_effect = Exception("Validation error")

        # Call the model_validate method
        with pytest.raises(Exception):
            RawParticipant.model_validate(mock_participant)


@pytest.mark.asyncio
async def test_rawprogram_model_validate():
    """Test that RawProgram.model_validate handles validation errors correctly."""
    # Create a mock program that will cause a validation error
    mock_program = MagicMock()

    # Mock the model_validate method to raise an exception
    with patch(
        "ciba_participant.cohort.crud.get_paginated_cohorts.RawProgram.model_validate"
    ) as mock_validate:
        mock_validate.side_effect = Exception("Validation error")

        # Call the model_validate method
        with pytest.raises(Exception):
            RawProgram.model_validate(mock_program)


@pytest.mark.asyncio
async def test_rawauthorized_model_validate():
    """Test that RawAuthorized.model_validate handles validation errors correctly."""
    # Create a mock authorized that will cause a validation error
    mock_authorized = MagicMock()

    # Mock the model_validate method to raise an exception
    with patch(
        "ciba_participant.cohort.crud.get_paginated_cohorts.RawAuthorized.model_validate"
    ) as mock_validate:
        mock_validate.side_effect = Exception("Validation error")

        # Call the model_validate method
        with pytest.raises(Exception):
            RawAuthorized.model_validate(mock_authorized)
