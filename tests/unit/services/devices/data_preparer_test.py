from unittest.mock import patch, AsyncMock, MagicMock

import pytest

from app.services.devices.data_preparer import DataPreparer, _create_cte

# Use only asyncio backend for tests
pytestmark = pytest.mark.anyio(backends=["asyncio"])


def test_create_cte():
    """Test that _create_cte returns the expected SQL CTE."""
    cte = _create_cte()

    # Check that the CTE contains the expected SQL fragments
    assert "WITH RECURSIVE rpm(email, type)" in cte
    assert "SELECT participant.email" in cte
    assert "pms.type" in cte
    assert "FROM participant" in cte
    assert "LEFT JOIN program_group_members pgm" in cte
    assert "LEFT JOIN program_module pm" in cte
    assert "LEFT JOIN program_module_section pms" in cte
    assert "LEFT JOIN program_module_section_progress pmsp" in cte
    assert "WHERE pmsp.program_module_section_id IS NULL" in cte


@pytest.mark.anyio
@patch("app.services.devices.data_preparer.in_transaction")
async def test_query_with_type(mock_in_transaction):
    """Test that _query_with_type executes the expected SQL query."""
    # Setup mock
    mock_connection = AsyncMock()
    mock_connection.execute_query_dict.return_value = [{"result": "test"}]

    # Setup context manager
    mock_context = MagicMock()
    mock_context.__aenter__.return_value = mock_connection
    mock_in_transaction.return_value = mock_context

    # Create DataPreparer instance and call _query_with_type
    data_preparer = DataPreparer()
    result = await data_preparer._query_with_type("weight_type")

    # Verify the result
    assert result == [{"result": "test"}]

    # Verify the SQL query
    expected_cte = _create_cte()
    expected_query = (
        f"{expected_cte} SELECT email,pms_id,participant_id,program_module_id"
        f" FROM rpm WHERE rpm.type = "
        f"'weight_type';"
    )
    mock_connection.execute_query_dict.assert_called_once_with(expected_query)


@pytest.mark.anyio
@patch("app.services.devices.data_preparer.DataPreparer._query_with_type")
async def test_get_full_data(mock_query_with_type):
    """Test that _get_full_data calls _query_with_type with the expected arguments."""
    # Setup mock
    mock_query_with_type.side_effect = [
        [{"weight": "data"}],
        [{"activity": "data"}],
    ]

    # Create DataPreparer instance and call _get_full_data
    data_preparer = DataPreparer()
    weight_results, activity_results = await data_preparer._get_full_data()

    # Verify the results
    assert weight_results == [{"weight": "data"}]
    assert activity_results == [{"activity": "data"}]

    # Verify _query_with_type was called with the expected arguments
    assert mock_query_with_type.call_count == 2
    mock_query_with_type.assert_any_call("weight_type")
    mock_query_with_type.assert_any_call("activity_type")


@pytest.mark.anyio
@patch("app.services.devices.data_preparer.DataPreparer._get_full_data")
async def test_prepare_data(mock_get_full_data):
    """Test that prepare_data calls _get_full_data and returns its result."""
    # Setup mock
    mock_get_full_data.return_value = (
        [{"weight": "data"}],
        [{"activity": "data"}],
    )

    # Create DataPreparer instance and call prepare_data
    data_preparer = DataPreparer()
    result = await data_preparer.prepare_data()

    # Verify the result
    assert result == ([{"weight": "data"}], [{"activity": "data"}])

    # Verify _get_full_data was called
    mock_get_full_data.assert_called_once()
