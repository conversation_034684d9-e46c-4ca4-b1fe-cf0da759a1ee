from unittest.mock import patch, AsyncMock

import pytest

from app.services.devices.result_service import DataRequestHandler
from ciba_participant.participant.user_service import Devices

# Use only asyncio backend for tests
pytestmark = pytest.mark.anyio(backends=["asyncio"])


@pytest.mark.anyio
@patch("app.services.devices.result_service._loop_handler")
async def test_loop_handler(mock_loop_handler):
    """Test that _loop_handler is called with the correct arguments.

    Since _loop_handler is complex with multiple awaits, we'll mock it entirely
    and test the DataRequestHandler.get_result method instead.
    """
    # Setup mock
    mock_loop_handler.return_value = None

    # Create test data for DataRequestHandler.get_result
    with (
        patch(
            "app.services.devices.result_service.DataPreparer"
        ) as mock_data_preparer,
        patch(
            "app.services.devices.result_service.RPMRequestHandler"
        ) as mock_rpm_handler,
    ):
        # Setup mocks
        mock_data_preparer_instance = AsyncMock()
        mock_data_preparer.return_value = mock_data_preparer_instance
        mock_data_preparer_instance.prepare_data.return_value = (
            [{"email": "<EMAIL>", "weight": "data"}],
            [{"email": "<EMAIL>", "activity": "data"}],
        )

        mock_rpm_handler_instance = AsyncMock()
        mock_rpm_handler.return_value = mock_rpm_handler_instance

        # Mock the post_data method to return different values for different calls
        mock_rpm_handler_instance.post_data.side_effect = [
            [{"email": "<EMAIL>", "value": "weight_value"}],
            [{"email": "<EMAIL>", "value": "activity_value"}],
        ]

        # Create DataRequestHandler instance and call get_result
        handler = DataRequestHandler()
        await handler.get_result()

        # Verify _loop_handler was called with the correct arguments
        assert mock_loop_handler.call_count == 2

        # First call should be for weight data
        weight_data = mock_loop_handler.call_args_list[0][0][0]
        assert weight_data[0]["email"] == "<EMAIL>"
        assert weight_data[0]["value"] == "weight_value"
        assert weight_data[0]["weight"] == "data"
        assert mock_loop_handler.call_args_list[0][0][1] == Devices.SCALE

        # Second call should be for activity data
        activity_data = mock_loop_handler.call_args_list[1][0][0]
        assert activity_data[0]["email"] == "<EMAIL>"
        assert activity_data[0]["value"] == "activity_value"
        assert activity_data[0]["activity"] == "data"
        assert mock_loop_handler.call_args_list[1][0][1] == Devices.WATCH


@pytest.mark.anyio
async def test_merge_list_dicts():
    """Test that _merge_list_dicts correctly merges two lists of dictionaries."""
    # Create test data
    dict1 = [
        {"email": "<EMAIL>", "data1": "value1"},
        {"email": "<EMAIL>", "data1": "value2"},
        {"email": "<EMAIL>", "data1": "value3"},
    ]

    dict2 = [
        {"email": "<EMAIL>", "data2": "value4"},
        {"email": "<EMAIL>", "data2": "value5"},
    ]

    # Create DataRequestHandler instance and call _merge_list_dicts
    handler = DataRequestHandler()
    result = await handler._merge_list_dicts(dict1, dict2)

    # Verify the result
    assert len(result) == 3
    assert result[0] == {
        "email": "<EMAIL>",
        "data1": "value1",
        "data2": "value4",
    }
    assert result[1] == {"email": "<EMAIL>", "data1": "value2"}
    assert result[2] == {
        "email": "<EMAIL>",
        "data1": "value3",
        "data2": "value5",
    }


@pytest.mark.anyio
@patch("app.services.devices.result_service.DataPreparer")
@patch("app.services.devices.result_service.RPMRequestHandler")
@patch("app.services.devices.result_service._loop_handler")
async def test_get_result(
    mock_loop_handler, mock_rpm_handler, mock_data_preparer
):
    """Test that get_result processes data correctly."""
    # Setup mocks
    mock_data_preparer_instance = AsyncMock()
    mock_data_preparer.return_value = mock_data_preparer_instance
    mock_data_preparer_instance.prepare_data.return_value = (
        [{"email": "<EMAIL>", "weight": "data"}],
        [{"email": "<EMAIL>", "activity": "data"}],
    )

    mock_rpm_handler_instance = AsyncMock()
    mock_rpm_handler.return_value = mock_rpm_handler_instance

    # Mock the post_data method to return different values for different calls
    mock_rpm_handler_instance.post_data.side_effect = [
        [{"email": "<EMAIL>", "value": "weight_value"}],
        [{"email": "<EMAIL>", "value": "activity_value"}],
    ]

    # Create DataRequestHandler instance and call get_result
    handler = DataRequestHandler()
    await handler.get_result()

    # Verify the mocks were called correctly
    mock_data_preparer_instance.prepare_data.assert_called_once()

    assert mock_rpm_handler.call_count == 2
    mock_rpm_handler.assert_any_call("user_data/daily_weight")
    mock_rpm_handler.assert_any_call("user_data/daily_activity")

    assert mock_rpm_handler_instance.post_data.call_count == 2
    mock_rpm_handler_instance.post_data.assert_any_call(
        {"emails": [{"email": "<EMAIL>", "weight": "data"}]}
    )
    mock_rpm_handler_instance.post_data.assert_any_call(
        {"emails": [{"email": "<EMAIL>", "activity": "data"}]}
    )

    assert mock_loop_handler.call_count == 2
    # First call should be for weight data
    weight_data = mock_loop_handler.call_args_list[0][0][0]
    assert weight_data[0]["email"] == "<EMAIL>"
    assert weight_data[0]["value"] == "weight_value"
    assert weight_data[0]["weight"] == "data"
    assert mock_loop_handler.call_args_list[0][0][1] == Devices.SCALE

    # Second call should be for activity data
    activity_data = mock_loop_handler.call_args_list[1][0][0]
    assert activity_data[0]["email"] == "<EMAIL>"
    assert activity_data[0]["value"] == "activity_value"
    assert activity_data[0]["activity"] == "data"
    assert mock_loop_handler.call_args_list[1][0][1] == Devices.WATCH
