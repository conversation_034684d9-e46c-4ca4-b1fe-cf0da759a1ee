import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from botocore.exceptions import (
    NoCredentialsError,
    PartialCredentialsError,
    ClientError,
)

from app.services.checks.service_connection import (
    check_postgres,
    check_redis_connection,
    check_http_service,
    check_cognito_connection,
    verify_s3_connection,
    get_sqs_client,
    verify_sqs_connection,
)

# Use only asyncio backend for tests
pytestmark = pytest.mark.anyio(backends=["asyncio"])


@pytest.mark.anyio
async def test_check_postgres_success():
    """Test check_postgres function when connection is successful."""
    # Mock Tortoise connection
    mock_conn = AsyncMock()
    mock_conn.execute_query.return_value = True

    with patch(
        "app.services.checks.service_connection.Tortoise.get_connection",
        return_value=mock_conn,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.POSTGRES_HOST = "localhost"
        mock_settings.POSTGRES_PORT = 5432

        result = await check_postgres(mock_settings)

        assert result is True
        mock_conn.execute_query.assert_called_once_with("SELECT 1;")


@pytest.mark.anyio
async def test_check_postgres_failure():
    """Test check_postgres function when connection fails."""
    # Mock Tortoise connection
    mock_conn = AsyncMock()
    mock_conn.execute_query.side_effect = Exception("Connection error")

    with patch(
        "app.services.checks.service_connection.Tortoise.get_connection",
        return_value=mock_conn,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.POSTGRES_HOST = "localhost"
        mock_settings.POSTGRES_PORT = 5432

        result = await check_postgres(mock_settings)

        assert result is False
        mock_conn.execute_query.assert_called_once_with("SELECT 1;")


def test_get_sqs_client():
    """Test get_sqs_client function."""
    # Mock boto3 client
    mock_client = MagicMock()

    with patch(
        "app.services.checks.service_connection.boto3.client",
        return_value=mock_client,
    ) as mock_boto3:
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.AWS_REGION = "us-east-1"

        client = get_sqs_client(mock_settings)

        assert client == mock_client
        mock_boto3.assert_called_once_with("sqs", region_name="us-east-1")


@pytest.mark.anyio
async def test_check_redis_connection_success():
    """Test check_redis_connection function when connection is successful."""
    # Mock Redis client
    mock_redis = AsyncMock()
    mock_redis.ping.return_value = True

    with patch(
        "app.services.checks.service_connection.aioredis.from_url",
        return_value=mock_redis,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.REDIS_HOST = "localhost"
        mock_settings.REDIS_PORT = 6379
        mock_settings.REDIS_PASSWORD = "password"

        result = await check_redis_connection(mock_settings)

        assert result is True
        mock_redis.ping.assert_called_once()
        mock_redis.close.assert_called_once()
        mock_redis.connection_pool.disconnect.assert_called_once()


@pytest.mark.anyio
async def test_check_redis_connection_failure():
    """Test check_redis_connection function when connection fails."""
    # Mock Redis client to raise exception
    with patch(
        "app.services.checks.service_connection.aioredis.from_url",
        side_effect=Exception("Connection error"),
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.REDIS_HOST = "localhost"
        mock_settings.REDIS_PORT = 6379
        mock_settings.REDIS_PASSWORD = "password"

        result = await check_redis_connection(mock_settings)

        assert result is False


@pytest.mark.anyio
async def test_verify_s3_connection_success():
    """Test verify_s3_connection function when connection is successful."""
    # Mock S3 client
    mock_client = MagicMock()

    with patch(
        "app.services.checks.service_connection.get_s3_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.AWS_BUCKET_NAME = "test-bucket"

        result = await verify_s3_connection(mock_settings)

        assert result is True
        mock_client.head_bucket.assert_called_once_with(Bucket="test-bucket")


@pytest.mark.anyio
async def test_verify_s3_connection_no_credentials():
    """Test verify_s3_connection function with NoCredentialsError."""
    # Mock S3 client to raise NoCredentialsError
    mock_client = MagicMock()
    mock_client.head_bucket.side_effect = NoCredentialsError()

    with patch(
        "app.services.checks.service_connection.get_s3_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.AWS_BUCKET_NAME = "test-bucket"

        result = await verify_s3_connection(mock_settings)

        assert result is False
        mock_client.head_bucket.assert_called_once_with(Bucket="test-bucket")


@pytest.mark.anyio
async def test_verify_s3_connection_client_error():
    """Test verify_s3_connection function with ClientError."""
    # Mock S3 client to raise ClientError
    mock_client = MagicMock()
    error_response = {
        "Error": {
            "Code": "NoSuchBucket",
            "Message": "The specified bucket does not exist",
        }
    }
    mock_client.head_bucket.side_effect = ClientError(
        error_response, "HeadBucket"
    )

    with patch(
        "app.services.checks.service_connection.get_s3_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.AWS_BUCKET_NAME = "test-bucket"

        result = await verify_s3_connection(mock_settings)

        assert result is False
        mock_client.head_bucket.assert_called_once_with(Bucket="test-bucket")


@pytest.mark.anyio
async def test_verify_s3_connection_other_exception():
    """Test verify_s3_connection function with other exception."""
    # Mock S3 client to raise other exception
    mock_client = MagicMock()
    mock_client.head_bucket.side_effect = Exception("Some other error")

    with patch(
        "app.services.checks.service_connection.get_s3_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.AWS_BUCKET_NAME = "test-bucket"

        result = await verify_s3_connection(mock_settings)

        assert result is False
        mock_client.head_bucket.assert_called_once_with(Bucket="test-bucket")


@pytest.mark.anyio
async def test_check_http_service_success():
    """Test check_http_service function when connection is successful."""
    # Mock httpx client
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_client = AsyncMock()
    mock_client.get.return_value = mock_response

    # Mock the async context manager
    mock_client_cm = MagicMock()
    mock_client_cm.__aenter__.return_value = mock_client
    mock_client_cm.__aexit__.return_value = None

    with patch(
        "app.services.checks.service_connection.httpx.AsyncClient",
        return_value=mock_client_cm,
    ):
        host = "http://example.com"
        result = await check_http_service(host)

        assert result is True
        mock_client.get.assert_called_once_with(f"{host}/health")


@pytest.mark.anyio
async def test_check_http_service_non_200_response():
    """Test check_http_service function when response is not 200."""
    # Mock httpx client
    mock_response = MagicMock()
    mock_response.status_code = 500
    mock_client = AsyncMock()
    mock_client.get.return_value = mock_response

    # Mock the async context manager
    mock_client_cm = MagicMock()
    mock_client_cm.__aenter__.return_value = mock_client
    mock_client_cm.__aexit__.return_value = None

    with patch(
        "app.services.checks.service_connection.httpx.AsyncClient",
        return_value=mock_client_cm,
    ):
        host = "http://example.com"
        result = await check_http_service(host)

        assert result is False
        mock_client.get.assert_called_once_with(f"{host}/health")


@pytest.mark.anyio
async def test_check_http_service_failure():
    """Test check_http_service function when connection fails."""
    # Mock httpx client to raise exception
    mock_client = AsyncMock()
    mock_client.get.side_effect = Exception("Connection error")

    # Mock the async context manager
    mock_client_cm = MagicMock()
    mock_client_cm.__aenter__.return_value = mock_client
    mock_client_cm.__aexit__.return_value = None

    with patch(
        "app.services.checks.service_connection.httpx.AsyncClient",
        return_value=mock_client_cm,
    ):
        host = "http://example.com"
        result = await check_http_service(host)

        assert result is False
        mock_client.get.assert_called_once_with(f"{host}/health")


@pytest.mark.anyio
async def test_check_http_service_empty_host():
    """Test check_http_service function with empty host."""
    # Mock httpx client
    mock_response = MagicMock()
    mock_response.status_code = 404
    mock_client = AsyncMock()
    mock_client.get.return_value = mock_response

    # Mock the async context manager
    mock_client_cm = MagicMock()
    mock_client_cm.__aenter__.return_value = mock_client
    mock_client_cm.__aexit__.return_value = None

    with patch(
        "app.services.checks.service_connection.httpx.AsyncClient",
        return_value=mock_client_cm,
    ):
        host = ""
        result = await check_http_service(host)

        assert result is False
        # The function still tries to connect with an empty string
        mock_client.get.assert_called_once_with("/health")


@pytest.mark.anyio
async def test_verify_sqs_connection_success():
    """Test verify_sqs_connection function when connection is successful."""
    # Mock SQS client
    mock_client = MagicMock()
    mock_client.list_queues.return_value = {"QueueUrls": ["queue1", "queue2"]}

    with patch(
        "app.services.checks.service_connection.get_sqs_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()

        result = await verify_sqs_connection(mock_settings)

        assert result is True
        mock_client.list_queues.assert_called_once()


@pytest.mark.anyio
async def test_verify_sqs_connection_empty_queues():
    """Test verify_sqs_connection function with empty queues list."""
    # Mock SQS client with empty queues list
    mock_client = MagicMock()
    mock_client.list_queues.return_value = {}

    with patch(
        "app.services.checks.service_connection.get_sqs_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()

        result = await verify_sqs_connection(mock_settings)

        assert result is True
        mock_client.list_queues.assert_called_once()


@pytest.mark.anyio
async def test_verify_sqs_connection_no_credentials():
    """Test verify_sqs_connection function with NoCredentialsError."""
    # Mock SQS client to raise NoCredentialsError
    mock_client = MagicMock()
    mock_client.list_queues.side_effect = NoCredentialsError()

    with patch(
        "app.services.checks.service_connection.get_sqs_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()

        result = await verify_sqs_connection(mock_settings)

        assert result is False
        mock_client.list_queues.assert_called_once()


@pytest.mark.anyio
async def test_verify_sqs_connection_client_error():
    """Test verify_sqs_connection function with ClientError."""
    # Mock SQS client to raise ClientError
    mock_client = MagicMock()
    error_response = {
        "Error": {
            "Code": "InvalidClientTokenId",
            "Message": "The security token included in the request is invalid",
        }
    }
    mock_client.list_queues.side_effect = ClientError(
        error_response, "ListQueues"
    )

    with patch(
        "app.services.checks.service_connection.get_sqs_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()

        result = await verify_sqs_connection(mock_settings)

        assert result is False
        mock_client.list_queues.assert_called_once()


@pytest.mark.anyio
async def test_check_cognito_connection_success():
    """Test check_cognito_connection function when connection is successful."""
    # Mock cognito client
    mock_client = MagicMock()
    mock_client.describe_user_pool.return_value = {
        "UserPool": {"Id": "user-pool-id", "Name": "user-pool-name"}
    }

    with patch(
        "app.services.checks.service_connection.get_cognito_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.COGNITO_USER_POOL_ID = "user-pool-id"

        result = await check_cognito_connection(mock_settings)

        assert result is True
        mock_client.describe_user_pool.assert_called_once_with(
            UserPoolId="user-pool-id"
        )


@pytest.mark.anyio
async def test_check_cognito_connection_no_credentials():
    """Test check_cognito_connection function with NoCredentialsError."""
    # Mock cognito client to raise NoCredentialsError
    mock_client = MagicMock()
    mock_client.describe_user_pool.side_effect = NoCredentialsError()

    with patch(
        "app.services.checks.service_connection.get_cognito_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.COGNITO_USER_POOL_ID = "user-pool-id"

        result = await check_cognito_connection(mock_settings)

        assert result is False
        mock_client.describe_user_pool.assert_called_once_with(
            UserPoolId="user-pool-id"
        )


@pytest.mark.anyio
async def test_check_cognito_connection_partial_credentials():
    """Test check_cognito_connection function with PartialCredentialsError."""
    # Mock cognito client to raise PartialCredentialsError
    mock_client = MagicMock()
    error = PartialCredentialsError(
        provider="cognito", cred_var="aws_secret_access_key"
    )
    mock_client.describe_user_pool.side_effect = error

    with patch(
        "app.services.checks.service_connection.get_cognito_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.COGNITO_USER_POOL_ID = "user-pool-id"

        result = await check_cognito_connection(mock_settings)

        assert result is False
        mock_client.describe_user_pool.assert_called_once_with(
            UserPoolId="user-pool-id"
        )


@pytest.mark.anyio
async def test_check_cognito_connection_resource_not_found():
    """Test check_cognito_connection function with ResourceNotFoundException."""
    # Mock cognito client to raise ClientError with ResourceNotFoundException
    mock_client = MagicMock()
    error_response = {
        "Error": {
            "Code": "ResourceNotFoundException",
            "Message": "Resource not found",
        }
    }
    mock_client.describe_user_pool.side_effect = ClientError(
        error_response, "operation"
    )

    with patch(
        "app.services.checks.service_connection.get_cognito_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.COGNITO_USER_POOL_ID = "user-pool-id"

        result = await check_cognito_connection(mock_settings)

        assert result is False
        mock_client.describe_user_pool.assert_called_once_with(
            UserPoolId="user-pool-id"
        )


@pytest.mark.anyio
async def test_check_cognito_connection_other_client_error():
    """Test check_cognito_connection function with other ClientError."""
    # Mock cognito client to raise ClientError with other error
    mock_client = MagicMock()
    error_response = {
        "Error": {"Code": "OtherError", "Message": "Other error"}
    }
    mock_client.describe_user_pool.side_effect = ClientError(
        error_response, "operation"
    )

    with patch(
        "app.services.checks.service_connection.get_cognito_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.COGNITO_USER_POOL_ID = "user-pool-id"

        result = await check_cognito_connection(mock_settings)

        assert result is False
        mock_client.describe_user_pool.assert_called_once_with(
            UserPoolId="user-pool-id"
        )


@pytest.mark.anyio
async def test_check_cognito_connection_general_exception():
    """Test check_cognito_connection function with general exception."""
    # Mock cognito client to raise general exception
    mock_client = MagicMock()
    mock_client.describe_user_pool.side_effect = Exception("General error")

    with patch(
        "app.services.checks.service_connection.get_cognito_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.COGNITO_USER_POOL_ID = "user-pool-id"

        result = await check_cognito_connection(mock_settings)

        assert result is False
        mock_client.describe_user_pool.assert_called_once_with(
            UserPoolId="user-pool-id"
        )


@pytest.mark.anyio
async def test_verify_sqs_connection_other_exception():
    """Test verify_sqs_connection function with other exception."""
    # Mock SQS client to raise other exception
    mock_client = MagicMock()
    mock_client.list_queues.side_effect = Exception("Some other error")

    with patch(
        "app.services.checks.service_connection.get_sqs_client",
        return_value=mock_client,
    ):
        # Mock settings
        mock_settings = MagicMock()

        result = await verify_sqs_connection(mock_settings)

        assert result is False
        mock_client.list_queues.assert_called_once()
