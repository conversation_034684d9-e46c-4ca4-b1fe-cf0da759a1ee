from typing import Iterator
import pytest
from tortoise import Tortoise
from tortoise.contrib.test import (
    _init_db,
    getDBConfig,
    # finalizer,
    # initializer,
)

import asyncio

MODULES = [
    "aerich.models",
    "ciba_participant.activity.models",
    "ciba_participant.classes.models",
    "ciba_participant.cohort.models",
    "ciba_participant.participant.models",
    "ciba_participant.program.models",
    "ciba_participant.content_library.models",
]


@pytest.fixture(scope="session")
def event_loop() -> Iterator[asyncio.AbstractEventLoop]:
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session", autouse=True)
def initialize_test_db(request, event_loop):
    config = getDBConfig(
        modules=MODULES,
        app_label="models",
    )

    event_loop.run_until_complete(_init_db(config))

    def shutdown():
        event_loop.run_until_complete(Tortoise._drop_databases())

    request.addfinalizer(shutdown)


# Doesn't work, see https://github.com/tortoise/tortoise-orm/issues/1110
""" @pytest.fixture(scope="session", autouse=True)
def initialize_tests(request, event_loop):
    initializer(
        modules=MODULES,
        db_url="sqlite://:memory:",
        app_label="models",
        loop=event_loop,
    )
    request.addfinalizer(finalizer) """
