import pytest

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.test_utils import (
    clear_db,
    create_authorized_user,
    create_content_material,
    create_program,
)


@pytest.fixture
async def load_content_data():
    await clear_db()

    program = await create_program()
    authorized = await create_authorized_user()
    content_material = await create_content_material(
        programs=[program.id],
        activity_types=[ParticipantActivityEnum.RECIPES],
        user_id=authorized.id,
    )

    return content_material
