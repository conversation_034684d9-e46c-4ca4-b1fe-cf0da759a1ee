import pytest

from ciba_participant.content_library.service import check_duplicated_material_titles


@pytest.mark.asyncio
async def test_check_duplicated_material_titles_with_duplications_found(
    load_content_data,
):
    """
    check_duplicated_material_titles should return True
    when there are duplicated material titles
    and no exclusions are applied.
    """
    test_material = await load_content_data

    actual_value = await check_duplicated_material_titles(test_material.title)

    assert actual_value is True


@pytest.mark.asyncio
async def test_check_duplicated_material_titles_without_duplications_by_exclusion(
    load_content_data,
):
    """
    check_duplicated_material_titles should return False
    when there are duplicated material titles
    but the material with duplicated title is excluded.
    """
    test_material = await load_content_data

    actual_value = await check_duplicated_material_titles(
        test_material.title, test_material.id
    )

    assert actual_value is False


@pytest.mark.asyncio
async def test_check_duplicated_material_titles_without_duplications(load_content_data):
    """
    check_duplicated_material_titles should return False
    when there are no duplicated material titles
    """
    await load_content_data

    actual_value = await check_duplicated_material_titles("Random Title")

    assert actual_value is False
