import pytest

from ciba_participant.content_library.crud.get_content_material import (
    process,
    MaterialFilters,
)
from ciba_participant.content_library.enums import MaterialTag


@pytest.mark.asyncio
async def test_process_without_duplicates(load_content_data):
    """
    process should return the requested content material without duplicates.
    """
    content_material = await load_content_data

    test_filters = MaterialFilters(
        search="test",
        tags=[MaterialTag.ACTIVITY, MaterialTag.MOTIVATION],
    )

    actual_value = await process(filters=test_filters)

    assert actual_value.total == 1
    assert actual_value.items[0].id == content_material.id
