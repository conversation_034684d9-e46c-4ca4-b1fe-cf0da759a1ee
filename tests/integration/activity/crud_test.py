from uuid import uuid4

import pytest

from ciba_participant.activity.crud import ParticipantActivityRepository
from ciba_participant.activity.models import (
    ActivityUnit,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ParticipantActivityEnum,
)
from ciba_participant.activity.schemas import ParticipantActivityInput
from ciba_participant.participant.crud import ParticipantRepository
from ciba_participant.participant.models import ParticipantStatus
from ciba_participant.participant.pydantic_models import ParticipantCreate


@pytest.fixture
async def config_test_participant():
    test_id = uuid4()
    test_participant = await ParticipantRepository.create_participant(
        ParticipantCreate(
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="Do<PERSON>",
            group_id=test_id,
            member_id=test_id,
            status=ParticipantStatus.ACTIVE,
            is_test=True,
            cognito_sub=test_id,
            medical_record=None,
            last_reset=None,
        )
    )

    return test_participant


@pytest.mark.asyncio
async def test_get_participant_weights(config_test_participant):
    """get_activities_by_participant_id should return only the weight related activities"""

    test_participant = config_test_participant
    test_activity_1 = await ParticipantActivityRepository.create_participant_activity(
        ParticipantActivityInput(
            participant_id=test_participant.id,
            value="100",
            unit=ActivityUnit.LB.value,
            activity_device=ParticipantActivityDevice.WITHINGS.value,
            activity_category=ParticipantActivityCategory.WEIGHT.value,
            activity_type=ParticipantActivityEnum.WEIGHT.value,
        )
    )
    test_activity_2 = await ParticipantActivityRepository.create_participant_activity(
        ParticipantActivityInput(
            participant_id=test_participant.id,
            value="1",
            unit=ActivityUnit.ACTION.value,
            activity_device=ParticipantActivityDevice.MANUAL_INPUT.value,
            activity_category=ParticipantActivityCategory.STEPS.value,
            activity_type=ParticipantActivityEnum.ACTIVITY.value,
        )
    )
    actual_values = (
        await ParticipantActivityRepository.get_activities_by_participant_id(
            participant_id=test_participant.id,
            activity_type=ParticipantActivityEnum.WEIGHT.value,
        )
    )

    assert isinstance(actual_values, list)
    assert len(actual_values) == 1
    assert actual_values[0].id != test_activity_2.id
    assert actual_values[0].id == test_activity_1.id
