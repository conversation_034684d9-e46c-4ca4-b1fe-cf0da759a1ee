import pytest

from ciba_participant.classes.crud.get_creators import get_webinar_creators
from ciba_participant.test_utils import clear_db, create_authorized_user, create_webinar


@pytest.mark.asyncio
async def test_get_webinar_creators_success():
    """
    get_webinar_creators should return all webinars creators.
    """
    await clear_db()
    test_user_1 = await create_authorized_user()
    test_user_2 = await create_authorized_user()
    await create_webinar(test_user_1.id)

    actual_value = await get_webinar_creators()

    assert len(actual_value) == 1
    assert test_user_2.id != actual_value[0].id
    assert test_user_1.id == actual_value[0].id
