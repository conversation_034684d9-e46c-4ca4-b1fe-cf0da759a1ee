import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from ciba_participant.classes.crud.get_webinar import process as get_webinar
from ciba_participant.classes.crud.update_webinar import process as update_webinar
from ciba_participant.test_utils import (
    create_webinar,
    create_authorized_user,
    create_live_session,
)
from ciba_participant.classes.crud.types import WebinarUpdate, TopicEnum


@pytest.fixture
async def scenario_1():
    authorized = await create_authorized_user()
    webinar = await create_webinar(host_id=authorized.id, topic=TopicEnum.MENTAL_HEALTH)
    await create_live_session(webinar.id)
    return (webinar, authorized)


@pytest.fixture
async def scenario_2():
    authorized_1 = await create_authorized_user()
    authorized_2 = await create_authorized_user()
    webinar = await create_webinar(
        host_id=authorized_1.id, topic=TopicEnum.MENTAL_HEALTH
    )
    await create_live_session(webinar.id)
    return (webinar, authorized_1, authorized_2)


@pytest.mark.asyncio
async def test_update_title_and_description(scenario_1):
    """
    should update correctly and call schedule manager
    """
    webinar, authorized = scenario_1

    NEW_TITLE = "new title"
    NEW_DESCRIPTION = "new description"

    data = WebinarUpdate(
        id=webinar.id,
        title=NEW_TITLE,
        description=NEW_DESCRIPTION,
    )

    mock_schedule = MagicMock()
    mock_update = AsyncMock()
    mock_host = AsyncMock()
    mock_schedule.update_title_and_description = mock_update
    mock_schedule.change_meeting_host = mock_host

    with patch(
        "ciba_participant.classes.crud.update_webinar.ScheduleManager",
        return_value=mock_schedule,
    ):
        await update_webinar(data, authorized.id)

        updated_webinar = await get_webinar(webinar_id=webinar.id)

        assert updated_webinar.title == NEW_TITLE
        assert updated_webinar.description == NEW_DESCRIPTION

        mock_host.assert_not_called()
        mock_update.assert_called_once()


@pytest.mark.asyncio
async def test_update_host(scenario_2):
    """
    should update correctly and call schedule manager
    """
    webinar, authorized_1, authorized_2 = scenario_2

    data = WebinarUpdate(id=webinar.id, new_host=authorized_2.email)

    mock_schedule = MagicMock()
    mock_update = AsyncMock()
    mock_host = AsyncMock()
    mock_schedule.update_title_and_description = mock_update
    mock_schedule.change_meeting_host = mock_host

    with patch(
        "ciba_participant.classes.crud.update_webinar.ScheduleManager",
        return_value=mock_schedule,
    ):
        await update_webinar(data, authorized_1.id)

        updated_webinar = await get_webinar(webinar_id=webinar.id)

        assert updated_webinar.host is not None
        assert updated_webinar.host.id == authorized_2.id

        mock_update.assert_not_called()
        mock_host.assert_called_once()
