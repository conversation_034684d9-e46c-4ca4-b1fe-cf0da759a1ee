from unittest.mock import Mock

import pytest
from strawberry import Info

from app.auth.models import AuthContext
from app.graphql_api.participant.mutations import (
    add_weight_data,
)
from app.graphql_api.participant.types import (
    AddActivityResultType,
)
from ciba_participant.activity.models import ParticipantActivityDevice
import pendulum

from ciba_participant.test_utils import (
    clear_db,
    add_participant_to_cohort,
    create_cohort,
    DateWhen,
    create_participant,
    create_authorized_user,
    create_full_program,
    create_live_session,
    create_webinar,
)


@pytest.fixture(scope="function")
async def setup_data():
    await clear_db()

    program_resp, module_resp, section_resp = await create_full_program()
    authorized_user = await create_authorized_user()
    (
        participant_resp,
        solera_participant_resp,
        meta_resp,
    ) = await create_participant()
    participant_no_cohort_resp, _, _ = await create_participant()
    cohort_resp_now = await create_cohort(
        program_id=program_resp.id,
        created_by=authorized_user.id,
        cohort_date=DateWhen.NOW,
    )

    cohort_resp_past = [
        await create_cohort(
            program_id=program_resp.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.PAST,
        )
        for _ in range(3)
    ][-1]
    cohort_resp_future = [
        await create_cohort(
            program_id=program_resp.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.FUTURE,
        )
        for _ in range(3)
    ][-1]
    webinar_resp = await create_webinar(authorized_user.id)
    live_session_resp = await create_live_session(
        webinar_id=webinar_resp.id,
        meeting_start_time=pendulum.instance(cohort_resp_now.started_at).add(
            days=5
        ),
    )
    await add_participant_to_cohort(
        cohort_id=cohort_resp_past.id, participant_id=participant_resp.id
    )
    data = {
        "program_resp": program_resp,
        "module_resp": module_resp,
        "section_resp": section_resp,
        "authorized_user_id": authorized_user.id,
        "participant_resp": participant_resp,
        "participant_no_cohort_resp": participant_no_cohort_resp,
        "solera_participant_resp": solera_participant_resp,
        "meta_resp": meta_resp,
        "cohort_resp_past": cohort_resp_past,
        "cohort_resp_now": cohort_resp_now,
        "cohort_resp_future": cohort_resp_future,
        "live_session_resp": live_session_resp,
    }

    yield data

    await clear_db()


@pytest.mark.asyncio
async def test_add_weight_data(setup_data):
    data = await setup_data.__anext__()
    participant_data = data["participant_resp"]
    mock_context = Mock()
    mock_context.user = AuthContext(
        sub=participant_data.id,
        email=participant_data.email,
        email_verified=True,
        auth_time=1620000000,
        issuer="https://cognito-idp.us-east-1.amazonaws.com/us-east-1_123456789",
    )
    mock_info = Mock(spec=Info)
    mock_info.context = mock_context
    value = 70.5
    activity_device = ParticipantActivityDevice.MANUAL_INPUT.value
    result = await add_weight_data(
        info=mock_info, value=value, activity_device=activity_device
    )
    assert isinstance(result, AddActivityResultType), (
        "Returned object is not AddActivityResultType."
    )
    assert result.success is True, "Not successful"
