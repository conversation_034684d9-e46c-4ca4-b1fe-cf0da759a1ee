import pytest
from unittest.mock import patch

from strawberry.types import Info as InfoType
from strawberry import Info
from strawberry.types.field import St<PERSON><PERSON><PERSON>ield

from app.graphql_api.participant.queries import me
from ciba_participant.test_utils import create_participant


@pytest.fixture(scope="function")
async def happy_path():
    (
        participant,
        solera_participant,
        participant_meta,
    ) = await create_participant()

    (
        participant_disenrolled,
        solera_participant_disenrolled,
        participant_meta_disenrolled,
    ) = await create_participant(disenrolled=True)

    return (
        participant,
        solera_participant,
        participant_meta,
        participant_disenrolled,
        solera_participant_disenrolled,
        participant_meta_disenrolled,
    )


@pytest.mark.asyncio
async def test_me(happy_path):
    (
        participant,
        solera_participant,
        participant_metadata,
        participant_disenrolled,
        solera_participant_disenrolled,
        participant_metadata_disenrolled,
    ) = await happy_path

    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        # Active participant
        field: StrawberryField = StrawberryField()
        mock_info: InfoType = Info(GraphQLResolveInfo, field)
        mock_info.context.user.sub = participant.id

        resp = await me(mock_info)

        assert resp is not None
        assert resp.cognito_sub == participant.cognito_sub
        assert resp.email == participant.email
        assert resp.first_name == participant.first_name
        assert resp.last_name == participant.last_name
        assert resp.group_id == participant.group_id
        assert resp.member_id == participant.member_id
        assert resp.status == participant.status.value
        assert resp.medical_record == participant.medical_record
        assert resp.is_weight is False
        assert resp.solera_id == solera_participant.solera_id
        assert resp.solera_program_id == solera_participant.solera_program_id
        assert resp.chat_identity == participant.chat_identity
        assert resp.disenrollment_reason is None
        assert resp.disenrollment_date is None

        # Disenrolled participant
        field: StrawberryField = StrawberryField()
        mock_info: InfoType = Info(GraphQLResolveInfo, field)
        mock_info.context.user.sub = participant_disenrolled.id

        resp = await me(mock_info)

        assert resp is not None
        assert resp.cognito_sub == participant_disenrolled.cognito_sub
        assert resp.email == participant_disenrolled.email
        assert resp.first_name == participant_disenrolled.first_name
        assert resp.last_name == participant_disenrolled.last_name
        assert resp.group_id == participant_disenrolled.group_id
        assert resp.member_id == participant_disenrolled.member_id
        assert resp.status == participant_disenrolled.status.value
        assert resp.medical_record == participant_disenrolled.medical_record
        assert resp.is_weight is False
        assert resp.solera_id == solera_participant_disenrolled.solera_id
        assert (
            resp.solera_program_id
            == solera_participant_disenrolled.solera_program_id
        )
        assert resp.chat_identity == participant_disenrolled.chat_identity
        assert (
            resp.disenrollment_reason
            == participant_metadata_disenrolled.metadata.get(
                "disenrolledReason", None
            )
        )
        assert (
            resp.disenrollment_date
            == participant_metadata_disenrolled.metadata.get(
                "disenrollmentDate", None
            )
        )
