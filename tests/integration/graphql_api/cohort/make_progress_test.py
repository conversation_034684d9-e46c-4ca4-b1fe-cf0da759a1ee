import pytest
from unittest.mock import AsyncMock, patch

import pendulum
from strawberry.types import Info as InfoType
from strawberry import Info
from strawberry.types.field import <PERSON><PERSON><PERSON><PERSON>ield
from ciba_participant.test_utils import (
    DateWhen,
    add_participant_to_cohort,
    create_authorized_user,
    create_cohort,
    create_full_program,
    create_participant,
    clear_db,
)
from ciba_participant.activity.models import ParticipantActivity

from app.graphql_api.program.mutations import make_progress
from app.graphql_api.program.types import ModuleProgressType
from ciba_participant.helpers.timezone_conversion import TIMEZONE_HEADER


@pytest.fixture(scope="function")
async def happy_path():
    with patch(
        "ciba_participant.schedule_manager.service.ScheduleManager.create_zoom_meetings",
        new_callable=AsyncMock,
    ):
        await clear_db()

        program, module, section = await create_full_program()
        authorized_user = await create_authorized_user()
        (
            participant,
            solera_participant,
            participant_meta,
        ) = await create_participant()

        cohort = await create_cohort(
            program_id=program.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.NOW,
        )

        await add_participant_to_cohort(
            cohort_id=cohort.id, participant_id=participant.id
        )

        participant_future, _, _ = await create_participant()

        future_cohort = await create_cohort(
            program_id=program.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.FUTURE,
        )

        await add_participant_to_cohort(
            cohort_id=future_cohort.id, participant_id=participant_future.id
        )

        return (
            participant,
            participant_future,
            module,
            section,
            future_cohort,
        )


@pytest.mark.asyncio
async def test_make_progress(happy_path):
    (
        participant,
        participant_future,
        module,
        section,
        future_cohort,
    ) = await happy_path

    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        mock_info: InfoType = Info(GraphQLResolveInfo, field)
        mock_info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}
        mock_info.context.user.sub = participant.id

        # Test make progress
        status: ModuleProgressType = await make_progress(
            info=mock_info, program_module_id=module.id, section_id=section.id
        )

        activity = await ParticipantActivity.get_or_none(
            participant_id=participant.id, section_id=section.id
        )

        assert status is not None
        assert activity is not None

        # Test make progress when activity exists
        status: ModuleProgressType = await make_progress(
            info=mock_info, program_module_id=module.id, section_id=section.id
        )

        activity_count = await ParticipantActivity.filter(
            participant_id=participant.id, section_id=section.id
        ).count()

        assert status is not None
        assert activity_count == 1

        # Test make progress for future live session
        mock_info.context.user.sub = participant_future.id

        status: ModuleProgressType = await make_progress(
            info=mock_info, program_module_id=module.id, section_id=section.id
        )

        activity = await ParticipantActivity.get_or_none(
            participant_id=participant_future.id, section_id=section.id
        )

        assert status is not None
        assert activity is not None
        assert (
            activity.created_at.date()
            == future_cohort.started_at.date() + pendulum.duration(days=1)
        )
