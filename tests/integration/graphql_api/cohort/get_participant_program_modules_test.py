import pendulum
import pytest

from unittest.mock import patch

from app.graphql_api import schema as graphql_schema

from ciba_participant.classes.models import TopicEnum
from ciba_participant.test_utils import (
    DateWhen,
    add_participant_to_cohort,
    create_authorized_user,
    create_cohort,
    create_full_program,
    create_participant,
    clear_db,
    create_booking,
    create_webinar,
    create_live_session,
)
from ciba_participant.activity.crud import ParticipantActivityRepository
from ciba_participant.activity.schemas import ParticipantActivityInput
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityCategory,
    ActivityUnit,
    ParticipantActivityDevice,
)


class Request:
    headers: dict


async def init_scenarios(cohort_start: DateWhen):
    authorized_user = await create_authorized_user()
    program, module, _ = await create_full_program()

    cohort = await create_cohort(
        program_id=program.id,
        created_by=authorized_user.id,
        cohort_date=cohort_start,
    )

    participant, _, _ = await create_participant()
    await add_participant_to_cohort(
        cohort_id=cohort.id, participant_id=participant.id
    )

    webinar = await create_webinar(authorized_user.id, TopicEnum.INTRO_SESSION)

    if cohort_start == DateWhen.FUTURE:
        meeting_start = pendulum.now().add(days=5)
    elif cohort_start == DateWhen.NOW:
        meeting_start = pendulum.now().add(hours=2)
    elif cohort_start == DateWhen.PAST:
        meeting_start = pendulum.now().subtract(days=5)
    elif cohort_start == DateWhen.YESTERDAY:
        meeting_start = pendulum.now().subtract(days=1)

    live_session = await create_live_session(webinar.id, meeting_start)

    return (
        program,
        module,
        participant,
        webinar,
        live_session,
        authorized_user,
        cohort,
    )


@pytest.fixture
async def scenario_1():
    """
    scenario 1: user is enrolled and has no class or chat activity
    """
    await clear_db()

    (
        program,
        module,
        participant,
        _,
        _,
        _,
        _,
    ) = await init_scenarios(DateWhen.NOW)

    data = {
        "program": program,
        "module": module,
        "participant": participant,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_2():
    """
    scenario 2: user is enrolled and has an intro session booking and one chat activity
    """
    await clear_db()

    (
        program,
        module,
        participant,
        _,
        live_session,
        _,
        _,
    ) = await init_scenarios(DateWhen.NOW)

    booking = await create_booking(participant.id, live_session.id)

    await ParticipantActivityRepository.create_participant_activity(
        ParticipantActivityInput(
            participant_id=participant.id,
            value=1,
            unit=ActivityUnit.ACTION,
            activity_device=ParticipantActivityDevice.MANUAL_INPUT,
            activity_category=ParticipantActivityCategory.ACTIVITY,
            activity_type=ParticipantActivityEnum.COACH,
        )
    )

    data = {
        "program": program,
        "module": module,
        "participant": participant,
        "live_session": live_session,
        "booking": booking,
    }

    yield data

    await clear_db()


def init_context(participant_id: str):
    variables = {"participantId": participant_id}
    request = Request()
    request.headers = {"Time-Zone-Offset": "00:00"}
    mock_context = type(
        "Context",
        (),
        {
            "user": type(
                "User", (), {"is_authenticated": True, "sub": "123"}
            )(),
            "request": request,
        },
    )()

    return variables, mock_context


QUERY = """query GetModuleProgress($programModuleId: UUID!, $participantId: UUID!) {
  getModuleProgress(programModuleId: $programModuleId, participantId: $participantId) {
    id
    completed
    programWeightTrend
    previousModuleWeightTrend
    sections {
      sectionId
      sectionType
      metadata {
        ... on DietitianCallProgressMetadataType {
          startDate
          endDate
        }
        ... on CoachingCallProgressMetadataType {
          startDate
          endDate
        }
        ... on CurriculumProgressMetadataType {
          startDate
          endDate
        }
        ... on PersonalSuccessProgressMetadataType {
          startDate
          endDate
          responseId
        }
        ... on WeightTypeProgressMetadataType {
          startDate
          endDate
          value
        }
        ... on FoodTypeProgressMetadataType {
          startDate
          endDate
          value
        }
        ... on VideoTypeProgressMetadataType {
          startDate
          endDate
          value
        }
        ... on QuizTypeProgressMetadataType {
          startDate
          endDate
          responseId
        }
        ... on RecipeTypeProgressMetadataType {
          startDate
          endDate
        }
        ... on ActivityTypeProgressMetadataType {
          startDate
          endDate
          value
        }
      }
      values {
        ... on WeightTypeProgressMetadataType {
          startDate
          endDate
          value
        }
        ... on ActivityTypeProgressMetadataType {
          startDate
          endDate
          value
        }
      }
      completed
    }
    classActivities {
      completed
      activityDate
      title
    }
    chatActivities {
      completed
      activityDate
      title
    }
  }
}"""


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_no_activities(scenario_1, mocker):  # noqa: F811
    """
    case when the participant has booked an intro session on enrollment
    """
    data = await scenario_1.__anext__()

    participant_id = str(data["participant"].id)
    module = data["module"]

    _, mock_context = init_context(participant_id)

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
        mocker.patch(
            "app.auth.permissions.CanViewParticipant.has_permission",
            return_value=True,
        ),
    ):
        progress_result = await graphql_schema.execute(
            QUERY,
            variable_values={
                "participantId": participant_id,
                "programModuleId": str(module.id),
            },
            context_value=mock_context,
        )

        assert progress_result.errors is None

        progress = progress_result.data["getModuleProgress"]

        assert progress["completed"] is False
        assert len(progress["sections"]) == 8
        assert len(progress["classActivities"]) == 0
        assert len(progress["chatActivities"]) == 0


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_has_activities(scenario_2, mocker):  # noqa: F811
    """
    case when the participant has booked an intro session on enrollment
    """
    data = await scenario_2.__anext__()

    participant_id = str(data["participant"].id)
    module = data["module"]

    _, mock_context = init_context(participant_id)

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
        mocker.patch(
            "app.auth.permissions.CanViewParticipant.has_permission",
            return_value=True,
        ),
    ):
        progress_result = await graphql_schema.execute(
            QUERY,
            variable_values={
                "participantId": participant_id,
                "programModuleId": str(module.id),
            },
            context_value=mock_context,
        )

        assert progress_result.errors is None

        progress = progress_result.data["getModuleProgress"]

        assert progress["completed"] is False
        assert len(progress["sections"]) == 8
        assert len(progress["classActivities"]) == 1
        assert len(progress["chatActivities"]) == 1
