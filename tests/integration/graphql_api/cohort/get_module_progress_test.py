import pytest
from unittest.mock import AsyncMock, patch
from strawberry.types import Info as InfoType
from strawberry import Info
from strawberry.types.field import <PERSON><PERSON><PERSON><PERSON>ield

from app.graphql_api.program.queries import get_module_progress
from app.graphql_api.program.types import ModuleProgressType
from ciba_participant.test_utils import (
    DateWhen,
    add_participant_to_cohort,
    create_authorized_user,
    create_cohort,
    create_full_program,
    create_participant,
    create_participant_activity,
    create_participant_weight_activity,
    clear_db,
)
from ciba_participant.activity.models import TrendEnum
from ciba_participant.helpers.timezone_conversion import TIMEZONE_HEADER


@pytest.fixture(scope="function")
async def no_progress():
    with patch(
        "ciba_participant.schedule_manager.service.ScheduleManager.create_zoom_meetings",
        new_callable=AsyncMock,
    ):
        await clear_db()

        program, module, _ = await create_full_program()
        authorized_user = await create_authorized_user()
        (
            participant,
            solera_participant,
            participant_meta,
        ) = await create_participant()

        cohort = await create_cohort(
            program_id=program.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.NOW,
        )

        await add_participant_to_cohort(
            cohort_id=cohort.id, participant_id=participant.id
        )

        return participant, module


@pytest.fixture(scope="function")
async def happy_path():
    with patch(
        "ciba_participant.schedule_manager.service.ScheduleManager.create_zoom_meetings",
        new_callable=AsyncMock,
    ):
        await clear_db()

        program, module, _ = await create_full_program()
        authorized_user = await create_authorized_user()
        (
            participant,
            solera_participant,
            participant_meta,
        ) = await create_participant()

        cohort = await create_cohort(
            program_id=program.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.YESTERDAY,
        )

        await add_participant_to_cohort(
            cohort_id=cohort.id, participant_id=participant.id
        )

        await create_participant_activity(participant.id)
        await create_participant_weight_activity(participant.id)

        return participant, module


@pytest.mark.asyncio
async def test_get_module_progress(happy_path):
    participant, module = await happy_path

    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        info: InfoType = Info(GraphQLResolveInfo, field)
        info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}

        result: ModuleProgressType = await get_module_progress(
            participant_id=participant.id,
            program_module_id=module.id,
            info=info,
        )

        assert result is not None
        assert result.completed is False
        assert result.program_weight_trend is TrendEnum.FLAT
        assert result.previous_module_weight_trend is TrendEnum.FLAT
        assert len(result.sections) > 0


@pytest.mark.asyncio
async def test_get_module_progress_no_progress(no_progress):
    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        info: InfoType = Info(GraphQLResolveInfo, field)
        info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}

        participant, module = await no_progress

        result: ModuleProgressType = await get_module_progress(
            participant_id=participant.id,
            program_module_id=module.id,
            info=info,
        )

        assert result is not None
        assert result.completed is False
        assert result.program_weight_trend is None
        assert result.previous_module_weight_trend is None
        assert len(result.sections) > 0
