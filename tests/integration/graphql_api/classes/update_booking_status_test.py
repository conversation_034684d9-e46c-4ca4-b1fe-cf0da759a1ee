import pytest
from unittest.mock import patch

from strawberry import Info
from strawberry.types.field import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ciba_participant.classes.models import BookingStatusEnum
from ciba_participant.test_utils import (
    create_authorized_user,
    create_participant,
    clear_db,
    create_webinar,
    create_live_session,
    create_booking,
)

from app.graphql_api.classes.mutations import update_booking_status

GRAPHQL_RESOLVER = "graphql.GraphQLResolveInfo"


@pytest.fixture(scope="function")
async def happy_path():
    await clear_db()

    authorized_user = await create_authorized_user()
    (
        participant,
        solera_participant,
        participant_meta,
    ) = await create_participant()

    webinar = await create_webinar(host_id=authorized_user.id)
    live_session = await create_live_session(webinar_id=webinar.id)

    return participant, webinar, live_session


@pytest.mark.asyncio
async def test_update_booking_status_booked(happy_path):
    participant, _, live_session = await happy_path

    with patch(GRAPHQL_RESOLVER) as GraphQLResolveInfo:
        field = StrawberryField()
        mock_info = Info(GraphQLResolveInfo, field)
        mock_info.context.user.sub = participant.id

    response = await update_booking_status(
        info=mock_info,
        live_session_id=live_session.id,
        status=BookingStatusEnum.BOOKED,
    )

    assert response.success is True
    assert response.error is None


@pytest.mark.asyncio
async def test_update_booking_status_attended(happy_path):
    participant, _, live_session = await happy_path

    await create_booking(
        live_session_id=live_session.id,
        participant_id=participant.id,
    )

    with patch(GRAPHQL_RESOLVER) as GraphQLResolveInfo:
        field = StrawberryField()
        mock_info = Info(GraphQLResolveInfo, field)
        mock_info.context.user.sub = participant.id

    response = await update_booking_status(
        info=mock_info,
        live_session_id=live_session.id,
        status=BookingStatusEnum.ATTENDED,
    )

    assert response.success is True
    assert response.error is None


@pytest.mark.asyncio
async def test_update_booking_status_canceled(happy_path):
    participant, _, live_session = await happy_path
    await create_booking(
        live_session_id=live_session.id,
        participant_id=participant.id,
    )

    with patch(GRAPHQL_RESOLVER) as GraphQLResolveInfo:
        field = StrawberryField()
        mock_info = Info(GraphQLResolveInfo, field)
        mock_info.context.user.sub = participant.id

    response = await update_booking_status(
        info=mock_info,
        live_session_id=live_session.id,
        status=BookingStatusEnum.CANCELED,
    )

    assert response.success is True
    assert response.error is None


@pytest.mark.asyncio
async def test_update_booking_status_not_found(happy_path):
    participant, _, live_session = await happy_path

    with patch(GRAPHQL_RESOLVER) as GraphQLResolveInfo:
        field = StrawberryField()
        mock_info = Info(GraphQLResolveInfo, field)
        mock_info.context.user.sub = participant.id

    response = await update_booking_status(
        info=mock_info,
        live_session_id=live_session.id,
        status=BookingStatusEnum.ATTENDED,
    )

    assert response.success is False
    assert response.error == "Booking not found"


@pytest.mark.asyncio
async def test_update_booking_status_already_made(happy_path):
    participant, _, live_session = await happy_path
    await create_booking(
        live_session_id=live_session.id,
        participant_id=participant.id,
    )

    with patch(GRAPHQL_RESOLVER) as GraphQLResolveInfo:
        field = StrawberryField()
        mock_info = Info(GraphQLResolveInfo, field)
        mock_info.context.user.sub = participant.id

    response = await update_booking_status(
        info=mock_info,
        live_session_id=live_session.id,
        status=BookingStatusEnum.BOOKED,
    )

    assert response.success is False
    assert response.error == "Booking was already made"
