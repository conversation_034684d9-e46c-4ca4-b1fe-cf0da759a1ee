from uuid import UUID

import pytest
from unittest.mock import patch

from strawberry.types import Info as InfoType
from strawberry import Info
from strawberry.types.field import <PERSON><PERSON><PERSON><PERSON>ield
from ciba_participant.content_library.models import ContentInteractions
from ciba_participant.test_utils import (
    clear_db,
    create_authorized_user,
    create_cohort,
    create_full_program,
    create_participant,
    create_content_material,
    add_participant_to_cohort,
    DateWhen,
)
from ciba_participant.helpers.timezone_conversion import TIMEZONE_HEADER

from app.graphql_api.content_library.mutations import (
    toggle_favorite_status,
)


@pytest.fixture(scope="function")
async def happy_path():
    await clear_db()

    program, _, _ = await create_full_program()
    authorized_user = await create_authorized_user()
    participant, _, _ = await create_participant()
    participant_future, _, _ = await create_participant()

    cohort = await create_cohort(
        program_id=program.id,
        created_by=authorized_user.id,
        cohort_date=DateWhen.NOW,
    )
    await add_participant_to_cohort(
        cohort_id=cohort.id, participant_id=participant.id
    )

    # Create a content material that will be used in the test
    content = await create_content_material(
        programs=[program.id],
        activity_types=[],  # Not necessary for favorite status testing
        user_id=authorized_user.id,
    )

    return (participant, participant_future, content)


@pytest.mark.asyncio
async def test_toggle_favorite_status(happy_path):
    participant, _, content = await happy_path

    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field = StrawberryField()
        mock_info: InfoType = Info(GraphQLResolveInfo, field)
        mock_info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}
        mock_info.context.user.sub = participant.id

        result = await toggle_favorite_status(
            info=mock_info,
            content_material_id=content.id,
        )
        assert result.success is True

        interaction = await ContentInteractions.filter(
            participant_id=participant.id, material_id=content.id
        ).first()

        assert interaction is not None
        assert interaction.is_completed is False
        assert interaction.is_favorite is True

        result = await toggle_favorite_status(
            info=mock_info,
            content_material_id=content.id,
        )
        assert result.success is True

        interaction = await ContentInteractions.filter(
            participant_id=participant.id, material_id=content.id
        ).first()
        assert interaction is not None
        assert interaction.is_favorite is False


@pytest.mark.asyncio
async def test_toggle_favorite_status_content_not_found(happy_path):
    participant, _, _ = await happy_path

    # Create a fake GraphQL info context with participant id and timezone header
    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field = StrawberryField()
        mock_info: InfoType = Info(GraphQLResolveInfo, field)
        mock_info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}
        mock_info.context.user.sub = participant.id

        # Provide a non-existent content material id
        non_existent_content_id = UUID("00000000-0000-0000-0000-000000000000")
        status = await toggle_favorite_status(
            info=mock_info,
            content_material_id=non_existent_content_id,
        )
        assert status.success is False
        assert status.error == "Content material not found"
