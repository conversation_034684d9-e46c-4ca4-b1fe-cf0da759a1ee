import pendulum
import pytest

from ciba_participant.classes.models import TopicEnum, BookingStatusEnum

from ciba_participant.test_utils import (
    DateWhen,
    add_participant_to_cohort,
    create_authorized_user,
    create_cohort,
    create_full_program,
    create_participant,
    clear_db,
    create_booking,
    create_webinar,
    create_live_session,
)


async def init_scenarios(cohort_start: DateWhen):
    authorized_user = await create_authorized_user()
    program_resp, _, _ = await create_full_program()

    cohort = await create_cohort(
        program_id=program_resp.id,
        created_by=authorized_user.id,
        cohort_date=cohort_start,
    )

    participant, _, _ = await create_participant()
    await add_participant_to_cohort(
        cohort_id=cohort.id, participant_id=participant.id
    )

    webinar = await create_webinar(
        host_id=authorized_user.id, topic=TopicEnum.INTRO_SESSION
    )

    meeting_start = pendulum.now().add(hours=2)

    if cohort_start == DateWhen.FUTURE:
        meeting_start = pendulum.instance(cohort.started_at).add(days=5)
    elif cohort_start == DateWhen.NOW:
        meeting_start = pendulum.now().add(hours=2)
    elif cohort_start == DateWhen.PAST:
        meeting_start = pendulum.instance(cohort.started_at).subtract(days=5)
    elif cohort_start == DateWhen.YESTERDAY:
        meeting_start = pendulum.instance(cohort.started_at).subtract(days=1)

    live_session = await create_live_session(
        webinar_id=webinar.id, meeting_start_time=meeting_start
    )

    return participant, webinar, live_session, authorized_user, cohort


@pytest.fixture
async def scenario_1():
    """
    scenario 1: user is enrolled and has an intro session booking
    """
    await clear_db()

    participant, webinar, live_session, _, _ = await init_scenarios(
        DateWhen.NOW
    )

    booking = await create_booking(participant.id, live_session.id)

    data = {
        "participant": participant,
        "webinar": webinar,
        "live_session": live_session,
        "booking": booking,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_2():
    """
    scenario 2: user is enrolled and does not have an intro session booking
    """
    await clear_db()

    participant, webinar, live_session, _, _ = await init_scenarios(
        DateWhen.FUTURE
    )

    data = {
        "participant": participant,
        "webinar": webinar,
        "live_session": live_session,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_3():
    """
    scenario 3: user was enrolled in the past, should see non-intro sessions
    """
    await clear_db()

    participant, intro_webinar, _, authorized_user, _ = await init_scenarios(
        DateWhen.PAST
    )

    non_intro_webinar = await create_webinar(
        authorized_user.id, TopicEnum.MENTAL_HEALTH
    )

    legacy_live_session = await create_live_session(
        intro_webinar.id, pendulum.now().subtract(months=5)
    )
    non_intro_live_session = await create_live_session(
        non_intro_webinar.id, pendulum.now().add(days=1)
    )

    booking = await create_booking(
        participant.id, legacy_live_session.id, BookingStatusEnum.ATTENDED
    )

    data = {
        "participant": participant,
        "webinar": non_intro_webinar,
        "live_session": non_intro_live_session,
        "booking": booking,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_4():
    """
    scenario 4: user was enrolled correctly, but next session is two
    months away, so it is not shown.
    """
    await clear_db()

    participant, intro_webinar, _, authorized_user, _ = await init_scenarios(
        DateWhen.PAST
    )

    non_intro_webinar = await create_webinar(
        authorized_user.id, TopicEnum.MENTAL_HEALTH
    )

    legacy_live_session = await create_live_session(
        intro_webinar.id, pendulum.now().subtract(months=5)
    )
    non_intro_live_session = await create_live_session(
        non_intro_webinar.id, pendulum.now().add(months=2)
    )

    booking = await create_booking(
        participant.id, legacy_live_session.id, BookingStatusEnum.ATTENDED
    )

    data = {
        "participant": participant,
        "webinar": non_intro_webinar,
        "live_session": non_intro_live_session,
        "booking": booking,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_5():
    """
    scenario 5: user has no bookings but there are 2 live sessions
    in the 7-day range, and one afterwards. Should show just the 2
    first ones.
    """
    await clear_db()

    participant, webinar, live_session_1, _, _ = await init_scenarios(
        DateWhen.NOW
    )

    live_session_2 = await create_live_session(
        webinar.id, pendulum.now().add(days=6)
    )

    # Anther live session, should not be returned
    await create_live_session(webinar.id, pendulum.now().add(days=8))

    data = {
        "participant": participant,
        "webinar": webinar,
        "live_session_1": live_session_1,
        "live_session_2": live_session_2,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_6():
    """
    scenario 6: user is enrolled and has an intro session booking
    """
    await clear_db()

    participant, webinar, live_session_1, _, cohort = await init_scenarios(
        DateWhen.FUTURE
    )

    max_days = (
        pendulum.instance(cohort.started_at).diff(cohort.started_at).in_days()
    )

    live_session_2 = await create_live_session(
        webinar.id,
        pendulum.instance(cohort.started_at).add(days=min(10, max_days)),
    )
    live_session_3 = await create_live_session(
        webinar.id,
        pendulum.instance(cohort.started_at).add(days=min(23, max_days)),
    )

    data = {
        "participant": participant,
        "webinar": webinar,
        "live_session_1": live_session_1,
        "live_session_2": live_session_2,
        "live_session_3": live_session_3,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_7():
    """
    scenario 7: user has multiple bookings
    """
    await clear_db()

    (
        participant,
        webinar,
        live_session,
        authorized_user,
        _,
    ) = await init_scenarios(DateWhen.PAST)

    booking_1 = await create_booking(
        participant.id, live_session.id, BookingStatusEnum.WATCHED_RECORDING
    )

    webinar_2 = await create_webinar(
        authorized_user.id, TopicEnum.MENTAL_HEALTH
    )

    live_session_2 = await create_live_session(
        webinar_2.id, pendulum.now().add(days=5)
    )
    booking_2 = await create_booking(participant.id, live_session_2.id)

    data = {
        "participant": participant,
        "webinar_1": webinar,
        "webinar_2": webinar_2,
        "live_session_1": live_session,
        "live_session_2": live_session_2,
        "booking_1": booking_1,
        "booking_2": booking_2,
    }

    yield data

    await clear_db()


@pytest.fixture
async def scenario_8():
    """
    scenario 8: user is not shown intro sessions before cohort start
    """
    await clear_db()

    participant, webinar, live_session_1, _, cohort = await init_scenarios(
        DateWhen.FUTURE
    )

    await create_booking(
        participant.id, live_session_1.id, BookingStatusEnum.CANCELED
    )

    live_session_2 = await create_live_session(
        webinar.id,
        pendulum.instance(cohort.started_at).subtract(hours=1),
    )
    live_session_3 = await create_live_session(
        webinar.id,
        pendulum.instance(cohort.started_at).subtract(days=1),
    )
    live_session_4 = await create_live_session(
        webinar.id,
        pendulum.instance(cohort.started_at).add(days=2),
    )

    data = {
        "participant": participant,
        "webinar": webinar,
        "live_session_1": live_session_1,
        "live_session_2": live_session_2,
        "live_session_3": live_session_3,
        "live_session_4": live_session_4,
    }

    yield data

    await clear_db()
