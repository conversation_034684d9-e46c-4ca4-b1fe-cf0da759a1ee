import pytest
from unittest.mock import AsyncMock, patch

import pendulum
from strawberry.types import Info as InfoType
from strawberry import Info
from strawberry.types.field import <PERSON><PERSON><PERSON><PERSON>ield

from app.graphql_api.content_library.mutations import (
    record_content_interaction,
)
from app.graphql_api.types import SimpleResponseType
from ciba_participant.content_library.models import ContentInteractions
from ciba_participant.test_utils import (
    DateWhen,
    add_participant_to_cohort,
    create_authorized_user,
    create_cohort,
    create_full_program,
    create_participant,
    clear_db,
    create_content_material,
)
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
)

from ciba_participant.helpers.timezone_conversion import TIMEZONE_HEADER


@pytest.fixture(scope="function")
async def happy_path():
    with patch(
        "ciba_participant.schedule_manager.service.ScheduleManager.create_zoom_meetings",
        new_callable=AsyncMock,
    ):
        await clear_db()

        program, _, _ = await create_full_program()
        authorized_user = await create_authorized_user()
        (
            participant,
            solera_participant,
            participant_meta,
        ) = await create_participant()

        cohort = await create_cohort(
            program_id=program.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.NOW,
        )

        await add_participant_to_cohort(
            cohort_id=cohort.id, participant_id=participant.id
        )

        participant_future, _, _ = await create_participant()

        future_cohort = await create_cohort(
            program_id=program.id,
            created_by=authorized_user.id,
            cohort_date=DateWhen.FUTURE,
        )

        await add_participant_to_cohort(
            cohort_id=future_cohort.id, participant_id=participant_future.id
        )

        content = await create_content_material(
            programs=[program.id],
            activity_types=[
                ParticipantActivityEnum.QUIZ,
                ParticipantActivityEnum.RECIPES,
            ],
            user_id=authorized_user.id,
        )

        return (participant, participant_future, future_cohort, content)


@pytest.mark.asyncio
async def test_record_content_interaction(happy_path):
    (
        participant,
        participant_future,
        future_cohort,
        content,
    ) = await happy_path

    with patch("graphql.GraphQLResolveInfo") as GraphQLResolveInfo:
        field: StrawberryField = StrawberryField()
        mock_info: InfoType = Info(GraphQLResolveInfo, field)
        mock_info.context.request.headers = {TIMEZONE_HEADER: "+02:00"}
        mock_info.context.user.sub = participant.id

        # Test content interaction
        status: SimpleResponseType = await record_content_interaction(
            info=mock_info,
            content_material_id=content.id,
        )

        activities_count = await ParticipantActivity.filter(
            participant_id=participant.id
        ).count()

        interaction = await ContentInteractions.filter(
            participant_id=participant.id, material_id=content.id
        ).first()

        assert status.success
        assert interaction is not None
        assert activities_count == 2
        assert interaction.is_completed
        assert interaction.interactions == 1

        # Test content interaction when activity exists
        status: SimpleResponseType = await record_content_interaction(
            info=mock_info,
            content_material_id=content.id,
        )

        activities_count = await ParticipantActivity.filter(
            participant_id=participant.id
        ).count()

        interaction = await ContentInteractions.filter(
            participant_id=participant.id, material_id=content.id
        ).first()

        assert status.success
        assert interaction is not None
        assert activities_count == 4
        assert interaction.is_completed
        assert interaction.interactions == 2

        # Test content interaction for a cohort in future
        mock_info.context.user.sub = participant_future.id

        status: SimpleResponseType = await record_content_interaction(
            info=mock_info,
            content_material_id=content.id,
        )

        activity = await ParticipantActivity.filter(
            participant_id=participant_future.id
        ).first()

        assert status.success
        assert activity is not None
        assert (
            activity.created_at.date()
            == future_cohort.started_at.date() + pendulum.duration(days=1)
        )
