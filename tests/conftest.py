import pytest
from tortoise import Tortoise

MODELS = [
    "ciba_participant.participant.models",
    "ciba_participant.program.models",
    "ciba_participant.cohort.models",
    "ciba_participant.activity.models",
    "ciba_participant.classes.models",
    "ciba_participant.content_library.models",
]


@pytest.fixture(scope="function", autouse=True)
async def init_db():
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": MODELS},
    )
    await Tortoise.generate_schemas()

    yield  # Run the test

    # Clean up
    await Tortoise._drop_databases()
    await Tortoise.close_connections()
