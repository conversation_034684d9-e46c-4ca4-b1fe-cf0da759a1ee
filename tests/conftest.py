# pylint: skip-file
import asyncio
import uuid
from contextlib import contextmanager
import respx
import pytest

from httpx import Response

from ciba_participant.db import get_tortoise_orm_config

from app.settings import get_settings

settings = get_settings()
TORTOISE_ORM = get_tortoise_orm_config(settings.default_db_url)


# Configure pytest-asyncio to use session scope for event loops
pytest_asyncio_scope = "session"


@pytest.fixture(scope="session")
def event_loop_policy():
    return asyncio.get_event_loop_policy()


@contextmanager
def mock_solera_endpoints():
    with respx.mock:
        respx.post("http://test").mock(
            return_value=Response(
                200, json={"access_token": "mocked_token", "expires_in": 3600}
            )
        )
        respx.get("http://test/123456789").mock(
            return_value=Response(
                200,
                json={
                    "email": "<EMAIL>",
                    "programId": "NDPP",
                    "given_name": "<PERSON>",
                    "family_name": "Doe",
                    "patientId": str(uuid.uuid4()),
                    "enrollmentId": str(uuid.uuid4()),
                },
            )
        )
        yield
