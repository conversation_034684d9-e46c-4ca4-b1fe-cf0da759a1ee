{"name": "FastAPI DevContainer", "dockerFile": "Dockerfile", "context": "..", "appPort": [8000, 22, 5678, 3005], "runArgs": ["--mount=type=ssh"], "settings": {"terminal.integrated.shell.linux": "/bin/bash"}, "extensions": ["ms-python.python", "ms-azuretools.vscode-docker", "ms-vscode-remote.remote-containers"], "postCreateCommand": "poetry install --no-root --only main", "remoteEnv": {"PYTHONDONTWRITEBYTECODE": "1", "PYTHONUNBUFFERED": "1", "TESTING": "0", "POETRY_HOME": "/opt/poetry", "PATH": "/opt/poetry/bin:${PATH}"}, "mounts": ["source=/home/<USER>/.ssh,target=/root/.ssh,type=bind,consistency=cached"], "remoteUser": "root", "features": {"ghcr.io/devcontainers/features/python:1": {"version": "3.12"}}}