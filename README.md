## Participant Api

### Env setup

### MacOS

#### Python

follow [this](https://medium.com/marvelous-mlops/the-rightway-to-install-python-on-a-mac-f3146d9d9a32) guide

#### UV

follow [this](https://docs.astral.sh/uv/)

#### Docker

follow [this](https://docs.docker.com/desktop/install/mac-install/)

#### Setup your github ssh-key

follow [this](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent)

### Running locally

#### Build participant-api dev image

```shell
make build
```

#### Run docker-compose

```shell
make up
```

#### Run terminal

```shell
make term
```

P.S works on OS X

P.P.S you have to have your github ssh-key added to be able to install packages from private repositories and share these keys with docker containers

#### Debug app in built image

##### VSCode Web

http://localhost:3005

Access password is available in [entrypoint.sh](entrypoint.sh)

##### JetBrains Gateway

https://www.jetbrains.com/remote-development/gateway/

##### Unit Tests

Run unit tests with command:

```shell
uv run pytest
```
