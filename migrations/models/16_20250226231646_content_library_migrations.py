from tortoise import BaseDBAsync<PERSON><PERSON>


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "booking" ALTER COLUMN "status" TYPE VARCHAR(17) USING "status"::VARCHAR(17);
        COMMENT ON COLUMN "booking"."status" IS 'BOOKED: booked
ATTENDED: attended
WATCHED_RECORDING: watched_recording
CANCELED: canceled';
        CREATE TABLE IF NOT EXISTS "content_material" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "created_by" UUID,
    "updated_by" UUID,
    "id" UUID NOT NULL  PRIMARY KEY,
    "is_healthy" BOOL   DEFAULT True,
    "title" VARCHAR(255) NOT NULL,
    "description" VARCHAR(500) NOT NULL,
    "content_url" VARCHAR(255),
    "mime_type" VARCHAR(255) NOT NULL,
    "form_id" VARCHAR(50),
    "file_size" INT NOT NULL  DEFAULT 0,
    "file_location" VARCHAR(255),
    "file_name" VARCHAR(255),
    "status" SMALLINT NOT NULL  DEFAULT 1
);
COMMENT ON COLUMN "content_material"."status" IS 'ACTIVE: 1\nDELETED: 2\nARCHIVED: 3';
        CREATE TABLE IF NOT EXISTS "content_activity_types" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "activity_type" VARCHAR(100) NOT NULL,
    "material_id" UUID NOT NULL REFERENCES "content_material" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "content_activity_types"."activity_type" IS 'ENROLL: enrollment_type\nWEIGHT: weight_type\nPLAY: video_type\nCOACH: chat_type\nRECIPES: recipe_type\nQUIZ: personal_success\nACTIVITY: activity_type\nARTICLE: curriculum\nGROUP: coaching_call';
        CREATE TABLE IF NOT EXISTS "content_tags" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "tag" VARCHAR(100) NOT NULL,
    "material_id" UUID NOT NULL REFERENCES "content_material" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "content_tags"."tag" IS 'ACTIVITY: activity\nGUT_HEALTH: gut_health\nHEALTHY_EATING: healthy_eating\nMINDSETS: mindsets\nMOTIVATION: motivation\nRECIPES: recipes\nSLEEP: sleep\nSTRESS: stress\nSUPPORT: support';
        CREATE TABLE IF NOT EXISTS "favorite_content" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "material_id" UUID NOT NULL REFERENCES "content_material" ("id") ON DELETE CASCADE,
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_favorite_co_partici_88e0ee" UNIQUE ("participant_id", "material_id")
);
        CREATE TABLE IF NOT EXISTS "program_content" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "material_id" UUID NOT NULL REFERENCES "content_material" ("id") ON DELETE CASCADE,
    "program_id" UUID NOT NULL REFERENCES "program" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_program_con_program_243b53" UNIQUE ("program_id", "material_id")
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "booking"."status" IS 'BOOKED: booked
ATTENDED: attended
CANCELED: canceled';
        ALTER TABLE "booking" ALTER COLUMN "status" TYPE VARCHAR(8) USING "status"::VARCHAR(8);
        DROP TABLE IF EXISTS "favorite_content";
        DROP TABLE IF EXISTS "content_activity_types";
        DROP TABLE IF EXISTS "content_material";
        DROP TABLE IF EXISTS "program_content";
        DROP TABLE IF EXISTS "content_tags";"""
