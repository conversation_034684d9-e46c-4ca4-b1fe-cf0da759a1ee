from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "cohort_members" ALTER COLUMN "status" TYPE VARCHAR(7) USING "status"::VARCHAR(7);
        COMMENT ON COLUMN "cohort_members"."status" IS 'ACTIVE: active
DELETED: deleted';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "cohort_members"."status" IS 'ACTIVE: active
DISENROLLED: disenrolled';
        ALTER TABLE "cohort_members" ALTER COLUMN "status" TYPE VARCHAR(11) USING "status"::VARCHAR(11);"""
