from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "content_interactions" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "interactions" INT NOT NULL  DEFAULT 0,
    "is_completed" BOOL NOT NULL  DEFAULT False,
    "is_favorite" BOOL NOT NULL  DEFAULT False,
    "material_id" UUID NOT NULL REFERENCES "content_material" ("id") ON DELETE CASCADE,
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_content_int_partici_39e138" UNIQUE ("participant_id", "material_id")
);
COMMENT ON TABLE "content_interactions" IS 'Model to track content interactions.';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "content_interactions";"""
