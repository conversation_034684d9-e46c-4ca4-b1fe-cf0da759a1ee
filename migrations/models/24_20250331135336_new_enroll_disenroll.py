from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "solera_participants" ADD "status" VARCHAR(8) NOT NULL  DEFAULT 'active';
        ALTER TABLE "cohort_members" ADD "status" VARCHAR(8) NOT NULL  DEFAULT 'active';
        """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "solera_participants" DROP COLUMN "status";
        ALTER TABLE "cohort_members" DROP COLUMN "status";
        """
