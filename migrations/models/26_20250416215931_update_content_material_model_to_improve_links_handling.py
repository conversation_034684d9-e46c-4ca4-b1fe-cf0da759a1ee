from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "content_material" ADD "file_url_expiration" TIMESTAMPTZ;
        ALTER TABLE "content_material" ADD "file_url" VARCHAR(2000);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "content_material" DROP COLUMN "file_url_expiration";
        ALTER TABLE "content_material" DROP COLUMN "file_url";"""
