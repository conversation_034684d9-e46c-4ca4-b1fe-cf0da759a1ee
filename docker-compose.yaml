version: '3'
services:
  app:
    image: participant-api:dev-latest
    container_name: ciba-participant-api
    restart: unless-stopped
    tty: true
    environment:
      ENV: local
    ports:
      - "8001:8000"
      - "2223:22"
      - "5679:5678"
      - "3006:3005"
    entrypoint: /app/entrypoint.sh
    volumes:
      - .:/app/
    networks:
        - participant-network

volumes:
  participant_db:
    driver: local
networks:
  participant-network:
    external: true
