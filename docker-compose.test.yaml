version: '3'
services:
  app:
    image: ${IMAGE_PATH}
    container_name: ciba_participant_api
    restart: unless-stopped
    tty: true
    environment:
      POSTGRES_HOST: db
      POSTGRES_DB: participant_api
      POSTGRES_USER: participant_api
      POSTGRES_PASSWORD: secret
      ENV: local
    ports:
      - "8001:8000"
    depends_on:
      - db
    volumes:
      - .:/app/
  db:
    image: postgres:14
    container_name: participant_api_db
    restart: unless-stopped
    tty: true
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: participant_api
      POSTGRES_USER: participant_api
      POSTGRES_PASSWORD: secret
    volumes:
      - participant_db:/var/lib/postgresql
volumes:
  participant_db:
    driver: local
