# Instruction Manual for Program Data Management CLI

This CLI tool is used to **backup** and **restore** program data, including their associated modules and sections, in a JSON file. It supports environments from settings.py `ENV` enum and offers an option to preserve old IDs during backups and restores.

## Installation and Setup

Make sure the database credentials are correctly configured:

- For `local` or `test` environments, the credentials are read from `settings.py`.
- For `prod` or similar environments, the credentials are fetched from the **AWS SSM Parameter Store**. Ensure AWS credentials are set up and accessible.

## CLI Commands

### Backup Program Data

The `backup` command extracts program data, including their modules and sections, from the database and writes it to JSON file.

#### Usage

```bash
poetry run python backup_restore_programs.py backup --env <ENV> [--with-old-id]
```

#### Options:

- `--env`: **(Required)** The environment to use, e.g., `prod`, `dev`, or `local`.
- `--with-old-id`: **(Optional)** Include old IDs (UUIDs) in the backup file.

#### Example:

1. Backup data for the `prod` environment with old IDs:

   ```bash
   poetry run python backup_restore_programs.py backup --env prod --with-old-id
   ```

1. Backup data for the `dev` environment without IDs:

   ```bash
   poetry run python backup_restore_programs.py backup --env dev
   ```

#### Output:

- A backup JSON file is created in the `backup/` directory, named as:
  ```
  <ENV>-programs-backup-<timestamp>.json
  ```

______________________________________________________________________

### Restore Program Data

The `restore` command reads program data from a JSON file and writes it back to the database.

#### Usage

```bash
poetry run python backup_restore_programs.py restore --env <ENV> --location <FILE_PATH>
```

#### Options:

- `--env`: **(Required)** The environment to use, e.g., `prod`, `dev`, or `local`.
- `--location`: **(Required)** The path to the JSON file containing the backup data.

#### Example:

1. Restore data to the `prod` environment:

   ```bash
   poetry run python backup_restore_programs.py restore --env prod --location ./backup/prod-programs-backup-2024-11-29T15:23:00Z.json
   ```

1. Restore data to the `local` environment:

   ```bash
   poetry run python backup_restore_programs.py restore --env local --location ./backup/local-programs-backup-2024-11-29T15:23:00Z.json
   ```

#### Notes:

- If the JSON file contains IDs and the database supports UUIDs, the restored data will preserve the original IDs.
- Ensure the input file exists and is accessible.

______________________________________________________________________

## Advanced Notes

- **Transactional Safety**: The restore operation is wrapped in a database transaction. If any part of the process fails, all changes are rolled back.
- **Metadata Inclusion**: The `--with-old-id` flag ensures program IDs, module IDs, and section IDs are included in the backup for accurate restoration.
- **Time-Based File Naming**: Backup files are timestamped to avoid overwriting previous backups.

______________________________________________________________________

## Troubleshooting

1. **AWS SSM Credentials Issue**:

   - Ensure your AWS credentials are configured correctly .
   - Verify that your environment has sufficient permissions to fetch SSM parameters.

1. **Database Connection Errors**:

   - Verify that the database is accessible from your environment.
   - Ensure VPN is turned on if connecting to a remote database.
   - Double-check the database credentials in `settings.py` or AWS SSM.

1. **File Not Found During Restore**:

   - Ensure the file path provided in the `--location` option is correct and accessible.

______________________________________________________________________

## Examples

### Backup Data

```bash
poetry run python backup_restore_programs.py backup --env dev --with-old-id
```

### Restore Data

```bash
poetry run python backup_restore_programs.py restore --env dev --location ./backup/dev-programs-backup-2024-11-29T15:23:00Z.json
```
