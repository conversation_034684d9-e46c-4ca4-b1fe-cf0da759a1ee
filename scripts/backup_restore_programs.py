import asyncio
import json
from pathlib import Path
from uuid import uuid4

import click
from pendulum import now
import tortoise.transactions

from ciba_participant import get_settings
from ciba_participant.program.models import Program, ProgramModule, ProgramModuleSection
from ciba_participant.common.db import close_db, init_db


async def fetch_and_write_data_to_json(output_file: str, with_old_id: bool):
    """Pull data about Program, Program Modules and Program Module Sections
    from the database and write it to a JSON file."""
    await init_db()

    try:
        programs = await Program.all().prefetch_related("modules__sections")

        # Prepare data for serialization
        result = []
        for program in programs:
            program_data = {"title": program.title, "modules": []}
            if with_old_id:
                program_data["id"] = str(program.id)

            for module in program.modules:
                module_data = {
                    "title": module.title,
                    "short_title": module.short_title,
                    "length": module.length,
                    "description": module.description,
                    "order": module.order,
                    "sections": [],
                }
                if with_old_id:
                    module_data["id"] = str(module.id)

                for section in module.sections:
                    section_data = {
                        "title": section.title,
                        "description": section.description,
                        "activity_type": section.activity_type.value,
                        "activity_category": section.activity_category.value,
                        "metadata": section.metadata,
                    }
                    if with_old_id:
                        section_data["id"] = str(section.id)

                    module_data["sections"].append(section_data)
                program_data["modules"].append(module_data)
            result.append(program_data)

        # Write the data to a JSON file
        with open(output_file, "w") as json_file:
            json.dump(result, json_file, indent=4)

        print(f"Data successfully written to {output_file}")
    finally:
        # Close the database connection
        await close_db()


async def write_data_from_json_to_db(input_file: str):
    """Read data from a JSON file and write it to the database."""
    # Initialize the Tortoise ORM
    await init_db()

    try:
        # Read data from JSON file
        with open(input_file, "r") as json_file:
            data = json.load(json_file)

        # Process each program in the JSON file
        async with tortoise.transactions.in_transaction():
            for program_data in data:
                program, _ = await Program.get_or_create(
                    id=program_data.get("id", uuid4()), title=program_data["title"]
                )
                program_modules = program_data.get("modules", [])

                for module_data in program_modules:
                    module, _ = await ProgramModule.get_or_create(
                        id=module_data.get("id", uuid4()),
                        program_id=program.id,
                        title=module_data["title"],
                        short_title=module_data["short_title"],
                        length=module_data["length"],
                        description=module_data["description"],
                        order=module_data["order"],
                    )
                    module_sections = module_data.get("sections", [])

                    for section_data in module_sections:
                        await ProgramModuleSection.get_or_create(
                            id=section_data.get("id", uuid4()),
                            program_module_id=module.id,
                            title=section_data["title"],
                            description=section_data["description"],
                            activity_category=section_data["activity_category"],
                            activity_type=section_data["activity_type"],
                            metadata=section_data["metadata"],
                        )

        print(f"Data from {input_file} successfully written to the database")
    finally:
        # Close the database connection
        await close_db()


@click.group()
def cli():
    """A CLI tool for managing program data."""
    pass


@cli.command()
@click.option("--env", required=True, help="Environment (prod, dev, local).")
@click.option("--with-old-id", is_flag=True, help="Include old IDs in the backup.")
def backup(env, with_old_id):
    """Backup program data to a JSON file.
    If with-meta is true, IDs will be included in the backup.
    If you use local/test env, database credentials will be pulled from settings.py,
    otherwise from ssm parameter store, so make AWS credentials available."""
    settings = get_settings()
    settings.ENV = env

    backup_folder = Path("backup")
    backup_folder.mkdir(exist_ok=True, parents=True)
    output_file = (
        backup_folder
        / f"{settings.ENV}-programs-backup-{now('UTC').to_iso8601_string()}.json"
    )

    asyncio.run(fetch_and_write_data_to_json(str(output_file), with_old_id))


@cli.command()
@click.option("--env", required=True, help="Environment (e.g. prod, dev, local).")
@click.option("--location", required=True, help="Path to the input JSON file.")
def restore(env, location):
    """Restore program data from a JSON file.
    If with-old-id flag was used during backup, old IDs will be included in db restore.
    If you use local/test env, database credentials will be pulled from settings.py,
    otherwise from ssm parameter store, so make AWS credentials available."""
    settings = get_settings()
    settings.ENV = env

    input_file = Path(location)
    if not input_file.exists():
        print(f"Input file {location} not found.")
        return

    asyncio.run(write_data_from_json_to_db(str(input_file)))


if __name__ == "__main__":
    cli()
