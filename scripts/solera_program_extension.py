import asyncio
import click
import pendulum
import tortoise
import tortoise.transactions
from uuid import UUID
from loguru import logger

from ciba_participant.cohort.models import <PERSON>hor<PERSON>, CohortProgramModules
from ciba_participant.common.db import close_db, custom_init_db
from ciba_participant.program.models import Program, ProgramModule
from ciba_participant.settings import ENV, get_settings


async def sync_cohort_modules(cohort: Cohort, modules_to_add: list[ProgramModule]):
    logger.info("Updating cohort...")

    # sort cohort modules, sometimes it arrives unsorted
    modules_to_add.sort(key=lambda x: x.order)
    cohort_modules = list(cohort.program_modules)

    started_at = pendulum.instance(cohort_modules[-1].ended_at)

    updated = False

    async with tortoise.transactions.in_transaction():
        for module in modules_to_add:
            logger.info(f"Adding module: {module.title}")
            ended_at = started_at.add(days=module.length)
            new_cohort_module = await CohortProgramModules.create(
                cohort=cohort,
                program_module=module,
                started_at=started_at,
                ended_at=ended_at,
            )
            await new_cohort_module.save()
            started_at = ended_at
        updated = True

    return updated


async def extend_program(
    program_id: UUID | None = None,
    program_name: str | None = None,
    dry_run=True,
    env: str = "local",
):
    logger.info(f"Fetching settings for {env} environment...")
    settings = get_settings(ENV=env)

    if program_id is None and program_name is None:
        print("Please provide a program name or UUID.")
        return

    if dry_run is True:
        logger.info("Running in dry-run mode")
    else:
        logger.warning("Running in dangerous mode")

    await custom_init_db(settings)
    logger.info("DB initialized")

    try:
        if program_id is not None:
            program = await Program.get_or_none(id=program_id).prefetch_related(
                "modules__sections",
                "cohorts__program_modules__program_module",
            )
        else:
            program = await Program.get_or_none(title=program_name).prefetch_related(
                "modules__sections",
                "cohorts__program_modules__program_module",
            )

        if program is None:
            logger.error(
                f"No program found with ID {program_id} and title {program_name}"
            )
            return

        cohorts = list(program.cohorts)
        modules = list(program.modules)

        if len(modules) == 0:
            logger.error("Program has no available modules")
            return

        if len(cohorts) == 0:
            logger.error("Program has no cohorts assigned")
            return

        print(f"Program: {program.title}")
        print(f"Found {len(modules)} modules")
        print(f"Found {len(cohorts)} cohorts")

        updates_needed = False
        is_error = False

        for cohort in cohorts:
            cohort_modules = [d.program_module for d in list(cohort.program_modules)]
            modules_to_add = []
            if len(modules) != len(cohort_modules):
                print(
                    f"Found discrepancy: {len(cohort_modules)} cohort modules vs {len(modules)} program modules"
                )

            # sort cohort modules, sometimes it arrives unsorted
            cohort_modules.sort(key=lambda x: x.order)

            for module in modules:
                try:
                    list(filter(lambda x: x.id == module.id, cohort_modules))[0]
                except IndexError:
                    logger.warning(f"Module not found: {module.title}")
                    updates_needed = True
                    modules_to_add.append(module)

            if len(modules_to_add) > 0:
                if dry_run is True:
                    logger.info(f"Cohort '{cohort.name}' needs to be updated")
                else:
                    updated = await sync_cohort_modules(cohort, modules_to_add)
                    if updated is False:
                        is_error = True
                        logger.error("Modules not updated")
        if updates_needed is False:
            logger.success("No changes to make")
        else:
            if is_error is True:
                logger.error("Failed to update")
            else:
                logger.success("Cohort modules updated")

    except ConnectionRefusedError:
        logger.error(f"Error: Could not connect to {settings.ENV} DB")
    except Exception as e:
        logger.error(f"Unhandled exception: {type(e)}")
        logger.error(e)
    finally:
        await close_db()


@click.group()
def cli(): ...


@cli.command()
@click.option(
    "-n",
    "--name",
    type=click.STRING,
    help="Name of the program to be extended",
)
@click.option("-i", "--id", type=click.UUID, help="UUID of the program to be extended")
@click.option(
    "--commit",
    is_flag=True,
    help="Commit changes to DB. USE WITH CAUTION!",
)
@click.option(
    "-e",
    "--environment",
    type=str,
    default="local",
    help="Environment to use. Default: local",
)
def extend(id: UUID, name: str, commit: bool = False, environment: str = "local"):
    if environment == ENV.PROD:
        MESSAGE = (
            "Running script on PROD environment. Do you want to continue? (yes/no)\n"
        )
        confirmation = input(MESSAGE)
        while confirmation != "yes":
            if confirmation == "no":
                print("Cancelled")
                return
            confirmation = input(MESSAGE)
    asyncio.run(
        extend_program(
            program_id=id, program_name=name, dry_run=not commit, env=environment
        )
    )


if __name__ == "__main__":
    cli()
