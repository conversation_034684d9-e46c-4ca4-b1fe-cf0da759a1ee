import asyncio
from random import sample, choice, randint
from uuid import UUID
import click
import pendulum
from loguru import logger
from typing import List, Dict, Optional
import os
import pandas as pd

from ciba_participant.common.db import close_db, custom_init_db
from ciba_participant.classes.models import (
    Webinar,
    LiveSession,
    Booking,
    TopicEnum,
    RecurrenceEnum,
    MeetingTypeEnum,
    BookingStatusEnum,
)
from ciba_participant.participant.models import (
    Authorized,
    Participant,
)
from ciba_participant.settings import ENV, get_settings

SAMPLE_WEBINARS = [
    {
        "title": "Mindful Eating in a Digital Age",
        "topic": TopicEnum.FOOD,
        "description": "Join renowned nutritionist Dr. <PERSON> for an interactive 90-minute session exploring the intersection of modern technology and mindful eating practices. Learn evidence-based strategies to maintain healthy eating habits while navigating a world of food delivery apps and social media influence. Includes practical exercises and a Q&A session.",
    },
    {
        "title": "Sleep Optimization: Beyond the Basics",
        "topic": TopicEnum.HEALTH_AND_WELLNESS,
        "description": "Dr. <PERSON>, sleep specialist and author of 'The Sleep Revolution,' presents an in-depth workshop on maximizing sleep quality using cutting-edge research. Topics include circadian rhythm optimization, bedroom environment design, and natural sleep enhancement techniques. Perfect for busy professionals seeking to improve their sleep habits.",
    },
    {
        "title": "Functional Movement for Desk Warriors",
        "topic": TopicEnum.ACTIVITY,
        "description": "Expert physical therapist Maria Rodriguez leads this practical workshop designed for remote workers and office professionals. Learn desk-friendly exercises, proper ergonomic setups, and daily movement routines to prevent chronic pain and maintain mobility. Includes downloadable exercise guides and posture checklists.",
    },
    {
        "title": "Stress Management in the Modern World",
        "topic": TopicEnum.MENTAL_HEALTH,
        "description": "Join mental health expert Dr. Robert Thompson for this comprehensive three-part webinar series exploring innovative stress management techniques. Sessions cover breathing exercises, digital boundaries, and practical mindfulness strategies for today's fast-paced lifestyle. Includes guided meditation recordings and stress-tracking tools.",
    },
    {
        "title": "Gut Health Revolution: Understanding Your Microbiome",
        "topic": TopicEnum.EDUCATIONAL,
        "description": "Gastroenterologist Dr. Emily Watson presents the latest research on gut health and its connection to overall wellness. Learn about probiotic benefits, food combinations for optimal digestion, and natural ways to support your gut microbiome. Includes meal planning templates and recipe suggestions.",
    },
]

# Default descriptions to use if CSV doesn't provide one
DEFAULT_DESCRIPTIONS = {
    TopicEnum.FOOD: "Join our expert nutritionist for an interactive session exploring healthy eating strategies and practical food knowledge for better wellness.",
    TopicEnum.HEALTH_AND_WELLNESS: "Discover evidence-based approaches to improve your overall health and wellness in this engaging and informative session.",
    TopicEnum.ACTIVITY: "Learn effective physical activity techniques and routines designed to enhance your fitness and daily mobility.",
    TopicEnum.MENTAL_HEALTH: "Explore practical mental health strategies and techniques to manage stress and improve your emotional wellbeing.",
    TopicEnum.EDUCATIONAL: "Gain valuable knowledge and insights on important health topics in this educational and interactive session.",
}

BOOKING_STATUSES = [
    BookingStatusEnum.BOOKED,
    BookingStatusEnum.ATTENDED,
    BookingStatusEnum.CANCELED,
]


def parse_csv_with_pandas(csv_path: str) -> List[Dict]:
    """Parse CSV file using pandas and return a list of webinar data dictionaries."""
    if not os.path.exists(csv_path):
        logger.error(f"CSV file not found: {csv_path}")
        return []

    webinars = []
    try:
        df = pd.read_csv(csv_path)

        # Drop rows where Topic is NaN or empty
        df = df.dropna(subset=["Topic"]).reset_index(drop=True)
        df = df[df["Topic"].str.strip() != ""]

        topic_mapping = {
            "Food": TopicEnum.FOOD,
            "Health and Wellness": TopicEnum.HEALTH_AND_WELLNESS,
            "Activity": TopicEnum.ACTIVITY,
            "Mental Health": TopicEnum.MENTAL_HEALTH,
            "Educational": TopicEnum.EDUCATIONAL,
        }

        for _, row in df.iterrows():
            topic_str = row.get("Topic", "").strip()
            topic = topic_mapping.get(topic_str, TopicEnum.EDUCATIONAL)

            time_str = str(row.get("Time (PST)", "")).strip()

            day_of_week = str(row.get("Day of the Week", "")).strip()

            coach = str(row.get("Health Coach/RD", "")).strip()

            # TODO: Pull description from CSV
            description = DEFAULT_DESCRIPTIONS.get(topic, "")

            webinar_data = {
                "title": row.get("Title", "Unnamed Session"),
                "topic": topic,
                "description": description,
                "presenter": coach if coach != "nan" else "",
                "time": time_str if time_str != "nan" else "",
                "day_of_week": day_of_week if day_of_week != "nan" else "",
            }
            webinars.append(webinar_data)

        logger.success(
            f"Successfully parsed {len(webinars)} webinars from CSV using pandas"
        )
        return webinars
    except Exception as e:
        logger.error(f"Error parsing CSV with pandas: {str(e)}")
        return []


async def clean_tables():
    await Booking.all().delete()
    await LiveSession.all().delete()
    await Webinar.all().delete()


async def create_live_sessions(data: dict, webinar_id: UUID, participants):
    num_sessions = 3  # Default

    now = pendulum.now()

    for i in range(num_sessions):
        session_date = now.add(months=i)

        # If we have day_of_week info, try to align with that
        day_of_week = data.get("day_of_week", "")
        if day_of_week:
            # This is a simple approach - for production, we would need to use more robust date calculations
            if "1st" in day_of_week:
                session_date = session_date.replace(day=1)
            elif "2nd" in day_of_week:
                session_date = session_date.replace(day=8)
            elif "3rd" in day_of_week:
                session_date = session_date.replace(day=15)
            elif "4th" in day_of_week:
                session_date = session_date.replace(day=22)

            if "Mon" in day_of_week:
                while session_date.day_of_week != 1:  # 1 = Monday
                    session_date = session_date.add(days=1)
            elif "Tues" in day_of_week or "Tue" in day_of_week:
                while session_date.day_of_week != 2:  # 2 = Tuesday
                    session_date = session_date.add(days=1)
            elif "Wed" in day_of_week:
                while session_date.day_of_week != 3:  # 3 = Wednesday
                    session_date = session_date.add(days=1)
            elif "Thurs" in day_of_week or "Thu" in day_of_week:
                while session_date.day_of_week != 4:  # 4 = Thursday
                    session_date = session_date.add(days=1)
            elif "Fri" in day_of_week:
                while session_date.day_of_week != 5:  # 5 = Friday
                    session_date = session_date.add(days=1)

        time_str = data.get("time", "")
        if time_str and ":" in time_str:
            try:
                hour, minute = (
                    time_str.replace("AM", "").replace("PM", "").strip().split(":")
                )
                hour = int(hour)
                minute = int(minute)

                if "PM" in time_str and hour < 12:
                    hour += 12

                session_date = session_date.set(hour=hour, minute=minute)
            except ValueError:
                pass

        session = await LiveSession.create(
            title=f"Live Session {i + 1}: {data['title']}",
            description=data["description"],
            meeting_start_time=session_date,
            webinar_id=webinar_id,
            meeting_type=MeetingTypeEnum.RECURRING_WITH_FIXED_TIME,
            zoom_id="1111111111",
            zoom_occurrence_id="123123123",
            timezone="America/Los_Angeles",
            has_conflict=False,
        )

        for p_id in sample(participants, randint(1, 7)):
            await Booking.create(
                live_session_id=session.id,
                participant_id=p_id,
                status=choice(BOOKING_STATUSES),
            )


async def create_webinars(
    env: str = "local",
    clean: bool = False,
    csv_file: Optional[str] = None,
):
    logger.info(f"Fetching settings for {env} environment...")
    settings = get_settings(ENV=env)

    await custom_init_db(settings)
    logger.info("DB initialized")

    if clean:
        logger.info("Cleaning tables...")
        await clean_tables()

    authorized = await Authorized.all()
    # TODO: Check emails for authorized users

    logger.info("Current authorized users:")
    for a in authorized:
        print(a.email)

    participants = await Participant.all().values_list("id", flat=True)

    webinar_data = SAMPLE_WEBINARS
    if csv_file:
        logger.info(f"Parsing CSV file: {csv_file}")
        webinar_data = parse_csv_with_pandas(csv_file)
        if not webinar_data:
            logger.warning("No valid data found in CSV")
            return None

    try:
        for i, data in enumerate(webinar_data):
            if "title" not in data or not data["title"]:
                continue

            host = choice(authorized)

            if "topic" not in data:
                data["topic"] = choice(list(TopicEnum))

            if "description" not in data or not data["description"]:
                data["description"] = DEFAULT_DESCRIPTIONS.get(
                    data["topic"], "An informative session on health and wellness."
                )

            webinar = await Webinar.create(
                title=data["title"],
                description=data["description"],
                topic=data["topic"],
                recurrence=RecurrenceEnum.MONTHLY,
                host_id=host.id,
                max_capacity=200,
                cover_url="https://sample.image.com",
            )

            await webinar.save()

            await create_live_sessions(data, webinar.id, participants)

            logger.success(f"Webinar created: {data['title']}")

        total_created = len(webinar_data)
        logger.success(
            f"All data created - {total_created} webinars with live sessions"
        )

    except ConnectionRefusedError:
        logger.error(f"Error: Could not connect to {settings.ENV} DB")

    finally:
        await close_db()


@click.command()
@click.option(
    "-e",
    "--environment",
    type=str,
    default="local",
    help="Environment to use. Default: local",
)
@click.option(
    "-c",
    "--clean",
    is_flag=True,
    help="Clean tables before populating",
)
@click.option(
    "--csv",
    type=str,
    default=None,
    help="Path to CSV file with webinar data",
)
def populate(
    environment: str = "local", clean: bool = False, csv: Optional[str] = None
):
    if environment != ENV.PROD:
        asyncio.run(create_webinars(env=environment, clean=clean, csv_file=csv))


if __name__ == "__main__":
    populate()
