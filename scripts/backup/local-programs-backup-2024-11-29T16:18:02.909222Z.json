[{"title": "IBC", "modules": [{"title": "Introduction to the Program", "short_title": "Week 0/50", "length": 7, "description": "I look forward to starting this journey with you. Stay motivated. Together we will do it!", "order": 1, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "2a05a974-62d9-483e-bd0e-97e6913aae53"}, {"title": "Introduction video: Reviewing the program", "description": "Welcome to the program! Check out our introduction video where we discuss goals, motivations, and the structure of the program. Please watch whenever you are ready to get started with us!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Solera_Intro_Video.mp4", "type": null, "form_id": null, "started_at": null}, "id": "7c3f6dc9-9e8a-48bc-b967-9a18877a600b"}, {"title": "Set the Goals. Commit to a healthier lifestyle.", "description": "Now that you've identified your goals for the program, it's time to save your pledge for us to review in our first live session. Let's GO!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "sHANMvTU", "started_at": null}, "id": "c6417d87-ad24-4e79-8428-eecb83b59162"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "ab0fe102-1656-4d71-8a2c-45da9fe76e15"}, {"title": "Introduction to the Program", "description": "This introductory module that will help you change your lifestyle by moving from the thinking phase to the action phase. The module sets the stage for the entire Lose Weight course.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "programs/IBC/pdf/testPage.pdf", "type": null, "form_id": null, "signed_url": null, "started_at": null}, "id": "c59fc3a6-991c-482a-aec0-74658105a866"}, {"title": "Test", "description": "Test", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "https://www.youtube.com/watch?v=H41fuhz_gvw", "type": "video", "form_id": "", "started_at": null}, "id": "ab957795-f427-4aa7-9de3-024751ee48b8"}, {"title": "Test", "description": "Test", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "https://www.youtube.com/watch?v=H41fuhz_gvw", "type": "file", "form_id": "", "started_at": null}, "id": "999d05e7-3aa7-4f56-a3e6-e3d669aaf292"}, {"title": "ASDASD", "description": "", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": "", "type": "type_form", "form_id": "ASDASDASD///", "started_at": null}, "id": "0e56100c-cb6a-49ca-b89a-824c0346e499"}, {"title": "ASDASD", "description": "ASDASD", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": "", "type": "type_form", "form_id": "a2vR0BwJ", "signed_url": null, "started_at": null}, "id": "9953f888-d94b-4e33-9e8c-3a3c669a85ec"}], "id": "680b3155-3fbe-485e-8c88-d945686e31b7"}, {"title": "Get Active", "short_title": "Week 1/50", "length": 7, "description": "Physical activity can help lose weight. This module introduces the concept of getting active. Make it fun! This is activity NOT exercise - don't forget to complete the Personal Success Tool (PST) online module for this week", "order": 2, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "2d2ca4df-7021-44be-886a-6af2e61605f0"}, {"title": "Video: Get Active", "description": "Learn how to get active", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module2_get_active.mov", "type": null, "form_id": null, "started_at": null}, "id": "ab8e5d1f-3c4a-4426-b367-45b0c4fe6deb"}, {"title": "Daily Stretching", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/EXERCISE/Daily_Stretching.pdf", "type": null, "form_id": null, "started_at": null}, "id": "8004efe9-657a-4d61-b3f5-cecba74dfaa6"}, {"title": "Get active to Lose Weight", "description": "Think about how physically active you are right now. Find an activity that’s right for you and make a plan for when things get in the way.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "unD4OYd1", "started_at": null}, "id": "8421bb10-1e8c-4dbf-a7eb-8966aa8e0fe6"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "92b95474-c8ad-4c8c-a9ac-e91097fdbf0d"}], "id": "1f83f60e-1b1b-43c5-8292-36a7568c33d7"}, {"title": "Track Your Activity", "short_title": "Week 2/50", "length": 7, "description": "Tracking, or self-monitoring, can help you lose weight. From now on you'll need to provide your activity minutes each week. Remember we're in this together so start where you feel comfortable", "order": 3, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "b412917c-7ccc-4646-b99b-778f00a1a511"}, {"title": "Video: Track Your Activity", "description": "Track Your Activity to Lose Weight", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module3_track_your_activity.mov", "type": null, "form_id": null, "started_at": null}, "id": "7334e691-7e18-4ee5-9c0e-b3d493aa24b6"}, {"title": "Back and Triceps Body Weight Workout", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/EXERCISE/Day_1___Back_and_Triceps_Body_Weight_Workout.pdf", "type": null, "form_id": null, "started_at": null}, "id": "2cbbc7c6-9209-4714-a0e4-71418f336f91"}, {"title": "Track Your Activity", "description": "Tracking your physical activity is the best way to be sure you hit your goals. It helps you remember. It keeps you accountable. Itʼs the best way to see how far youʼve come!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "cRmgQA65", "started_at": null}, "id": "61b9ffd8-b67f-43ba-8d7b-a2f7e523d792"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "f6afd71b-8226-4967-8cee-6b9f7c294fd1"}], "id": "068f33bd-1872-400a-9a37-f251530bd9ab"}, {"title": "Eat Well to Lose Weight", "short_title": "Week 3/50", "length": 7, "description": "Eating well can help you lose weight. This module includes a plate with suggested portions of food.", "order": 4, "sections": [{"title": "Article: Eat Right for a Healthy Weight", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/RECIPE/Eating_Right_for_a_Healthy_Weight.pdf", "type": null, "form_id": null, "started_at": null}, "id": "488950b6-482f-4317-86b9-2f0402f9b54c"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3daa9350-eb19-44a5-b8d4-e9615ab26797"}, {"title": "Video: Eat Well to Lose Weight", "description": "We'll learn about building a healthy meal and portion sizes", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module4_eat_well.mov", "type": null, "form_id": null, "started_at": null}, "id": "ae602874-8e47-416a-a1c1-b4c065f1ccb0"}, {"title": "Eat Well to Lose Weight", "description": "This session we will talk about: How to eat well, How to build a healthy meal, The items in each food group. ", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_4_Eat_Well.pdf", "type": null, "form_id": null, "started_at": null}, "id": "4480e49e-6de8-433b-93c4-4a4ae45c0095"}, {"title": "Eat well, live well", "description": "Eating well is a crucial component to creating a healthier you. Each healthy choice you make brings you one step closer to your goal! Are you ready to discover new ways to eat well?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "B0vQZYgj", "started_at": null}, "id": "9d409250-4ff3-422b-9a65-e14f3c512839"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "134aa9c6-30fd-490c-8e23-5126c944cdb9"}], "id": "e0011edc-4b22-4178-b311-1e4ecf51f078"}, {"title": "Track Your Food", "short_title": "Week 4/50", "length": 7, "description": "Tracking your food is one of the best ways to really understand the foods you eat each day. Try it for this week and see what new information you find. It can be helpful to track how you feel after you eat as well.  It can help you discover if there are any foods that impact how you feel.", "order": 5, "sections": [{"title": "The Plate Method (Plate Control)", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/the-plate-method.html", "type": null, "form_id": null, "started_at": null}, "id": "b68a5943-70bb-4cea-9b38-77dff507e2a5"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "400e7573-7a63-4a3d-872c-4268a4459996"}, {"title": "Track Your Food", "description": "Have you ever tracked your food to really see what you are eating in a day? This video will discuss ways to track your food", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module5_Track_Your_Food.mp4", "type": null, "form_id": null, "started_at": null}, "id": "9344a57c-06c0-463a-830d-b5ca910ec766"}, {"title": "Track Your Food", "description": "Tracking your food each day can help you lose weight. You will learn: The purpose of tracking, How to track your food, How to make sense of food labels", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_5_Track_Your_Food.pdf", "type": null, "form_id": null, "started_at": null}, "id": "827a9985-7c67-46c6-abf6-b3dc386a4910"}, {"title": "Redefine your goals. Recommit to yourself. Make healthy living a habit.", "description": "Now that you’re a few weeks into your program, it’s important to stop and reflect on your goals. What has been working? What has been holding you back?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "Kads4OdG", "started_at": null}, "id": "2c399bb7-146b-4790-ab12-f9012c63ce8c"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "1bb410c3-3032-47ac-b12b-1dddc31d968b"}], "id": "4ee22120-35d7-4cba-8c7b-9ccd9f8d470e"}, {"title": "Get More Active", "short_title": "Week 5/50", "length": 7, "description": "This builds on what you learned in the 2nd session and includes information on how to increase your activity in small steps.  I look forward to hearing about your favorite activities!", "order": 6, "sections": [{"title": "Article: 5 Mindless Eating", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/5-ways-to-beat-mindless-eating.html", "type": null, "form_id": null, "started_at": null}, "id": "b1f5099e-d5f8-4478-8140-d9b079bdf150"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "f88c5b33-65c8-430d-bfc9-edba924e039a"}, {"title": "Video: Get More Active to Lose Weight", "description": "Ways to increase your activity in small steps", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module6_get_more_active.mov", "type": null, "form_id": null, "started_at": null}, "id": "199b2359-627c-4dd3-a3c8-a877eac48779"}, {"title": "Chest and Biceps Body Weight Workout", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-2-chest-biceps-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "fc6ca22b-a7ce-43d4-9816-4b9d92924694"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "76f7e5e6-e7fd-4f2e-a0c3-e76e286efc0b"}], "id": "1a52f5cf-0b15-4883-85c0-71d27c18a4ac"}, {"title": "Burn More Calories Than You Take In", "short_title": "Week 6/50", "length": 7, "description": "Curious about how many calories are in food and how many calories you burn during activities?  This module will help you understand those calories and ways to balance them", "order": 7, "sections": [{"title": "Recipe: Nutrition Reset Guide", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/nutrition-reset-guide.html", "type": null, "form_id": null, "started_at": null}, "id": "1d153463-26b1-4adc-8a00-0cdb2f1201de"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "7e8c0855-c3c2-4a79-b969-51aadccb5cad"}, {"title": "Video: Burn More Calories than you Take in", "description": "We’ll discuss how to lose weight by burning more calories than you take in.", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module7_burn_more_caloriese.mov", "type": null, "form_id": null, "started_at": null}, "id": "02e46167-1484-441a-8bcd-6883ebb17538"}, {"title": "Cardio - Endurance Body Weight Workout", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-3-cardio-endurance-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "660e98e1-a003-432e-bc4e-53588a2cd3fa"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "4d2641ea-6376-43ca-8b8c-f9b51eb8a4d1"}], "id": "4d1db4c8-6044-4e55-bb34-c7169b81f4e9"}, {"title": "Healthy Food", "short_title": "Week 7/50", "length": 7, "description": "This module has healthy shopping and cooking tips. Sometimes it's as easy as making a simple substitution to make your favorite recipe healthier.", "order": 8, "sections": [{"title": "Article: Lower Body Flexibility Workout", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-5-lower-body-flexibility-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "211b62f0-28f8-4697-9a63-6ff69adba360"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "27b02d2a-be40-41b1-9946-7ee4ac9ffa8f"}, {"title": "Video: <PERSON> and Cook to <PERSON><PERSON> Weight", "description": "This video discusses how to buy and cook healthy food.", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module_8_Shop_and_cook.mov", "type": null, "form_id": null, "started_at": null}, "id": "48e63d71-268a-48bc-813b-18cde0b47599"}, {"title": "Shop and Cook to Lose Weight", "description": "Healthy shopping and cooking can help you lose weight. This session we will talk about: Healthy food, How to shop for healthy food, How to cook healthy food", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_8_Shop_and_Cook.pdf", "type": null, "form_id": null, "started_at": null}, "id": "856c81a4-8709-4228-9712-b8963b769cc7"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "39bb8151-5c18-4327-848a-ce2ace6ff52f"}], "id": "2d72c096-4ae2-47ad-922f-216d3790bf86"}, {"title": "Manage Stress", "short_title": "Week 8/50", "length": 7, "description": "You Are Amazing! This week's module is about stress, it's effect on weight loss and how to deal with it to live happier and healthier life. ", "order": 9, "sections": [{"title": "Article: Cultivating Self- Awareness and Mindfulness", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/cultivating-self-awareness-and-mindfulness.html", "type": null, "form_id": null, "started_at": null}, "id": "c76a9ce5-5cd9-43f2-9917-8f7b1f632049"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "2d350d1d-cfb3-421e-b498-4823c3237af9"}, {"title": "Manage Stress", "description": "Stress plays a big component in our health. Having the ability to manage stress will ultimately help with achieving your goals for this program", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module9_Manage_Stress.mp4", "type": null, "form_id": null, "started_at": null}, "id": "d3573c20-e0ab-4033-8c6e-645029f114eb"}, {"title": "Manage Stress", "description": "Managing stress can help you lose weight. This session we will talk about: Some causes of stress, the link between stress and weight loss, Some ways to reduce stress, some healthy ways to cope with stress", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_9_Manage_Stress.pdf", "type": null, "form_id": null, "started_at": null}, "id": "6e883f55-f6d6-4a97-97b4-08e451b5cb02"}, {"title": "Stress is an everyday part of life.", "description": "But you can do something about it! Take a few minutes to practice healthy ways to manage stress so you can still reach your goals.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "XZbTO8iB", "started_at": null}, "id": "d08f7bb8-1bab-4216-bd23-b4b9e672c093"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "02949f8b-b551-404d-95d0-0a7bfd6c72bc"}], "id": "57428e41-9661-445d-b383-034918d4873c"}, {"title": "Find Time For Fitness", "short_title": "Week 9/50", "length": 7, "description": "We are doing great! This module teaches about how to find time to be active. You know that getting active is one of the key factors to lose weight. ", "order": 10, "sections": [{"title": "Article: Legs Body Weight Workout", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-4-legs-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "187d2bd0-8c8d-433c-b7ad-9196f295646c"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "38f00a9e-0d70-4576-b841-f234bb07d27b"}, {"title": "Finding Time for Fitness", "description": "This video will show you different ways to schedule in more fitness", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module_9_finding_time_for_fitness.mov", "type": null, "form_id": null, "started_at": null}, "id": "f0f4d7b3-3cae-4e4d-82c3-f276dc1bf683"}, {"title": "Find Time for Fitness", "description": "It can be challenging to fit in at least 150 minutes of activity each week.\nThis session we will talk about: Benefits of being active, The challenge of fitting in fitness, How to find time for fitness", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_10_Find_time_For_Fitness.pdf", "type": null, "form_id": null, "started_at": null}, "id": "6f4aad7b-472f-4015-be5b-3d658e359e38"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "a83b36b4-8a51-45dc-aac1-b08dea1e88a8"}], "id": "a6c01e7e-2558-4816-8369-a4db47e53ed3"}, {"title": "<PERSON> with <PERSON><PERSON><PERSON>", "short_title": "Week 10-11/50", "length": 14, "description": "How are you feeling today? This module teaches about how to cope with triggers of unhealthy behaviors.", "order": 11, "sections": [{"title": "Breathing Techni<PERSON> to Soothe the Soul", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": null, "form_id": null, "started_at": null}, "id": "aa2a4b7f-7a23-4211-a26c-b130e3bee859"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "7705a68c-87ef-477c-b907-e5eeb3e146ce"}, {"title": "Coping with triggers", "description": "This video will show you different ways to cope with triggers", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/HWM_VIDEO/Module11_Coping_with_triggers.mov", "type": null, "form_id": null, "started_at": null}, "id": "19c43195-aca4-4cd2-9b3c-316ed5e80df4"}, {"title": "<PERSON> with <PERSON><PERSON><PERSON> to <PERSON><PERSON> Weight", "description": "Coping with triggers can help you lose weight. This session we will talk about: Some unhealthy food shopping triggers and ways to cope with them, Some unhealthy eating triggers and ways to cope with them, Some triggers of sitting still and ways to cope with them", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant-Module-11_.<PERSON>_With_Triggers. Weight Loss.pdf.pdf", "type": null, "form_id": null, "started_at": null}, "id": "e1da49d2-2e77-4f6b-9c6e-67f532b904e9"}, {"title": "Take Control of Your Triggers", "description": "You can take control of your triggers. The smell of a favorite dish, an argument with a loved one, baked goods in the office breakroom. When you’re learning to make healthy choices, sometimes everyday life can throw you off track", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "GL17cZVu", "started_at": null}, "id": "3b6422e8-3435-4b17-8858-c1b1d72b4af6"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "0a662748-f4d0-4b80-a38c-e8b1276b2ad1"}], "id": "25ff65a1-a443-4995-abe4-d0a224252590"}, {"title": "Keep Your Heart Healthy", "short_title": "Week 12-13/50", "length": 14, "description": "You made it this far, well done! This module is about why and how to keep your heart healthy. See you soon!", "order": 12, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "a2eb2b40-f583-4424-846e-97553551b4c3"}, {"title": "Heart Health", "description": "This video, led by one of Ciba Health physicians, will discuss ways to keep your heart healthy!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module12_Keeping_Your_Heart_Healthy.mp4", "type": null, "form_id": null, "started_at": null}, "id": "8a67327e-abb0-4fc4-a580-1096ac3f77f0"}, {"title": "Keep Your Heart Healthy", "description": "Since you are at risk to have problems with your heart or arteries, it’s important to keep your heart healthy. This session we will talk about: Why heart health matters, How to keep your heart healthy, How to be heart smart about fats.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Module-12_Keep_Your_Heart_Healthy_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "6ba9a0fe-0795-460c-b973-9bf58d525ed0"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "d1578355-a714-4c9e-92fe-2a4d818da655"}], "id": "d65aaee7-269d-4925-8afb-7ea2e8cbe2ed"}, {"title": "Take Charge of Your Thoughts", "short_title": "Week 14-15/50", "length": 14, "description": "Keep positive! This module teaches about how to replace harmful thoughts with helpful thoughts and why it matters for your health. ", "order": 13, "sections": [{"title": "Breathing Techni<PERSON> to Soothe the Soul", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": null, "form_id": null, "started_at": null}, "id": "11113c42-d114-45d5-82c3-635924639b8b"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "95543a15-6177-480d-9853-6866d5144f3d"}, {"title": "Take Charge of Your Thoughts", "description": "This video will discuss taking charge of your thoughts to stay positive during your weight loss journey", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module13_Take_charge_of_your_thoughts.mov", "type": null, "form_id": null, "started_at": null}, "id": "5351ec44-fdc5-45f8-8002-e99f91b41546"}, {"title": "Take Charge of Your Thoughts", "description": "Taking charge of your thoughts can help you lose weight. This session we will talk about: The difference between harmful and helpful thoughts, How to replace harmful thoughts with helpful thoughts", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_13_Take_Charge_Of_Your_Thoughts_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "4d8d6a7c-27ae-47a4-b580-393d5bdf14ac"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "a3d39240-98ef-47c7-a3d2-66bc6b6ef4cc"}], "id": "cda0fe81-8d85-4f9b-ab7a-f39f7f21e1e5"}, {"title": "Get Support", "short_title": "Week 16-17/50", "length": 14, "description": "Hey! You made it today! This module teaches about how to get support for healthy lifestyle and why it matters.", "order": 14, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e4b42196-dbe2-40a2-b00f-c5dfec2cd687"}, {"title": "Get support", "description": "This video will discuss how to get support from the people around you, to aid you in your journey to better wellness!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module14_Get_Support.mp4", "type": null, "form_id": null, "started_at": null}, "id": "426ff120-792e-47fa-a258-d8c876ee2b99"}, {"title": "Get Support", "description": "Getting support for your healthy lifestyle can help you lose weight. This session we will talk about how to get support from Family, friends, and coworkers, Groups, classes, and clubs, Professionals", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_14_Get_Support_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "35d8042f-9bb5-4fef-b4e2-6c70859747db"}, {"title": "Get Support. Make Lasting Changes.", "description": "To make sure small steps become true lifestyle changes, you need to build a strong support network. Can you count on friends? Talk to family members? Could a doctor or counselor help you succeed?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "vVN65cCn", "started_at": null}, "id": "ca3814a3-720a-4f99-b040-477b7aeb7c97"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "80c901d6-9a76-4c05-9b30-4d5bdaff4480"}], "id": "045f7bc2-cfac-4ec8-a75c-1aead4c6c359"}, {"title": "Eat Well Away from Home", "short_title": "Week 18-19/50", "length": 14, "description": "Hey! This module teaches about how to stay on track with eating goals at restaurants and social events.", "order": 15, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "6307e42b-786a-4f7f-91fd-e8552088bb76"}, {"title": "Eat Well Away From Home", "description": "This video will discuss tips and tracks when eating away from home", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module15_Eat_Well_Away_From_Home.mp4", "type": null, "form_id": null, "started_at": null}, "id": "f20fc4d9-9dd5-4bd2-b63a-0df3f8d59d29"}, {"title": "Eat Well Away from Home", "description": "We will talk about: Some challenges of eating well at restaurants and social events, How to plan for and cope with these challenges.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module15_Eat_Well_Away_From_Home_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "9a8d39c4-45c6-4eed-a99c-60e4c579b196"}, {"title": "Eat well wherever you are", "description": "Whether you are out with friends, having a lunch break on the go, or are heading to a sporting event, sticking to your healthy eating goals when you’re away from home can be a major challenge. But with the right skills and mindset, you can enjoy yourself when you’re out and about and still be healthy!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "yHBCTi79", "started_at": null}, "id": "2c030634-4d50-4b93-8423-0a0f54e574b2"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "8bb12b95-80ae-494d-89f9-d353027c553e"}], "id": "32687da6-0bdc-4e63-b02a-7a3f660472bf"}, {"title": "Stay Motivated to Lose Weight", "short_title": "Week 20-21/50", "length": 14, "description": "You've come a long way in the program. This module helps you reflect on your progress and keep making positive changes over the next six months.", "order": 16, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "6ce9aac1-4e90-4ab0-9e67-ed555ff4b3fd"}, {"title": "Stay Motivated", "description": "Stay motivated in this journey, you got this! This video will discuss staying motivated to the end", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module16_Stay_Motivated.mp4", "type": null, "form_id": null, "started_at": null}, "id": "e9552615-8150-44da-aea2-51a062641b08"}, {"title": "Stay Motivated to Lose Weight", "description": "In this session we will talk about: How far you’ve come since you started this program, Our next steps, Your goals for the next six months.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module16_Stay_Motivated_To_Prevent_Obesity_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "46044940-3ae0-438a-8dc7-a86ca070d5a0"}, {"title": "Commit to action. Pledge to change.", "description": "It’s time to stop and reflect. How will you stay track toward your goals? Take the step now to recommit to the program and to adopting lifelong healthy habits.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "zkE8vqpP", "started_at": null}, "id": "278c7379-93a7-4988-9535-2ffd0ba634a6"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "6054a452-3dc8-4093-9161-37e1f56da82d"}], "id": "b504a554-7ad7-41ab-9a1f-cd27589bbdc0"}, {"title": "When Weight Loss Stalls", "short_title": "Week 22-23/50", "length": 14, "description": "Hey! This module encourages You to pause and reflect on experiences, reset goals, and refresh daily routines to stay motivated on your weight loss journey.", "order": 17, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "a28f787d-f5d3-4145-a098-dff4ae49ea45"}, {"title": "When Weight Loss Stalls", "description": "Sometimes it feels like our weight loss can hit a plateu. In this video, our registered dietitian will discuss how to push through this wall!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module17_When_Weight_Loss_Stalls.mp4", "type": null, "form_id": null, "started_at": null}, "id": "714ad117-26a6-4abb-aa64-b758bfe9f270"}, {"title": "When Weight Loss Stalls", "description": "In this session we will talk about: Why weight loss can stall, How to start losing weight again", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_17_When_Weight_Loss_Stalls_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "984eb994-0dd3-44e4-8879-7acc69874b5b"}, {"title": "Make your healthy habits last a lifetime.", "description": "Have you met your weight loss goal? Congratulations! Are you still working toward your goal weight? That's OK! No matter where you are on your journey, consistency is the key to making a lasting lifestyle change.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "FDl6tBdn", "started_at": null}, "id": "03a25480-3c66-47e6-8f59-5c3cf0173b3d"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e236da87-fc1c-4cd4-91fe-f307fd4ddb99"}], "id": "40101d11-47ab-42a7-b16c-c6822df063dd"}, {"title": "Take a Fitness Break", "short_title": "Week 24-25/50", "length": 14, "description": "You made it so far! This module teaches about how to overcome barriers and about taking a 2-minute fitness break every 30 minutes.", "order": 18, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "79d7442e-ac79-44a1-9938-16f54f19fe13"}, {"title": "Time for a Fitness Break", "description": "This is a marathon, not a sprint! This video will discuss how you can incorporate small fitness breaks into your everyday routine", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module18_Take_a_fitness_break.mp4", "type": null, "form_id": null, "started_at": null}, "id": "2042bd13-27fe-4937-a7fe-a9cd08825faa"}, {"title": "Take a Fitness Break", "description": "Taking a 2-minute fitness break every 30 minutes can help you lose weight.\nThis session we will talk about: The link between sitting still and losing weight, Some challenges of taking fitness breaks and ways to cope with them.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_18_Take_a_Fitness_break_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "c41e269c-9f53-4b19-9d2b-df30063a3918"}, {"title": "Are you sitting down as you read this?", "description": "We spend so much time sitting—in the car, at work, relaxing at home. Did you know that sitting still for too long can lead to health problems? The good news is that taking short activity breaks every 30 minutes can make a big difference.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "isEZatYX", "started_at": null}, "id": "23f458a6-0261-49ca-88bd-61062c6d38a7"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "bfee493e-e924-4eed-b1c5-ad29ce2f1f10"}, {"title": "Test url", "description": "Test url", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "programs/IBC/pdf/Ciba Health Employee PTO Policy -- Q1 2024.pdf", "type": "file", "form_id": null, "signed_url": null, "started_at": null}, "id": "d2166fd6-ca59-47b1-a577-49d113c858de"}], "id": "2a0b56f1-d1ee-4ec6-9ff6-c97283bba70d"}, {"title": "Stay Active to Lose Weight", "short_title": "Week 26-27/50", "length": 14, "description": "Hey! This module teaches about how to cope with some challenges of staying active. Keep going!", "order": 19, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3ae27ac8-bd1e-49ee-809a-43fdcf2ec9d4"}, {"title": "Stay Active to Lose Weight", "description": "This video will provide encouragement while finding motivation to stay active in your journey", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module19_Stay_Active_to_Lose_Weight.mp4", "type": null, "form_id": null, "started_at": null}, "id": "39bd6142-1e60-47a6-b132-37c3258d8dd1"}, {"title": "Stay Active to Lose Weight", "description": "This session we will talk about: Some benefits of staying active, Some challenges of staying active and ways to cope with them and How far you’ve come since you started this program", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Module_19_Stay_Active_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "4d767e5a-a7f7-4ba5-a94b-64560a8db7ed"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "c776b71c-32c2-490f-93e1-4a77df159d90"}], "id": "15272116-73c8-49fa-8148-d2a8e36269a8"}, {"title": "Stay Active Away from Home", "short_title": "Week 28-29/50", "length": 14, "description": "You made it so far! This module teaches about how to overcome barriers to staying active when you aren't at home", "order": 20, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "7a0e92ab-17d3-4636-920c-16ac90f636b1"}, {"title": "Stay Active Away from Home", "description": "This video will discuss how to stay active away from home, cope with challenges, and planning for success", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module20_Stay_active_away_from_home.mp4", "type": null, "form_id": null, "started_at": null}, "id": "11c24187-06ec-4b1d-ae72-8ea53f416919"}, {"title": "Stay Active Away from Home", "description": "Staying active away from home can help you lose weight. In this session we will talk about challenges of staying active away from home, and ways to cope with them", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_20_Stay_Active_Away_From_Home_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "22744b33-d8c2-4e2f-9864-97f31a11df3a"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "53ec5b7a-6704-4126-bac6-5147ef27ee69"}], "id": "702e2a9e-4d06-45be-b481-6b7c7f144b61"}, {"title": "To Be Filled", "short_title": "Week 30-31/50", "length": 14, "description": "To Be Filled", "order": 21, "sections": [], "id": "e5094c20-129f-4669-ac9a-27481cd9ef3c"}, {"title": "To Be Filled", "short_title": "Week 32-35/50", "length": 28, "description": "To Be Filled", "order": 22, "sections": [], "id": "1be77e3e-716d-4272-8393-9737fe060446"}, {"title": "To Be Filled", "short_title": "Week 36-39/50", "length": 28, "description": "To Be Filled", "order": 23, "sections": [], "id": "ac90a526-9605-440e-941c-6dea86bbc65b"}, {"title": "To Be Filled", "short_title": "Week 40-43/50", "length": 28, "description": "To Be Filled", "order": 24, "sections": [], "id": "0d7022bb-6157-4ab3-b331-33b989c76d3c"}, {"title": "To Be Filled", "short_title": "Week 44-47/50", "length": 28, "description": "To Be Filled", "order": 25, "sections": [], "id": "04a33487-9034-40e1-9615-5fe59a72d0ba"}, {"title": "To Be Filled", "short_title": "Week 48-51/50", "length": 28, "description": "To Be Filled", "order": 26, "sections": [], "id": "c6cc15c7-eda8-4cf4-b719-6210786102e2"}], "id": "842debe7-a6ab-4e4c-97c6-0996fb357fb9"}, {"title": "HWM", "modules": [{"title": "Introduction to the Program", "short_title": "Week 0/50", "length": 7, "description": "I look forward to starting this journey with you. Stay motivated. Together we will do it!", "order": 1, "sections": [{"title": "Introduction video: Reviewing the program", "description": "Welcome to the program! Check out our introduction video where we discuss goals, motivations, and the structure of the program. Please watch whenever you are ready to get started with us!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Solera_Intro_Video.mp4", "type": null, "form_id": null, "started_at": null}, "id": "816d461b-5386-49d3-ab40-9a47ed898e26"}, {"title": "Introduction to the Program", "description": "This introductory module that will help you change your lifestyle by moving from the thinking phase to the action phase. The module sets the stage for the entire Lose Weight course.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_1_Introduction.pdf", "type": null, "form_id": null, "started_at": null}, "id": "ca6d59e9-ba12-4276-baf9-34236cd0fe1e"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "5df03b3a-04c1-4cfa-8509-abc5e7257349"}, {"title": "Set the Goals. Commit to a healthier lifestyle.", "description": "Now that you've identified your goals for the program, it's time to save your pledge for us to review in our first live session. Let's GO!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": "type_form", "form_id": "sHANMvTU", "signed_url": null, "started_at": null}, "id": "c5c02f20-8bb0-48d8-8300-e123830c15a7"}], "id": "1f68f3e0-bf33-482c-ad8d-ada13c4db605"}, {"title": "Get Active", "short_title": "Week 1/50", "length": 7, "description": "Physical activity can help lose weight. This module introduces the concept of getting active. Make it fun! This is activity NOT exercise - don't forget to complete the Personal Success Tool (PST) online module for this week", "order": 2, "sections": [{"title": "test1", "description": "asdasd", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": "file", "form_id": "", "started_at": null}, "id": "5aa2d6e9-a27e-464f-ae82-efa35f0bde46"}, {"title": "Get active to Lose Weight", "description": "Think about how physically active you are right now. Find an activity that’s right for you and make a plan for when things get in the way.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "unD4OYd1", "started_at": null}, "id": "d13a7e91-ebee-462e-9598-dc0215f0cbb1"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "f15dc01a-ab83-4fcc-b985-59b7584182d7"}, {"title": "Video: Get Active", "description": "Learn how to get active", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module2_get_active.mov", "type": null, "form_id": null, "started_at": null}, "id": "b43f1928-a982-4396-a67c-737c51460837"}, {"title": "Daily Stretching", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/EXERCISE/Daily_Stretching.pdf", "type": null, "form_id": null, "started_at": null}, "id": "33ef9b16-1182-4a94-b51c-59c951926db3"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "8ba4ed3d-cd13-4cf1-8de1-b4c1300aa40c"}], "id": "4cb5b144-45f6-4fc6-9aec-c0714089e7a1"}, {"title": "Track Your Activity", "short_title": "Week 2/50", "length": 7, "description": "Tracking, or self-monitoring, can help you lose weight. From now on you'll need to provide your activity minutes each week. Remember we're in this together so start where you feel comfortable", "order": 3, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e93b3d4b-df8d-4f28-97e2-82e184962dde"}, {"title": "Video: Track Your Activity", "description": "Track Your Activity to Lose Weight", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module3_track_your_activity.mov", "type": null, "form_id": null, "started_at": null}, "id": "cdcb7a30-79e9-4547-9a8c-610dac3223f2"}, {"title": "Back and Triceps Body Weight Workout", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/EXERCISE/Day_1___Back_and_Triceps_Body_Weight_Workout.pdf", "type": null, "form_id": null, "started_at": null}, "id": "47a0e05a-800d-430b-85a0-946fc73b35c3"}, {"title": "Track Your Activity", "description": "Tracking your physical activity is the best way to be sure you hit your goals. It helps you remember. It keeps you accountable. Itʼs the best way to see how far youʼve come!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "cRmgQA65", "started_at": null}, "id": "3a5fcd2a-8e6b-4da1-a1bd-3d736b9a85b6"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "49cf2f0d-7a65-4de1-84ad-ad8bdce073f2"}], "id": "0d85e563-16c9-4f7e-adfd-69d4f03ec60e"}, {"title": "Eat Well to Lose Weight", "short_title": "Week 3/50", "length": 7, "description": "Eating well can help you lose weight. This module includes a plate with suggested portions of food.", "order": 4, "sections": [{"title": "Article: Eat Right for a Healthy Weight", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/RECIPE/Eating_Right_for_a_Healthy_Weight.pdf", "type": null, "form_id": null, "started_at": null}, "id": "308a547b-92d6-4de3-8f68-395348c18e2c"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3be262b2-3f5f-4630-9237-3cc258dbad48"}, {"title": "Video: Eat Well to Lose Weight", "description": "We'll learn about building a healthy meal and portion sizes", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module4_eat_well.mov", "type": null, "form_id": null, "started_at": null}, "id": "85308a9a-962e-472f-b678-2ed4c378dbd2"}, {"title": "Eat Well to Lose Weight", "description": "This session we will talk about: How to eat well, How to build a healthy meal, The items in each food group. ", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_4_Eat_Well.pdf", "type": null, "form_id": null, "started_at": null}, "id": "be7771b8-7e22-4827-a6c5-2771abb84044"}, {"title": "Eat well, live well", "description": "Eating well is a crucial component to creating a healthier you. Each healthy choice you make brings you one step closer to your goal! Are you ready to discover new ways to eat well?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "B0vQZYgj", "started_at": null}, "id": "4cfbd106-f58e-4aac-8011-8539dbe36361"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e6380c2f-e401-4dc8-84a0-b58be7bedbb8"}], "id": "a3fff9e8-5af0-4260-a643-19ebfb16225a"}, {"title": "Track Your Food", "short_title": "Week 4/50", "length": 7, "description": "Tracking your food is one of the best ways to really understand the foods you eat each day. Try it for this week and see what new information you find. It can be helpful to track how you feel after you eat as well.  It can help you discover if there are any foods that impact how you feel.", "order": 5, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "786755e4-a98d-446a-ba3d-49c5f30ced46"}, {"title": "The Plate Method (Plate Control)", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/the-plate-method.html", "type": null, "form_id": null, "started_at": null}, "id": "c77f6c99-97a9-4a82-86fd-42c939eb5652"}, {"title": "Track Your Food", "description": "Have you ever tracked your food to really see what you are eating in a day? This video will discuss ways to track your food", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module5_Track_Your_Food.mp4", "type": null, "form_id": null, "started_at": null}, "id": "9638f504-7eb8-4e93-9ede-dc4d30db153f"}, {"title": "Track Your Food", "description": "Tracking your food each day can help you lose weight. You will learn: The purpose of tracking, How to track your food, How to make sense of food labels", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_5_Track_Your_Food.pdf", "type": null, "form_id": null, "started_at": null}, "id": "f4a6e8d3-583e-4fb2-85ba-8f2e3678b28e"}, {"title": "Redefine your goals. Recommit to yourself. Make healthy living a habit.", "description": "Now that you’re a few weeks into your program, it’s important to stop and reflect on your goals. What has been working? What has been holding you back?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "Kads4OdG", "started_at": null}, "id": "4717a93d-7ffe-4c17-aee0-fc8503c9ce95"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "46b4909f-f953-49cd-b0ca-cab439ad645b"}], "id": "6eebac1f-aff6-4240-beb0-a723381270c6"}, {"title": "Get More Active", "short_title": "Week 5/50", "length": 7, "description": "This builds on what you learned in the 2nd session and includes information on how to increase your activity in small steps.  I look forward to hearing about your favorite activities!", "order": 6, "sections": [{"title": "Article: 5 Mindless Eating", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/5-ways-to-beat-mindless-eating.html", "type": null, "form_id": null, "started_at": null}, "id": "5fe538e6-4e82-4810-8a33-85c6c083b5cc"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "6cc6a530-1c8b-40d3-a7a7-e16cbc8c66c1"}, {"title": "Video: Get More Active to Lose Weight", "description": "Ways to increase your activity in small steps", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module6_get_more_active.mov", "type": null, "form_id": null, "started_at": null}, "id": "7539719c-8523-4343-a7b8-cdf7966379ae"}, {"title": "Chest and Biceps Body Weight Workout", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-2-chest-biceps-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "bb24ed4e-3206-4de5-bb65-699639ae02dd"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "ba71284c-963e-426f-ae43-2b0ec2cece19"}], "id": "78b28f73-bb52-4fbf-adf3-507125d88a6a"}, {"title": "Burn More Calories Than You Take In", "short_title": "Week 6/50", "length": 7, "description": "Curious about how many calories are in food and how many calories you burn during activities?  This module will help you understand those calories and ways to balance them", "order": 7, "sections": [{"title": "Recipe: Nutrition Reset Guide", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/nutrition-reset-guide.html", "type": null, "form_id": null, "started_at": null}, "id": "64c85c76-3147-46c2-95ae-ac72611e9f8f"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "69663091-ad44-42a6-88fa-18c60e0d7ee5"}, {"title": "Video: Burn More Calories than you Take in", "description": "We’ll discuss how to lose weight by burning more calories than you take in.", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module7_burn_more_caloriese.mov", "type": null, "form_id": null, "started_at": null}, "id": "92468c47-426f-4fa4-9003-a986dad4832c"}, {"title": "Cardio - Endurance Body Weight Workout", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-3-cardio-endurance-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "80bfcc7d-9b07-428d-9473-1b23fe45c8a5"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "7ff432f1-0bf3-415f-a703-ce757d5d7600"}], "id": "d6dc738b-8f5d-48f1-94f2-ce20fa92901e"}, {"title": "Healthy Food", "short_title": "Week 7/50", "length": 7, "description": "This module has healthy shopping and cooking tips. Sometimes it's as easy as making a simple substitution to make your favorite recipe healthier.", "order": 8, "sections": [{"title": "Article: Lower Body Flexibility Workout", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-5-lower-body-flexibility-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "2454719e-1ee9-47d1-8693-a2778d69d6da"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "f736c0d0-9a7a-47c7-875c-f2ffa40a9a93"}, {"title": "Video: <PERSON> and Cook to <PERSON><PERSON> Weight", "description": "This video discusses how to buy and cook healthy food.", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module_8_Shop_and_cook.mov", "type": null, "form_id": null, "started_at": null}, "id": "835c463f-6d58-4fb9-a8a9-cab74d3a6292"}, {"title": "Shop and Cook to Lose Weight", "description": "Healthy shopping and cooking can help you lose weight. This session we will talk about: Healthy food, How to shop for healthy food, How to cook healthy food", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_8_Shop_and_Cook.pdf", "type": null, "form_id": null, "started_at": null}, "id": "3add658a-3e65-47de-9bdd-80f88eb7f3c1"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e3d3729f-0f5b-4810-bb3a-49938c85403e"}], "id": "f675fe14-5686-4ef2-a666-261cb8153f50"}, {"title": "Manage Stress", "short_title": "Week 8/50", "length": 7, "description": "You Are Amazing! This week's module is about stress, it's effect on weight loss and how to deal with it to live happier and healthier life. ", "order": 9, "sections": [{"title": "Article: Cultivating Self- Awareness and Mindfulness", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/cultivating-self-awareness-and-mindfulness.html", "type": null, "form_id": null, "started_at": null}, "id": "de4bbd3a-1e92-4c9e-a258-2c2584787eec"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e3a0d44a-6de6-4c5c-8f13-4b534d9fb911"}, {"title": "Manage Stress", "description": "Stress plays a big component in our health. Having the ability to manage stress will ultimately help with achieving your goals for this program", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module9_Manage_Stress.mp4", "type": null, "form_id": null, "started_at": null}, "id": "a733ce4d-8420-4f33-b269-07f34ccd73b9"}, {"title": "Manage Stress", "description": "Managing stress can help you lose weight. This session we will talk about: Some causes of stress, the link between stress and weight loss, Some ways to reduce stress, some healthy ways to cope with stress", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_9_Manage_Stress.pdf", "type": null, "form_id": null, "started_at": null}, "id": "4946e095-ddd8-4ed4-8470-f6710274d51e"}, {"title": "Stress is an everyday part of life.", "description": "But you can do something about it! Take a few minutes to practice healthy ways to manage stress so you can still reach your goals.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "XZbTO8iB", "started_at": null}, "id": "44bcb85c-1f15-44d3-838e-e98228af851e"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "d829b06e-cb3b-4638-8a29-5bee3ba5150c"}], "id": "8713966c-f1d7-4b0c-bc86-f92bf3424b04"}, {"title": "Find Time For Fitness", "short_title": "Week 9/50", "length": 7, "description": "We are doing great! This module teaches about how to find time to be active. You know that getting active is one of the key factors to lose weight. ", "order": 10, "sections": [{"title": "Article: Legs Body Weight Workout", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-4-legs-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "e9004456-06aa-4230-a9d0-207b76c2b2e8"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "9e5827a1-a66a-4981-8c96-955a124af644"}, {"title": "Finding Time for Fitness", "description": "This video will show you different ways to schedule in more fitness", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module_9_finding_time_for_fitness.mov", "type": null, "form_id": null, "started_at": null}, "id": "97c3a9e5-9762-4924-ab63-8f2739f997ae"}, {"title": "Find Time for Fitness", "description": "It can be challenging to fit in at least 150 minutes of activity each week.\nThis session we will talk about: Benefits of being active, The challenge of fitting in fitness, How to find time for fitness", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_10_Find_time_For_Fitness.pdf", "type": null, "form_id": null, "started_at": null}, "id": "41873ce3-879a-44b9-84b2-aad39c97c408"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "745a9a4d-d8c0-4edc-befc-86c93d555a2b"}], "id": "2428521b-3775-455b-ba56-a514b359c8ca"}, {"title": "<PERSON> with <PERSON><PERSON><PERSON>", "short_title": "Week 10-11/50", "length": 14, "description": "How are you feeling today? This module teaches about how to cope with triggers of unhealthy behaviors.", "order": 11, "sections": [{"title": "Breathing Techni<PERSON> to Soothe the Soul", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": null, "form_id": null, "started_at": null}, "id": "fd5c5084-5ebc-41f7-90c5-9bf09c1d8b75"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "2fae840a-ea09-4d56-99f2-c3788cf07af4"}, {"title": "Coping with triggers", "description": "This video will show you different ways to cope with triggers", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/HWM_VIDEO/Module11_Coping_with_triggers.mov", "type": null, "form_id": null, "started_at": null}, "id": "54571335-a441-443c-b04d-8b24f330c81d"}, {"title": "<PERSON> with <PERSON><PERSON><PERSON> to <PERSON><PERSON> Weight", "description": "Coping with triggers can help you lose weight. This session we will talk about: Some unhealthy food shopping triggers and ways to cope with them, Some unhealthy eating triggers and ways to cope with them, Some triggers of sitting still and ways to cope with them", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant-Module-11_.<PERSON>_With_Triggers. Weight Loss.pdf.pdf", "type": null, "form_id": null, "started_at": null}, "id": "639fa72b-a109-4142-aaa2-4cb694d5e415"}, {"title": "Take Control of Your Triggers", "description": "You can take control of your triggers. The smell of a favorite dish, an argument with a loved one, baked goods in the office breakroom. When you’re learning to make healthy choices, sometimes everyday life can throw you off track", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "GL17cZVu", "started_at": null}, "id": "747fbd3a-3aef-4415-893c-d288b100ea16"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "49226b4a-83b7-45cd-8c1e-7fc9dd1ef2e0"}], "id": "0bf296a8-150d-4000-b671-7879715cee16"}, {"title": "Keep Your Heart Healthy", "short_title": "Week 12-13/50", "length": 14, "description": "You made it this far, well done! This module is about why and how to keep your heart healthy. See you soon!", "order": 12, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "2e56af0d-08aa-4d51-806c-a67fec2a77c2"}, {"title": "Heart Health", "description": "This video, led by one of Ciba Health physicians, will discuss ways to keep your heart healthy!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module12_Keeping_Your_Heart_Healthy.mp4", "type": null, "form_id": null, "started_at": null}, "id": "983a9847-f689-4b14-9a58-b93edda5fc24"}, {"title": "Keep Your Heart Healthy", "description": "Since you are at risk to have problems with your heart or arteries, it’s important to keep your heart healthy. This session we will talk about: Why heart health matters, How to keep your heart healthy, How to be heart smart about fats.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Module-12_Keep_Your_Heart_Healthy_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "4f2bdf5a-1b1b-4e9d-b2e4-06db109d86a7"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "d877996e-6f20-4057-9d79-350e47a991b5"}], "id": "1931423a-c108-4862-aef6-a34540fce4c4"}, {"title": "Take Charge of Your Thoughts", "short_title": "Week 14-15/50", "length": 14, "description": "Keep positive! This module teaches about how to replace harmful thoughts with helpful thoughts and why it matters for your health. ", "order": 13, "sections": [{"title": "Breathing Techni<PERSON> to Soothe the Soul", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": null, "form_id": null, "started_at": null}, "id": "73e19c7a-34a5-4e33-8c60-6eee045a0297"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "d8a832f9-fad5-4545-8a4a-5d8d6b5fefb5"}, {"title": "Take Charge of Your Thoughts", "description": "This video will discuss taking charge of your thoughts to stay positive during your weight loss journey", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module13_Take_charge_of_your_thoughts.mov", "type": null, "form_id": null, "started_at": null}, "id": "d68b28a1-9276-47fa-b6bc-868c591d3f86"}, {"title": "Take Charge of Your Thoughts", "description": "Taking charge of your thoughts can help you lose weight. This session we will talk about: The difference between harmful and helpful thoughts, How to replace harmful thoughts with helpful thoughts", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_13_Take_Charge_Of_Your_Thoughts_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "efafa8c5-4827-40c9-a2cf-c22e35edd8ce"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "a82ccc3f-dc2b-4399-93db-ae65b0c5886e"}], "id": "b8ad30f5-1ae8-4501-ad35-15b956a39589"}, {"title": "Get Support", "short_title": "Week 16-17/50", "length": 14, "description": "Hey! You made it today! This module teaches about how to get support for healthy lifestyle and why it matters.", "order": 14, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "375c80c9-7c41-45c5-9ce4-a39b03630451"}, {"title": "Get support", "description": "This video will discuss how to get support from the people around you, to aid you in your journey to better wellness!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module14_Get_Support.mp4", "type": null, "form_id": null, "started_at": null}, "id": "439416d6-d370-45d6-af4a-df3703e27e99"}, {"title": "Get Support", "description": "Getting support for your healthy lifestyle can help you lose weight. This session we will talk about how to get support from Family, friends, and coworkers, Groups, classes, and clubs, Professionals", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_14_Get_Support_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "a87310fe-2dc8-4a21-a7ae-d5f419f4027d"}, {"title": "Get Support. Make Lasting Changes.", "description": "To make sure small steps become true lifestyle changes, you need to build a strong support network. Can you count on friends? Talk to family members? Could a doctor or counselor help you succeed?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "vVN65cCn", "started_at": null}, "id": "4e802060-b101-4ab1-85d3-159f3ab1b3dd"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "caa211c4-b3b2-465a-9886-f11d86e51313"}], "id": "4be2b4e7-76af-4441-9f62-0a3b79f280d6"}, {"title": "Eat Well Away from Home", "short_title": "Week 18-19/50", "length": 14, "description": "Hey! This module teaches about how to stay on track with eating goals at restaurants and social events.", "order": 15, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "d1d74919-9ef2-4fc0-87e1-444a3db5708e"}, {"title": "Eat Well Away From Home", "description": "This video will discuss tips and tracks when eating away from home", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module15_Eat_Well_Away_From_Home.mp4", "type": null, "form_id": null, "started_at": null}, "id": "56670da0-9fb9-4292-ad89-0a5221c87e58"}, {"title": "Eat Well Away from Home", "description": "We will talk about: Some challenges of eating well at restaurants and social events, How to plan for and cope with these challenges.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module15_Eat_Well_Away_From_Home_Weight_loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "a4b47f39-0d2e-4ecf-adff-3aaac82c8a7a"}, {"title": "Eat well wherever you are", "description": "Whether you are out with friends, having a lunch break on the go, or are heading to a sporting event, sticking to your healthy eating goals when you’re away from home can be a major challenge. But with the right skills and mindset, you can enjoy yourself when you’re out and about and still be healthy!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "yHBCTi79", "started_at": null}, "id": "96730471-84be-4890-948e-4abdcccae06b"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "ea5dcb74-0d0d-472b-9e35-b611def7aead"}], "id": "c2ce7628-f7af-4603-abf2-cc8a2bb58407"}, {"title": "Stay Motivated to Lose Weight", "short_title": "Week 20-21/50", "length": 14, "description": "You've come a long way in the program. This module helps you reflect on your progress and keep making positive changes over the next six months.", "order": 16, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "df46e8ab-7a82-4ae3-8971-c485e2016af4"}, {"title": "Stay Motivated", "description": "Stay motivated in this journey, you got this! This video will discuss staying motivated to the end", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module16_Stay_Motivated.mp4", "type": null, "form_id": null, "started_at": null}, "id": "0e9479a5-d41c-422f-96c5-30e6f91e8589"}, {"title": "Stay Motivated to Lose Weight", "description": "In this session we will talk about: How far you’ve come since you started this program, Our next steps, Your goals for the next six months.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module16_Stay_Motivated_To_Prevent_Obesity_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "fbed7899-b2bf-48ea-96d4-38e61d8c8b92"}, {"title": "Stay motivated to make a lasting change.", "description": "The next six months are just as important. Whether you've taken a few small steps to adopt healthier habits or reached a major milestone on the scale, don't stop now!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "cH0ll5Li", "started_at": null}, "id": "1702717f-d800-491a-aa5a-9b182b12a8e1"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e4095ab6-0a86-48f1-9a5b-1108b22d25d7"}], "id": "8cedaf8a-ea8b-40ff-b4a5-622c20ab540b"}, {"title": "When Weight Loss Stalls", "short_title": "Week 22-23/50", "length": 14, "description": "Hey! This module encourages You to pause and reflect on experiences, reset goals, and refresh daily routines to stay motivated on your weight loss journey.", "order": 17, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "be738c4c-e1cd-4fd7-a0b9-10b8fbde1066"}, {"title": "When Weight Loss Stalls", "description": "Sometimes it feels like our weight loss can hit a plateu. In this video, our registered dietitian will discuss how to push through this wall!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module17_When_Weight_Loss_Stalls.mp4", "type": null, "form_id": null, "started_at": null}, "id": "886191f2-0be3-4eb2-8f35-47cf2b33f6ee"}, {"title": "When Weight Loss Stalls", "description": "In this session we will talk about: Why weight loss can stall, How to start losing weight again", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_17_When_Weight_Loss_Stalls_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "72b86c71-4a18-41d9-a943-0a466cb1f029"}, {"title": "Recommit to yourself. Make healthy living a habit.", "description": "Now that you’re a few weeks into your program, it’s important to stop and reflect on your goals. What has been working? What has been holding you back?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "axirq6WH", "started_at": null}, "id": "8009ce39-5c14-484d-8d57-070a7e07bf47"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3f0d0f1c-3578-45df-8fb5-f5309a29e9fb"}], "id": "0d23081b-9213-4dc6-8ee4-6c3419811606"}, {"title": "Take a Fitness Break", "short_title": "Week 24-25/50", "length": 14, "description": "You made it so far! This module teaches about how to overcome barriers and about taking a 2-minute fitness break every 30 minutes.", "order": 18, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3e007e8e-c770-4cd9-b026-8265fcb0d276"}, {"title": "Time for a Fitness Break", "description": "This is a marathon, not a sprint! This video will discuss how you can incorporate small fitness breaks into your everyday routine", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module18_Take_a_fitness_break.mp4", "type": null, "form_id": null, "started_at": null}, "id": "913e4d49-ff1a-4ae2-871b-f8aa5f29652b"}, {"title": "Take a Fitness Break", "description": "Taking a 2-minute fitness break every 30 minutes can help you lose weight.\nThis session we will talk about: The link between sitting still and losing weight, Some challenges of taking fitness breaks and ways to cope with them.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/IBC_Module_18_Take_a_Fitness_break_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "f692686e-a0bc-4acc-bd7c-ad9482a3853c"}, {"title": "Are you sitting down as you read this?", "description": "We spend so much time sitting—in the car, at work, relaxing at home. Did you know that sitting still for too long can lead to health problems? The good news is that taking short activity breaks every 30 minutes can make a big difference.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "isEZatYX", "started_at": null}, "id": "e30c5dd7-32ed-431f-9cee-5093e9953fdb"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "502f8b92-44cb-4ab5-af00-2d68baa3136a"}], "id": "a216456c-17d3-404a-98a3-bd804f75f82d"}, {"title": "Stay Active to Lose Weight", "short_title": "Week 26-27/50", "length": 14, "description": "Hey! This module teaches about how to cope with some challenges of staying active. Keep going!", "order": 19, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3b5bc7d3-2147-4540-be83-acfa1ac76b43"}, {"title": "Stay Active to Lose Weight", "description": "This video will provide encouragement while finding motivation to stay active in your journey", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module19_Stay_Active_to_Lose_Weight.mp4", "type": null, "form_id": null, "started_at": null}, "id": "e124cf05-4356-4337-be72-806ae65146b5"}, {"title": "Stay Active to Lose Weight", "description": "This session we will talk about: Some benefits of staying active, Some challenges of staying active and ways to cope with them and How far you’ve come since you started this program", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Module_19_Stay_Active_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "0c1750df-c975-4fe9-bc9b-a2e7a8d8aba8"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "f225ca81-d348-4290-bce4-f39baac59b91"}], "id": "dcc30655-266a-4031-b9f0-fe96f8eaa97b"}, {"title": "Stay Active Away from Home", "short_title": "Week 28-29/50", "length": 14, "description": "You made it so far! This module teaches about how to overcome barriers to staying active when you aren't at home", "order": 20, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "2ddad169-8650-4749-b4a0-3a66d57a9b30"}, {"title": "Stay Active Away from Home", "description": "This video will discuss how to stay active away from home, cope with challenges, and planning for success", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module20_Stay_active_away_from_home.mp4", "type": null, "form_id": null, "started_at": null}, "id": "6e5712e9-dc69-4a04-bc06-5d160b463c6f"}, {"title": "Stay Active Away from Home", "description": "Staying active away from home can help you lose weight. In this session we will talk about challenges of staying active away from home, and ways to cope with them", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/IBC_V1/Participant_Module_20_Stay_Active_Away_From_Home_Weight_Loss.pdf", "type": null, "form_id": null, "started_at": null}, "id": "40fc806a-642f-4f0d-8d4e-18f4c37ba258"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "9368a5eb-9825-4f7b-82fc-a896ccb3bb5e"}], "id": "5b4c46c9-ed8a-4343-b081-94465036bc02"}, {"title": "To Be Filled", "short_title": "Week 30-31/50", "length": 14, "description": "To Be Filled", "order": 21, "sections": [], "id": "e24927cb-5e2c-4cf8-85db-206c4cb80325"}, {"title": "To Be Filled", "short_title": "Week 32-35/50", "length": 28, "description": "To Be Filled", "order": 22, "sections": [], "id": "ca4e623e-64f3-4b8a-8c33-89fcc8381a9a"}, {"title": "To Be Filled", "short_title": "Week 36-39/50", "length": 28, "description": "To Be Filled", "order": 23, "sections": [], "id": "5ef9ce80-7c67-4acb-88fd-afb007b83fbe"}, {"title": "To Be Filled", "short_title": "Week 40-43/50", "length": 28, "description": "To Be Filled", "order": 24, "sections": [], "id": "3ce72084-3358-4a4b-bc24-42d41949fced"}, {"title": "To Be Filled", "short_title": "Week 44-47/50", "length": 28, "description": "To Be Filled", "order": 25, "sections": [], "id": "e1c15ebb-cddd-473f-a650-ecc38e45bbfb"}, {"title": "To Be Filled", "short_title": "Week 48-51/50", "length": 28, "description": "To Be Filled", "order": 26, "sections": [{"title": "test different url not youtube", "description": "", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "https://youtu.be/a3ICNMQW7Ok?si=ptfKvXovBhaT2Je4", "type": "video", "form_id": "", "signed_url": null, "started_at": null}, "id": "aebea409-190d-44da-b33e-5e3ec0a76534"}, {"title": "test ", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "123", "type": "file", "form_id": null, "signed_url": null, "started_at": null}, "id": "03aac445-5823-43c5-938e-094383218dc9"}, {"title": "test url 2", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "123124", "type": "file", "form_id": null, "signed_url": null, "started_at": null}, "id": "a4a7329f-99af-488b-b0e4-55ba59248052"}, {"title": "test12", "description": "qwe", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "user_input", "form_id": null, "signed_url": null, "started_at": null}, "id": "91ad2fc4-cf23-46e7-a280-7d64a695086b"}, {"title": "test url with file", "description": "", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "programs/HWM/video/src/Screen Recording 2024-09-17 at 1.49.21 PM.mov", "type": "video", "form_id": null, "signed_url": null, "started_at": null}, "id": "12b1e8e5-571f-491c-9121-a94b9f7018a8"}, {"title": "test", "description": "s;lkdgm;klsdjmg", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "123124124", "type": "file", "form_id": null, "started_at": null}, "id": "cd1e55d5-0d0b-480f-9b73-5defc8a91eca"}, {"title": "test12", "description": "qwe", "activity_type": "weight_type", "activity_category": "weight", "metadata": {"url": null, "type": "user_input", "form_id": null, "signed_url": null, "started_at": null}, "id": "70a6bc04-204d-46f2-91d6-54ee6e88906e"}], "id": "5faedb6a-031a-4af3-86d3-f77f5ba4a229"}], "id": "c3e5eb97-4c75-4f00-b243-51d9fd091694"}, {"title": "test", "modules": [{"title": "week 1", "short_title": "week 1", "length": 7, "description": "", "order": 1, "sections": [{"title": "qweqw", "description": "qweqweqw", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "https://www.youtube.com/watch?v=Yys43tMxMoc", "type": "video", "form_id": "", "signed_url": null, "started_at": null}, "id": "21d99892-839d-423c-9469-5a492ec80a6b"}, {"title": "asdasdasd", "description": "sdasdasdsd", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": "", "type": "type_form", "form_id": "asdasdasd", "started_at": null}, "id": "770960d2-fbb9-471b-a5b1-e416e568f451"}, {"title": "<PERSON><PERSON><PERSON>", "description": "asdasd", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/asdasd", "type": "file", "form_id": "", "started_at": null}, "id": "a8f89793-10a8-49c2-8ed4-d935b253ae7a"}], "id": "503dac8f-421e-4758-a208-c8753c2e1493"}], "id": "74dc43b6-6d5d-4b94-9281-225b597c41c5"}, {"title": "test_08", "modules": [{"title": "123", "short_title": "123", "length": 7, "description": "", "order": 1, "sections": [{"title": "asdasd", "description": "<PERSON><PERSON><PERSON>", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "https://www.youtube.com/watch?v=5gO0xpY_Y3E", "type": "video", "form_id": "", "started_at": null}, "id": "ddc12aeb-d6ab-4eef-9b31-2d101e585ae9"}, {"title": "cv", "description": "cv", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "https://www.google.com", "type": "file", "form_id": "", "started_at": null}, "id": "fda589fe-a4db-4e98-9877-81d08c0ea722"}, {"title": "pdf", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "programs/test_08/pdf/testPage.pdf", "type": "file", "form_id": null, "signed_url": null, "started_at": null}, "id": "85ccc837-9051-4264-8859-fb358cd6108c"}, {"title": "cv2", "description": "desc", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "programs/test_08/pdf/testPage.pdf", "type": "file", "form_id": null, "signed_url": null, "started_at": null}, "id": "3ff7f7f8-4df0-4e51-8e41-9852de697b6e"}, {"title": "asdpdfgog", "description": "asdasd123", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "https://google.com", "type": "file", "form_id": "", "signed_url": null, "started_at": null}, "id": "fb2a1c91-388e-4aca-81d3-c5f300b44bf0"}], "id": "a6919600-4ace-4a50-96b4-0d996c371981"}], "id": "7a4aa7b9-84f6-4c59-b8ba-4e6613f1c609"}, {"title": "Updating test", "modules": [{"title": "Updating test", "short_title": "useSectionForm.tsx", "length": 7, "description": "useSectionForm.tsx ", "order": 1, "sections": [{"title": "YouTube link", "description": "link test", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "https://www.youtube.com/watch?v=q4AHJ22hz-E&list=RDCLAK5uy_kLWIr9gv1XLlPbaDS965-Db4TrBoUTxQ8&index=2", "type": "video", "form_id": "", "started_at": null}, "id": "a8477a79-99cd-4c8a-9a5c-7018f7de6cea"}, {"title": "File uploading", "description": "uploading test", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "programs/B37LQ0wYkZ0DruJ_Program/video/src/Screen Recording 2024-08-28 at 10.42.36.mov", "type": "video", "form_id": null, "signed_url": null, "started_at": null}, "id": "c84d75f0-0363-4266-b46e-820d470356fb"}, {"title": "typeform link", "description": "typeform link", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": "https://cibahealth.typeform.com/to/sHANMvTU", "type": "type_form", "form_id": "", "started_at": null}, "id": "3795e930-9224-4a1e-b7a8-c6687f2ca2ca"}, {"title": "typeform id", "description": "id", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": "", "type": "type_form", "form_id": "sHANMvTU", "started_at": null}, "id": "b2fd3632-9068-4e91-93e7-f53e3c70bda0"}], "id": "098b2741-60a4-46b0-b7df-6986bb8005d8"}], "id": "918527cd-8b47-486a-ba0b-d4e25cd51b2b"}, {"title": "tAQ5MKBG5hAEZM7_Program", "modules": [{"title": "TQPv91C6M5Rjn4V Module", "short_title": "TQPv91C6M5Rjn4V Module", "length": 7, "description": "ttMNXRoSPTln5UO 08-28-2024 13:08", "order": 1, "sections": [], "id": "543bace4-6190-4bd0-8fc3-767f99ffcd0f"}], "id": "e0885b87-251c-44da-ab90-1d9878891105"}, {"title": "3GmAixs5Tq7g8zQ_Program", "modules": [{"title": "61WA3thPVQDX2Ct Module", "short_title": "61WA3thPVQDX2Ct Module", "length": 7, "description": "xEDuL8JlclVtLYl 08-29-2024 13:08", "order": 1, "sections": [], "id": "5bde5e74-48d0-4234-bc65-659b45824378"}], "id": "521bc89f-105a-429d-8743-16852dad6f4e"}, {"title": "C5iXvFYumto5uxl_Program", "modules": [{"title": "ezwEIa73oZS66T1 Module", "short_title": "ezwEIa73oZS66T1 Module", "length": 7, "description": "uLLsCQCIa2daVnX 08-30-2024 13:08", "order": 1, "sections": [], "id": "518d6867-c6d7-4403-ad95-7ecd7d53cd50"}], "id": "19fcf6ca-2fa4-4e56-a34b-db1a5a673e98"}, {"title": "H5jw8a8wHZCmt3R_Program", "modules": [{"title": "EiYajON4O2LiBJX Module", "short_title": "EiYajON4O2LiBJX Module", "length": 7, "description": "yWBAlEh9kpcFLHm 08-31-2024 13:08", "order": 1, "sections": [], "id": "36cb5514-6b10-470e-b931-dab47b69a33e"}], "id": "088dbc2e-ac94-41d7-9564-cc7b96a679ac"}, {"title": "LLKd3ByNtJDVtAP_Program", "modules": [{"title": "cjbcxymWdPNncXF Module", "short_title": "cjbcxymWdPNncXF Module", "length": 7, "description": "thh7zl4KHNVKWAX 09-01-2024 13:09", "order": 1, "sections": [], "id": "6ba8e07b-0a6f-40bb-9bff-07fa86004b1f"}], "id": "eebe53d8-9f2d-43ce-8d39-7e2a54ebe570"}, {"title": "OdqBt0uh8ZNXvYz_Program", "modules": [{"title": "rM9Auz5z9R4pWdU Module", "short_title": "rM9Auz5z9R4pWdU Module", "length": 7, "description": "7dXjSYt9zCD51yt 09-02-2024 13:09", "order": 1, "sections": [], "id": "302235a9-462f-4468-bb99-6e2e7376b308"}], "id": "e49dc263-3d2a-4e4a-92da-e89b5ffb429b"}, {"title": "nWPUVuRVF1hun0R_Program", "modules": [{"title": "aVGgSWe9mNOHzWL Module", "short_title": "aVGgSWe9mNOHzWL Module", "length": 7, "description": "HblxdizBlepKhEg 09-03-2024 13:09", "order": 1, "sections": [], "id": "c09c96ac-7d53-478a-b30c-f19b06ade6cc"}], "id": "7fd6a8db-04f5-4e92-9509-63f67599449c"}, {"title": "3MOIwRqCqHWZR6X_Program", "modules": [{"title": "S0bEIzXshjFExgU Module", "short_title": "S0bEIzXshjFExgU Module", "length": 7, "description": "kq3od7bH63WpPF3 09-04-2024 13:09", "order": 1, "sections": [], "id": "6cef521c-0708-4682-8aea-30b319e98e4e"}], "id": "42a315fe-a625-4bc3-a9d3-a27e37932ae6"}, {"title": "xWzW6FVjTHc7zv0_Program", "modules": [{"title": "TkFXAKzZgDBw1QV Module", "short_title": "TkFXAKzZgDBw1QV Module", "length": 7, "description": "F8L86xfB8iZYcGw 09-05-2024 13:09", "order": 1, "sections": [], "id": "97a0fd87-20ef-47de-a33c-9e4112207a5e"}], "id": "65ec2e17-6d63-4a64-8e51-0d5703612c07"}, {"title": "SPYKd11EIJVs8eO_Program", "modules": [{"title": "uRMIFTF5iSHGveZ Module", "short_title": "uRMIFTF5iSHGveZ Module", "length": 7, "description": "SGXoITNHsM4IYJ7 09-06-2024 13:09", "order": 1, "sections": [], "id": "989d2225-16c2-465f-b509-375e72bdcbb4"}], "id": "ab7bfc03-baf7-466e-a952-ec5f2180cb65"}, {"title": "jhJ35Mmp1ixpxE7_Program", "modules": [{"title": "IUV6FAcDPjEAmos Module", "short_title": "IUV6FAcDPjEAmos Module", "length": 7, "description": "zaP9weWBfhrKqza 09-07-2024 13:09", "order": 1, "sections": [], "id": "0176de80-2b80-4436-b85e-21d9a1d36881"}], "id": "51b3c924-65b5-45ec-abb2-040711120663"}, {"title": "I0wOr0tY6RPs03s_Program", "modules": [{"title": "ldGhh24gULOFoR7 Module", "short_title": "ldGhh24gULOFoR7 Module", "length": 7, "description": "07cVXTGYhGc54qB 09-08-2024 13:09", "order": 1, "sections": [], "id": "0221053c-4b98-4826-a4ba-4f207e1b99e8"}], "id": "5f4879cf-b861-4cd8-a87e-fa870fcfbed6"}, {"title": "ox6Hhnhtuoa8Wiy_Program", "modules": [{"title": "XcvvSTLMsE7VH8F Module", "short_title": "XcvvSTLMsE7VH8F Module", "length": 7, "description": "fNOjDkfUAkqHYCy 09-09-2024 13:09", "order": 1, "sections": [], "id": "d5ffc15d-775b-4a29-b9c2-a286ff5d265d"}], "id": "960cadeb-443e-43be-abaa-c57971438511"}, {"title": "F9wgUtM79W3xuv2_Program", "modules": [{"title": "zZIPiYuPzX3lVbD Module", "short_title": "zZIPiYuPzX3lVbD Module", "length": 7, "description": "yqFQ17Qp6mQ5AFO 09-10-2024 13:09", "order": 1, "sections": [], "id": "42ca6141-5e47-4489-b4b8-9b4459c6a37a"}], "id": "dfde1776-1dd8-432f-b684-96dde47668e8"}, {"title": "HmKmOcV4GuEyhhM_Program", "modules": [{"title": "Dlmi32QlbgkgHMN Module", "short_title": "Dlmi32QlbgkgHMN Module", "length": 7, "description": "LP8fHGStztCPViu 09-11-2024 13:09", "order": 1, "sections": [], "id": "02d9f246-112d-4f60-bcbb-0b6ee3415b9c"}], "id": "bfc6d61d-a368-43ae-8903-7a14cd5762a2"}, {"title": "U4I0pS1VMsckQYs_Program", "modules": [{"title": "7QQlinU006gq3Fy Module", "short_title": "7QQlinU006gq3Fy Module", "length": 7, "description": "Gh9nvOmjxiC33Le 09-12-2024 13:09", "order": 1, "sections": [], "id": "043658be-e31b-42df-a88a-cd1d821a488d"}], "id": "00f4e528-e538-46b9-91cd-d7cfb35fea69"}, {"title": "TH7VoMzjGjGbkKL_Program", "modules": [{"title": "bBAq2N3r7wKVplD Module", "short_title": "bBAq2N3r7wKVplD Module", "length": 7, "description": "AZRri5INUPM4cGF 09-13-2024 13:09", "order": 1, "sections": [], "id": "b9ec883c-48e4-4770-bc89-77f20ed33982"}], "id": "195ddfb4-3382-4282-a435-023110c50f65"}, {"title": "qJia3pfNusFrrAE_Program", "modules": [{"title": "FYpjwhDKolMLROt Module", "short_title": "FYpjwhDKolMLROt Module", "length": 7, "description": "G9AJu9PoOdXIZnB 09-14-2024 13:09", "order": 1, "sections": [], "id": "794f3665-9c91-4abc-854d-40aea4989b06"}], "id": "81dc4d49-9cc6-4ad2-ab6b-4f144556ae55"}, {"title": "zVR7RtjQ9k8kxuc_Program", "modules": [{"title": "GXhmH3IX3sC9Tce Module", "short_title": "GXhmH3IX3sC9Tce Module", "length": 7, "description": "yQrtOU1HxzAUEyz 09-15-2024 13:09", "order": 1, "sections": [], "id": "44627676-120e-46e2-a13c-93cad9e6c557"}], "id": "63d1f85e-3c18-4f00-94b4-c584c67c13f2"}, {"title": "X4FA52cZKCgP88Q_Program", "modules": [{"title": "uJqIcS4BdsYC6Sw Module", "short_title": "uJqIcS4BdsYC6Sw Module", "length": 7, "description": "GsvvopBFUEMZKhd 09-16-2024 13:09", "order": 1, "sections": [], "id": "7beb02a2-d916-48dd-a6a5-8c33e0386faf"}], "id": "31ac6200-1b05-4a72-a08f-a5e623d5af9c"}, {"title": "gNAYQE6VzoEdAXt_Program", "modules": [{"title": "Z4PYYGyO2UCi1md Module", "short_title": "Z4PYYGyO2UCi1md Module", "length": 7, "description": "3pW3wk72qPCw5SW 09-17-2024 13:09", "order": 1, "sections": [], "id": "331d11ab-7d92-4b8d-a17b-42236ad7cfbb"}], "id": "ad4f74be-11ee-4d5e-912b-6c47bf931752"}, {"title": "5aIww6hbolQEbuz_Program", "modules": [{"title": "KhQeZbg9z7WJAJl Module", "short_title": "KhQeZbg9z7WJAJl Module", "length": 7, "description": "FsAZH6fSrp51uDA 09-18-2024 13:09", "order": 1, "sections": [], "id": "d7606851-ad0d-4b7f-a39d-0e2c78c04032"}], "id": "fa4ad591-bc7a-4b0d-b741-3659646b5aac"}, {"title": "dOFHfO0rzKouc3v_Program", "modules": [{"title": "TATNCKlIj2aSln2 Module", "short_title": "TATNCKlIj2aSln2 Module", "length": 7, "description": "9sKu1e4YA9MKA3l 09-19-2024 13:09", "order": 1, "sections": [], "id": "2756ff5c-9203-4d54-90d3-aa09434d1c1c"}], "id": "55cca4c6-fc1c-409e-b0da-d3b926529d02"}, {"title": "rDC2AYNPwaLU2KM_Program", "modules": [{"title": "4h75JRooAIbdqNt Module", "short_title": "4h75JRooAIbdqNt Module", "length": 7, "description": "X3wGCL5nVzlgh8L 09-20-2024 13:09", "order": 1, "sections": [], "id": "f49bae56-2c9e-4149-b067-12b619711d8d"}], "id": "26c16756-6eb9-4fa1-901b-37c5a42af37b"}, {"title": "hxoZSKK8rvl2cic_Program", "modules": [{"title": "NFKNdGMKirS3a2m Module", "short_title": "NFKNdGMKirS3a2m Module", "length": 7, "description": "0rjuApk8XZyjSrY 09-21-2024 13:09", "order": 1, "sections": [], "id": "cca8e667-feee-478f-b24e-171490df49d4"}], "id": "defc9eee-bc83-4560-923a-b94090c7ffed"}, {"title": "xaAgEDNyZPNjZw1_Program", "modules": [{"title": "SgHe3x5BzEyCtTb Module", "short_title": "SgHe3x5BzEyCtTb Module", "length": 7, "description": "kZwlsH4Fw6f7AdF 09-22-2024 13:09", "order": 1, "sections": [], "id": "d14649e4-830b-454c-a794-5b6fd60e3461"}], "id": "ec95bec2-b02f-43fe-ace8-5fefc0420110"}, {"title": "Blu5vXQTtaXgB8C_Program", "modules": [{"title": "fWZ7wuKR8joCPYx Module", "short_title": "fWZ7wuKR8joCPYx Module", "length": 7, "description": "W4RpDmrFLopZnU1 09-23-2024 13:09", "order": 1, "sections": [], "id": "62c51ea7-c794-4bc6-b121-fcbccf990481"}], "id": "bd2e3d5f-5c90-4208-907f-a23adb03319a"}, {"title": "BQWr7T2OoPVSCg0_Program", "modules": [{"title": "BY1h0FsefJEHcFS Module", "short_title": "BY1h0FsefJEHcFS Module", "length": 7, "description": "6HX89Y3YDOqHgDQ 09-24-2024 13:09", "order": 1, "sections": [], "id": "179a9783-4992-4159-ad66-d96afc2bd864"}], "id": "9b9e15d9-3f68-490f-a959-f24669a79f4f"}, {"title": "PIf8z5CsL3jtyLo_Program", "modules": [{"title": "mZB0jCk3VQbpT9Q Module", "short_title": "mZB0jCk3VQbpT9Q Module", "length": 7, "description": "a4m5fSOPpZpFint 09-25-2024 13:09", "order": 1, "sections": [], "id": "55dfe0cb-5614-41ad-b78b-fafe3ae8b334"}], "id": "4fc819a7-1283-4671-82ba-6dc7a96f13c7"}, {"title": "xx4svNaUWxYMK3E_Program", "modules": [{"title": "yUCCgQ1wzFL5YVw Module", "short_title": "yUCCgQ1wzFL5YVw Module", "length": 7, "description": "Ac9LDvSwieZ3RLk 09-26-2024 13:09", "order": 1, "sections": [], "id": "10e9df6f-fb14-4b38-b8e1-f698e49550ad"}], "id": "874822be-200f-470c-ba3c-e8dcf6980400"}, {"title": "Qmn58iveQuEK2A9_Program", "modules": [{"title": "jRGkEEgjpfkgAcT Module", "short_title": "jRGkEEgjpfkgAcT Module", "length": 7, "description": "kHD5QRAWpI4MXJq 09-27-2024 13:09", "order": 1, "sections": [], "id": "04bfc57d-2429-4bad-b946-0c06167528e1"}], "id": "fe68288a-99fa-4ab8-bab2-92f38eea00de"}, {"title": "kZ9eZjRIWUDzop9_Program", "modules": [{"title": "hc2Zsb84z9oyZmk Module", "short_title": "hc2Zsb84z9oyZmk Module", "length": 7, "description": "UxDJSE72fCq9Ec9 09-28-2024 13:09", "order": 1, "sections": [], "id": "4925dd56-f819-441c-98ad-07b1ac710435"}], "id": "71bb01cd-cae9-4808-a491-8c4815700809"}, {"title": "KubpegbtxbLs8C6_Program", "modules": [{"title": "k25TTk9LzQIjCEN Module", "short_title": "k25TTk9LzQIjCEN Module", "length": 7, "description": "ZyNCuxPwh3d3lcD 09-29-2024 13:09", "order": 1, "sections": [], "id": "82834182-41b0-4a88-9795-06309803ecc1"}], "id": "d7751972-f2a2-47c5-880f-ff9b3b35321c"}, {"title": "idOX1w0dfFLYhTf_Program", "modules": [{"title": "HuG1CuNVQtWon91 Module", "short_title": "HuG1CuNVQtWon91 Module", "length": 7, "description": "GKVi15zVlCmxMlk 09-30-2024 13:09", "order": 1, "sections": [], "id": "d4e0ba35-094c-4a37-a726-939f432ffbfd"}], "id": "cdd8039f-cfab-4789-bc9e-318cd5550dec"}, {"title": "4qtAz434Hd8YFaw_Program", "modules": [{"title": "dMqeBR2j13MmJvT Module", "short_title": "dMqeBR2j13MmJvT Module", "length": 7, "description": "KTPgHnYBfHMd81b 10-01-2024 13:10", "order": 1, "sections": [], "id": "f4615172-fd7d-4c8a-b73d-8ea1bbcdfb54"}], "id": "36260fa4-387d-4bba-9b1e-00a1864ec253"}, {"title": "WDtBvyXbpCwa9hU_Program", "modules": [{"title": "kfc0sMy2p6j63iD Module", "short_title": "kfc0sMy2p6j63iD Module", "length": 7, "description": "Q2hqdsD3qf67iFw 10-02-2024 13:10", "order": 1, "sections": [], "id": "34e5eda7-aa61-4eaa-937a-80935385f187"}], "id": "f5620316-a71b-4106-88d4-19c3253d0186"}, {"title": "NDPP", "modules": [{"title": "Introduction to the Program", "short_title": "Week 0/50", "length": 7, "description": "I look forward to starting this journey with you. Stay motivated on your own time by checking out the Personal Success Tool (PST) online module for this week. Stay motivated. Together we will do it!", "order": 1, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "208e689c-20e3-4120-9c77-32b860429315"}, {"title": "Introduction video: Reviewing the program", "description": "Welcome to the program! Check out our introduction video where we discuss goals, motivations, and the structure of the program. Please watch whenever you are ready to get started with us!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Solera_Intro_Video.mp4", "type": null, "form_id": null, "started_at": null}, "id": "ba32f5e4-0392-40a8-979a-446cf546668b"}, {"title": "Introduction to the Program", "description": "This introductory module that will help you change your lifestyle by moving from the thinking phase to the action phase. The module sets the stage for the entire Prevent T2 course.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_1_Introduction.pdf", "type": null, "form_id": null, "started_at": null}, "id": "8c1effa5-1eaa-43fb-b09f-76eb9e035441"}, {"title": "Set the Goals. Commit to a healthier lifestyle.", "description": "Now that you've identified your goals for the program, it's time to save your pledge for us to review in our first live session. Let's GO!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "oCwva4rN", "started_at": null}, "id": "29c070bc-2c86-4814-a94c-4b7df5778b8e"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "ad962dc2-6059-48e7-9c2b-ba8568966ac5"}, {"title": "Diana Testing URL Accpetance", "description": "You can delete if day > Aug/2024", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/super-simple-green-smoothie/", "type": "file", "form_id": "", "started_at": null}, "id": "c85a7b96-1521-4482-bb7f-0d17bb72bb98"}, {"title": "Test", "description": "Testing Typeform", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": "", "type": "type_form", "form_id": "ABCDE", "signed_url": null, "started_at": null}, "id": "61b3f156-edad-4990-b153-ee3fe93f1870"}, {"title": "Test ", "description": "Test", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "https://www.google.co.uk/", "type": "file", "form_id": "", "started_at": null}, "id": "55868837-fbf4-42ce-a091-7150393d0744"}], "id": "9cee7fc6-087b-49c1-8557-9e810bf51bd4"}, {"title": "Get Active", "short_title": "Week 1/50", "length": 7, "description": "Physical activity can help prevent or delay type 2 diabetes. This module introduces the concept of getting active. Make it fun! This is activity NOT exercise - don't for get to complete the Personal Success Tool (PST) online module for this week", "order": 2, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "c7ce80b6-127f-45e6-bcfe-ae602a6792e0"}, {"title": "Video: Get Active", "description": "Learn how to get active", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module2_get_active.mov", "type": null, "form_id": null, "started_at": null}, "id": "f0522065-de07-455c-be0a-440a86b64f1e"}, {"title": "Learn how to get active", "description": "Physical activity can help prevent or delay type 2 diabetes. This module introduces the concept of getting active", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_2_Get_Active.pdf", "type": null, "form_id": null, "started_at": null}, "id": "9e534e97-3876-4fad-8d24-ad75b960224e"}, {"title": "Get active to prevent T2", "description": "Think about how physically active you are right now. Find an activity that’s right for you and make a plan for when things get in the way.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "b5fd9ydV", "started_at": null}, "id": "7ef3773e-89ec-413e-8917-361449401b4f"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3c46b134-3c7d-4358-9edc-5772d1554104"}], "id": "c96a7bad-1da9-4bb6-a710-39d97b2c7b18"}, {"title": "Track Your Activity", "short_title": "Week 2/50", "length": 7, "description": "Tracking, or self-monitoring, can help prevent or delay type 2 diabetes. From now on you'll need to provide your activity minutes each week. Remember we're in this together so start where you feel comfortable", "order": 3, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "5c52f73e-4dc2-496c-9b41-caabc1855a63"}, {"title": "Video: Track Your Activity", "description": "Track Your Activity to Prevent T2", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module3_track_your_activity.mov", "type": null, "form_id": null, "started_at": null}, "id": "4fcb70fe-4df8-4b73-bfbe-15782386b6cb"}, {"title": "Back and Triceps Body Weight Workout", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/EXERCISE/Day_1___Back_and_Triceps_Body_Weight_Workout.pdf", "type": null, "form_id": null, "started_at": null}, "id": "4f7e75c3-2853-4d65-8fa9-1cd34dd9bc2f"}, {"title": "Track Your Activity", "description": "Tracking your physical activity is the best way to be sure you hit your goals. It helps you remember. It keeps you accountable. Itʼs the best way to see how far youʼve come!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "zarbCw89", "started_at": null}, "id": "8d15ed52-12f2-4807-8572-dca93fd8ff15"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "66e622fc-2a2e-462f-99d4-62171018cba3"}], "id": "dc4c67db-277d-4448-9245-e49edb669432"}, {"title": "Eat Well to Prevent T2D", "short_title": "Week 3/50", "length": 7, "description": "Eating well can help prevent or delay type 2 diabetes. This module includes a plate with suggested portions of food.", "order": 4, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "d462518d-823c-486d-9e53-3334da633443"}, {"title": "Video: Eat Well to Prevent T2", "description": "We'll learn about building a healthy meal and portion sizes", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module4_eat_well.mov", "type": null, "form_id": null, "started_at": null}, "id": "8b3ef4a2-828f-4ba1-90df-d449390b5dd8"}, {"title": "Eat Well to Prevent T2", "description": "This session we will talk about: How to eat well, How to build a healthy meal, The items in each food group. ", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_4_Eat_Well.pdf", "type": null, "form_id": null, "started_at": null}, "id": "b280b5f1-79aa-47f5-aad9-71558992eed3"}, {"title": "Eat well, live well", "description": "Eating well is a crucial component to creating a healthier you. Each healthy choice you make brings you one step closer to your goal! Are you ready to discover new ways to eat well?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "B0vQZYgj", "started_at": null}, "id": "99fc13c6-0ce9-4ba1-869a-208402ec4a11"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e59685c4-1a5a-4a13-b74a-db0857191210"}], "id": "63df00e1-ff90-40d0-84de-cac1e092d390"}, {"title": "Track Your Food", "short_title": "Week 4/50", "length": 7, "description": "Tracking your food is one of the best ways to really understand the foods you eat each day. Try it for this week and see what new information you find. It can be helpful to track how you feel after you eat as well.  It can help you discover if there are any foods that impact how you feel.", "order": 5, "sections": [{"title": "The Plate Method (Plate Control)", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/the-plate-method.html", "type": null, "form_id": null, "started_at": null}, "id": "d2ac6fd0-ccf7-4c7a-8179-1d51feebeab9"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "46d3fae2-a849-413a-bf24-ad742cca384d"}, {"title": "Track Your Food", "description": "Have you ever tracked your food to really see what you are eating in a day? This video will discuss ways to track your food", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module5_Track_Your_Food.mp4", "type": null, "form_id": null, "started_at": null}, "id": "0c516dc0-bb5a-4334-ae85-61b2286105a3"}, {"title": "Track Your Food to Delay T2", "description": "Tracking your food each day can help you prevent or delay type 2 diabetes. You will learn: The purpose of tracking, How to track your food, How to make sense of food labels", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_5_Track_Your_Food.pdf", "type": null, "form_id": null, "started_at": null}, "id": "e43fcd3c-29d6-4e8b-baf4-0e5e43929251"}, {"title": "Get on the right track.", "description": "The better you track what and how much you eat, the closer you will be to achieving your Six-Month Goals! Ready. Set. Go!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "RhnOkRyK", "started_at": null}, "id": "6cf7760a-a5e4-4dec-903e-9df68d2f6d04"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "15973c7c-cebc-4e58-a052-efc485d6d80b"}], "id": "088b08e2-b95c-4d5a-99ec-5e91d0d442e7"}, {"title": "Get More Active", "short_title": "Week 5/50", "length": 7, "description": "This builds on what you learned in the 2nd session and includes information on how to increase your activity in small steps.  I look forward to hearing about your favorite activities!", "order": 6, "sections": [{"title": "Article: 5 Mindless Eating", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/5-ways-to-beat-mindless-eating.html", "type": null, "form_id": null, "started_at": null}, "id": "bb96f4df-68ea-4ea7-ad45-f15771aad833"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "254492e6-c449-4fd9-9c83-48c4d436d5f8"}, {"title": "Video: Get More Active to Lose Weight", "description": "Ways to increase your activity in small steps", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module6_get_more_active.mov", "type": null, "form_id": null, "started_at": null}, "id": "2bc10045-ea23-49b8-ba1a-bd5e00cdc172"}, {"title": "Get More Active to Prevent T2", "description": "Getting more active can help you prevent or delay type 2 diabetes. This session we will talk about: The purpose of getting more active, Some ways to get more active, How to track more details about your fitness", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_6_Get_More_Active.pdf", "type": null, "form_id": null, "started_at": null}, "id": "6d5ebaad-6dcc-4093-82da-55efdf7cd9dd"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "0464c446-02ce-4645-8f65-1a00015d5a60"}], "id": "e300a031-bfbf-4925-9d5a-36759590659c"}, {"title": "Burn More Calories Than You Take In", "short_title": "Week 6/50", "length": 7, "description": "Curious about how many calories are in food and how many calories you burn during activities?  This module will help you understand those calories and ways to balance them", "order": 7, "sections": [{"title": "Recipe: Nutrition Reset Guide", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/nutrition-reset-guide.html", "type": null, "form_id": null, "started_at": null}, "id": "03a54129-4605-4cfd-870a-460af5648274"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "309b84ab-8374-483b-a70a-2c0997a68f4a"}, {"title": "Video: Burn More Calories than you Take in", "description": "We’ll discuss how to lose weight by burning more calories than you take in.", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module7_burn_more_caloriese.mov", "type": null, "form_id": null, "started_at": null}, "id": "d2a67c2e-fff0-4d37-a15b-1221860e63cf"}, {"title": "Burn More Calories Than You Take In", "description": "Losing weight can help you prevent or delay type 2 diabetes. This session we will talk about: The link between calories and weight, How to track the calories you take in, How to track the calories you burn, How to burn more calories than you take in", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_7_Burn_More_Calories_Than_You_Take_In.pdf", "type": null, "form_id": null, "started_at": null}, "id": "3cd4e86f-d5b7-475e-9d76-a40892f3631e"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "63bc73be-3891-4ff5-916c-72eabefc2578"}], "id": "04e6ea1c-ce7a-407f-b14b-35dce182d28c"}, {"title": "Healthy Food", "short_title": "Week 7/50", "length": 7, "description": "This module has health shopping and cooking tips. Sometimes it's as easy as making a simple substitution to make your favorite recipe healthier. This week's live session will be with a registered dietitian to discuss your personal plan.  Prepare by gathering your questions now.", "order": 8, "sections": [{"title": "Article: Lower Body Flexibility Workout", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-5-lower-body-flexibility-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "4a18fe2f-3ca6-4e33-b76d-75ef501cfa3e"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "4b87dd10-7662-4dd1-bd2c-4417e9d7a90c"}, {"title": "Video: <PERSON> and Cook to <PERSON><PERSON> Weight", "description": "This video discusses how to buy and cook healthy food.", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module_8_Shop_and_cook.mov", "type": null, "form_id": null, "started_at": null}, "id": "64e833be-300f-429d-8068-d003272eaf77"}, {"title": "Shop and Cook to Prevent T2", "description": "Healthy shopping and cooking can help you prevent or delay type 2 diabetes. This session we will talk about: Healthy food, How to shop for healthy food, How to cook healthy food", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_8_Shop_and_Cook_to_Prevent_T2.pdf", "type": null, "form_id": null, "started_at": null}, "id": "d09568c8-5c02-465e-a397-fccba60cacd6"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "9081ca09-03d3-4b7d-b760-00c9ced5558f"}], "id": "a1a9e30a-7749-4e02-b720-28201ca1b72b"}, {"title": "Manage Stress", "short_title": "Week 8/50", "length": 7, "description": "You Are Amazing! This week's module is about stress, it's effect on T2 and how to deal with it to live happier and healthier life.", "order": 9, "sections": [{"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "c3cb072b-2c66-402e-a4b3-3998620fe0f5"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "a66d5046-ede7-4884-9ca5-7cbea02f8593"}, {"title": "Manage Stress", "description": "Stress plays a big component in our health. Having the ability to manage stress will ultimately help with achieving your goals for this program", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module9_Manage_Stress.mp4", "type": null, "form_id": null, "started_at": null}, "id": "b92f56ff-9a5c-418e-b7d3-fe53495323d3"}, {"title": "Manage Stress", "description": "Managing stress can help you prevent or delay type 2 diabetes. This session we will talk about: Some causes of stress, The link between stress and type 2 diabetes, Some ways to reduce stress, Some healthy ways to cope with stress", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_9_Manage_Stress.pdf", "type": null, "form_id": null, "started_at": null}, "id": "1c754f8e-5034-499f-9806-d84ff222a493"}, {"title": "Stress is an everyday part of life.", "description": "But you can do something about it! Take a few minutes to practice healthy ways to manage stress so you can still reach your goals.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "XZbTO8iB", "started_at": null}, "id": "7e17060a-1cab-453a-ae5c-1f8e2c3444f5"}], "id": "e2947563-ea43-436e-a725-bf48ccbf8d57"}, {"title": "Find Time For Fitness", "short_title": "Week 9/50", "length": 7, "description": "We are doing great! This module teaches about how to find time to be active. You know that getting active is one of the key factors to prevent T2.", "order": 10, "sections": [{"title": "Article: Legs Body Weight Workout", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_HTML/day-4-legs-body-weight-workout.html", "type": null, "form_id": null, "started_at": null}, "id": "75ae9da4-6d68-466e-a475-9977b2926d23"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "42ab8d2a-1e46-440f-ae3e-fe157da99e22"}, {"title": "Finding Time for Fitness", "description": "This video will show you different ways to schedule in more fitness", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module_9_finding_time_for_fitness.mov", "type": null, "form_id": null, "started_at": null}, "id": "01c29aba-0c72-4f75-9209-0a587737e41b"}, {"title": "Find Time for Fitness", "description": "It can be challenging to fit in at least 150 minutes of activity each week. This session we will talk about: Benefits of being active, The challenge of fitting in fitness, How to find time for fitness", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_10_Find_time_For_Fitness.pdf", "type": null, "form_id": null, "started_at": null}, "id": "85541327-8627-41ff-9f37-0f0780f69734"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "650d96bd-5e57-4ee3-b8bd-14ae33931854"}], "id": "1c0e35f8-b795-4b8a-afe4-e58b9ea7ef37"}, {"title": "<PERSON> with <PERSON><PERSON><PERSON>", "short_title": "Week 10-11/50", "length": 14, "description": "How are you feeling today? This module teaches about how to cope with triggers of unhealthy behaviors.", "order": 11, "sections": [{"title": "Breathing Techni<PERSON> to Soothe the Soul", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": null, "form_id": null, "started_at": null}, "id": "ab01376a-7b21-4b81-8c22-256ff1021707"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "d101b813-045a-4705-a882-170f7e0f6fcf"}, {"title": "Coping with triggers", "description": "This video will show you different ways to cope with triggers", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module10_11_Coping_with_triggers.mov", "type": null, "form_id": null, "started_at": null}, "id": "3240d7a4-f217-4738-8317-46fac172cf34"}, {"title": "<PERSON> with <PERSON><PERSON><PERSON> to <PERSON><PERSON> Weight", "description": "Coping with triggers can help you lose weight. This session we will talk about: Some unhealthy food shopping triggers and ways to cope with them, Some unhealthy eating triggers and ways to cope with them, Some triggers of sitting still and ways to cope with them", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Participant_Module_11_<PERSON>_With_Triggers.pdf", "type": null, "form_id": null, "started_at": null}, "id": "19c62f4d-da62-4eea-bb93-f109daeb5a29"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "02043c86-a225-4206-8db8-996e38c9a228"}, {"title": "test for max changes", "description": "", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": "file", "form_id": "", "started_at": null}, "id": "5ef31eed-0b36-447b-abbe-34f1c0e881da"}, {"title": "Take Control of Your Triggers", "description": "You can take control of your triggers. The smell of a favorite dish, an argument with a loved one, baked goods in the office breakroom. When you’re learning to make healthy choices, sometimes everyday life can throw you off track", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "GL17cZVu", "started_at": null}, "id": "eaea995b-28ed-4444-bccf-6c1dcca559bf"}], "id": "cb07d9ed-c39c-42f4-8944-c3ed940f6d27"}, {"title": "Keep Your Heart Healthy", "short_title": "Week 12-13/50", "length": 14, "description": "You made that far, well done! This 2 weeks module is about why and how to keep your heart healthy. See you!", "order": 12, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "49a0b75e-5757-4d7e-ab28-207406add39b"}, {"title": "Heart Health", "description": "This video, led by one of Ciba Health physicians, will discuss ways to keep your heart healthy!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module12_Keeping_Your_Heart_Healthy.mp4", "type": null, "form_id": null, "started_at": null}, "id": "b44c4cbe-6e73-4cc5-b106-e2f3f5f37e43"}, {"title": "Keep Your Heart Healthy", "description": "Since you are at risk to have problems with your heart or arteries, it’s important to keep your heart healthy. This session we will talk about: Why heart health matters, How to keep your heart healthy, How to be heart smart about fats.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_12_Keep_Your_Heart_Healthy.pdf", "type": null, "form_id": null, "started_at": null}, "id": "e21c3171-2ec2-4281-8bcd-72ac28657cfc"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "586600e8-1d33-40b3-8ca1-71b42fa8cad9"}], "id": "03a45553-92ce-469a-b889-99d38c65c2da"}, {"title": "Take Charge of Your Thoughts", "short_title": "Week 14-15/50", "length": 14, "description": "Keep positive! This module teaches about how to replace harmful thoughts with helpful thoughts and why it matters for your health. ", "order": 13, "sections": [{"title": "Breathing Techni<PERSON> to Soothe the Soul", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": null, "form_id": null, "started_at": null}, "id": "2f24b1c8-1f01-4ae9-a72f-848afc646fd0"}, {"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "7b48d493-48a7-4006-9ab0-0bc53c918a77"}, {"title": "Take Charge of Your Thoughts", "description": "This video will discuss taking charge of your thoughts to stay positive during your weight loss journey", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module13_Take_charge_of_your_thoughts.mov", "type": null, "form_id": null, "started_at": null}, "id": "7d87ad80-9a07-41ef-912c-4fe5808a230a"}, {"title": "Take Charge of Your Thoughts", "description": "Taking charge of your thoughts can help you lose weight. This session we will talk about: The difference between harmful and helpful thoughts, How to replace harmful thoughts with helpful thoughts", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_13_Take_Charge_Of_Your_Thoughts.pdf", "type": null, "form_id": null, "started_at": null}, "id": "3d5d5bbf-6eec-4426-a758-a04c3e8df037"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "c679dbe7-8657-466c-87c8-cc094a103069"}], "id": "32a44b70-58d7-4f0d-af3e-386dde7744dc"}, {"title": "Get Support", "short_title": "Week 16-17/50", "length": 14, "description": "Hey! You made it today! This module teaches about how to get support for healthy lifestyle and why it matters.", "order": 14, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "a90759ca-8def-4c41-aa9a-42b80cfbf948"}, {"title": "Get Support", "description": "Getting support for your healthy lifestyle can help you lose weight. This session we will talk about how to get support from Family, friends, and coworkers, Groups, classes, and clubs, Professionals", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_14_Get_Support.pdf", "type": null, "form_id": null, "started_at": null}, "id": "2fe1cd82-a936-44aa-994c-8bb5d6f06872"}, {"title": "Get Support. Make Lasting Changes.", "description": "To make sure small steps become true lifestyle changes, you need to build a strong support network. Can you count on friends? Talk to family members? Could a doctor or counselor help you succeed?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "vVN65cCn", "started_at": null}, "id": "0c8a3083-80bf-4074-ae84-59690138b0ce"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "0dcafbc1-20ff-4eab-9739-a5891f814ccd"}, {"title": "Get support", "description": "In this video you will start to think about how to surround yourself with a support system for your health journey", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "programs/NDPP/video/src/Get Support (edited).mp4", "type": "video", "form_id": null, "signed_url": null, "started_at": null}, "id": "e4ed39b5-71b4-429c-8990-7e473e599f61"}, {"title": "Test", "description": "", "activity_type": "recipe_type", "activity_category": "activity", "metadata": {"url": "https://cibahealth.com/knowledgebase/breathing-techniques-to-soothe-the-soul/", "type": "file", "form_id": "", "started_at": null}, "id": "6512e327-0421-4637-91ba-73f8b4689276"}], "id": "f002abc2-0baf-485a-b59e-dc6193f2aab2"}, {"title": "Eat Well Away from Home", "short_title": "Week 18-19/50", "length": 14, "description": "Hey! This module teaches about how to stay on track with eating goals at restaurants and social events.", "order": 15, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "3cb7fac2-96c0-4cbe-959b-661e6920a1bb"}, {"title": "Eat Well Away From Home", "description": "This video will discuss tips and tracks when eating away from home", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module15_Eat_Well_Away_From_Home.mp4", "type": null, "form_id": null, "started_at": null}, "id": "21980012-6892-49e9-ac5e-29a00ecaa380"}, {"title": "Eat Well Away from Home", "description": "We will talk about: Some challenges of eating well at restaurants and social events, How to plan for and cope with these challenges.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_15_Eat_Well_Away_From_Home.pdf", "type": null, "form_id": null, "started_at": null}, "id": "7d8cea21-f045-4bf8-9b88-49daa342acf1"}, {"title": "Eat well wherever you are", "description": "Whether you are out with friends, having a lunch break on the go, or are heading to a sporting event, sticking to your healthy eating goals when you’re away from home can be a major challenge. But with the right skills and mindset, you can enjoy yourself when you’re out and about and still be healthy!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "yHBCTi79", "started_at": null}, "id": "9a6debc1-7a7e-4142-baf1-55baa8bb9929"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "9e4eb383-4b1a-475c-bcdb-57d2e3ef292e"}], "id": "4190d3eb-9764-4b69-9571-48ca03d4a810"}, {"title": "Stay Motivated to Lose Weight", "short_title": "Week 20-21/50", "length": 14, "description": "You've come a long way in the program. This module helps you reflect on your progress and keep making positive changes over the next six months.", "order": 16, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "150a3585-0827-4d9e-8e7c-79769cc995e0"}, {"title": "Stay Motivated", "description": "Stay motivated in this journey, you got this! This video will discuss staying motivated to the end", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module16_Stay_Motivated.mp4", "type": null, "form_id": null, "started_at": null}, "id": "fce4171c-ec6d-4fef-9d4c-109e7938ff9e"}, {"title": "Stay Motivated to Lose Weight", "description": "In this session we will talk about: How far you’ve come since you started this program, Our next steps, Your goals for the next six months.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_16_Stay_Motivated_To_Prevent_T2.pdf", "type": null, "form_id": null, "started_at": null}, "id": "02906b7e-6bc4-4a80-9b57-0ac00bfac1f3"}, {"title": "Stay motivated to make a lasting change.", "description": "The next six months are just as important. Whether you've taken a few small steps to adopt healthier habits or reached a major milestone on the scale, don't stop now!", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "cH0ll5Li", "started_at": null}, "id": "8c355e9d-8b83-453b-9ceb-f60695d4a49f"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "e3ffd7e0-efac-40fc-a3b3-ef2a03c041f9"}], "id": "49fc816f-b196-43bf-b6ca-f4f0f21fc29a"}, {"title": "When Weight Loss Stalls", "short_title": "Week 22-23/50", "length": 14, "description": "Hey! This module encourages You to pause and reflect on experiences, reset goals, and refresh daily routines to stay motivated on your weight loss journey.", "order": 17, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "78fd6514-de90-4d0b-845e-a1ef477c5f9b"}, {"title": "When Weight Loss Stalls", "description": "Sometimes it feels like our weight loss can hit a plateu. In this video, our registered dietitian will discuss how to push through this wall!", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module17_When_Weight_Loss_Stalls.mp4", "type": null, "form_id": null, "started_at": null}, "id": "c5e51bb9-25d2-4da7-9f10-dc59c7eed50f"}, {"title": "When Weight Loss Stalls", "description": "In this session we will talk about: Why weight loss can stall, How to start losing weight again", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_17_When_Weight_Loss_Stalls.pdf", "type": null, "form_id": null, "started_at": null}, "id": "d0386005-8868-4aba-aaad-11a88726fdcd"}, {"title": "Recommit to yourself. Make healthy living a habit.", "description": "Now that you’re a few weeks into your program, it’s important to stop and reflect on your goals. What has been working? What has been holding you back?", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "axirq6WH", "started_at": null}, "id": "da887089-08aa-4006-81c2-0695333b8157"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "27c95749-a52a-47aa-8bc1-273254c33607"}], "id": "c53e0c95-d722-4755-8eb6-29a6b89c0261"}, {"title": "Take a Fitness Break", "short_title": "Week 24-25/50", "length": 14, "description": "You made so far! This module teaches about how to overcome barriers to taking a 2-minute fitness break every 30 minutes.", "order": 18, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "19d643bb-e093-4b1b-a162-4cf5ce0b1c50"}, {"title": "Time for a Fitness Break", "description": "This is a marathon, not a sprint! This video will discuss how you can incorporate small fitness breaks into your everyday routine", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/NDPP_Module18_Take_a_fitness_break.mp4", "type": null, "form_id": null, "started_at": null}, "id": "a81e495c-836b-47d0-a3dd-2448a419545a"}, {"title": "Take a Fitness Break", "description": "Taking a 2-minute fitness break every 30 minutes can help you lose weight.\nThis session we will talk about: The link between sitting still and type 2 diabetes, Some challenges of taking fitness breaks and ways to cope with them.", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_18_Take_a_Fitness_Break.pdf", "type": null, "form_id": null, "started_at": null}, "id": "7c6ed5ef-6e74-4099-90db-2f848201f6da"}, {"title": "Are you sitting down as you read this?", "description": "We spend so much time sitting—in the car, at work, relaxing at home. Did you know that sitting still for too long can lead to health problems? The good news is that taking short activity breaks every 30 minutes can make a big difference.", "activity_type": "personal_success", "activity_category": "activity", "metadata": {"url": null, "type": null, "form_id": "isEZatYX", "started_at": null}, "id": "abd727e0-5585-4305-854f-d189cc55a856"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "540381d3-cc9b-4116-a6cf-07d9aa861141"}], "id": "1fa039a7-32e3-4de3-a443-19552d532c56"}, {"title": "Stay Active to Lose Weight", "short_title": "Week 26-27/50", "length": 14, "description": "Hey! This module teaches about how to cope with some challenges of staying active. Keep going!", "order": 19, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "c954ccec-a7c8-4e58-90c1-0f59a0e383ff"}, {"title": "Stay Active to Lose Weight", "description": "This video will provide encouragement while finding motivation to stay active in your journey", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module19_Stay_Active_to_Lose_Weight.mp4", "type": null, "form_id": null, "started_at": null}, "id": "40744198-a622-4278-a896-46f47cb810ca"}, {"title": "Stay Active to Lose Weight", "description": "This session we will talk about: Some benefits of staying active, Some challenges of staying active and ways to cope with them and How far you’ve come since you started this program", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_19_Stay_Active_To_Prevent_T2.pdf", "type": null, "form_id": null, "started_at": null}, "id": "fca9b727-14ed-41f5-b916-3385bef2e4bf"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "aeb4ea83-425a-4090-8aad-20222c479072"}], "id": "2ec70ad7-8537-481b-abf8-82d63bf531ca"}, {"title": "Stay Active Away from Home", "short_title": "Week 28-29/50", "length": 14, "description": "You made it so far! This module teaches about how to overcome barriers to staying active when you aren't at home", "order": 20, "sections": [{"title": "Add your current weight", "description": "Please use this field to log your current weight while your scale is being shipped. Once it arrives and is set up, this process will be automated. Assistance with the set up will be available.", "activity_type": "weight_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "0cd9f4d5-dff7-4285-a65a-d7562a430ea5"}, {"title": "Stay Active Away from Home", "description": "This video will discuss how to stay active away from home, cope with challenges, and planning for success", "activity_type": "video_type", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_VIDEO/Module20_Stay_active_away_from_home.mp4", "type": null, "form_id": null, "started_at": null}, "id": "74fbeb08-b1ee-41f8-b5d0-5cee03952c66"}, {"title": "Stay Active Away from Home", "description": "Staying active away from home can help you lose weight. In this session we will talk about challenges of staying active away from home, and ways to cope with them", "activity_type": "curriculum", "activity_category": "activity", "metadata": {"url": "course_materials/NDPP_V1/Module_20_Stay_Active_Away_From_Home.pdf", "type": null, "form_id": null, "started_at": null}, "id": "caf7008f-32b1-4f3e-b0a8-cc0a1bafbd87"}, {"title": "Log your physical activity", "description": "Please use this field to track you weekly activity minutes! Remember, try to work your way to 150 minutes of moderate activity a week to help you lose weight and feel great!", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": null, "type": "input", "form_id": null, "started_at": null}, "id": "9848fae5-6443-4b41-a1ec-d108ffb1a081"}], "id": "e7086539-d861-4c09-b5ed-21c78ab2d9eb"}, {"title": "To Be Filled", "short_title": "Week 30-31/50", "length": 14, "description": "To Be Filled", "order": 21, "sections": [], "id": "a5a52fff-e057-4f51-a2db-abbad6c8da87"}, {"title": "To Be Filled", "short_title": "Week 32-35/50", "length": 28, "description": "To Be Filled", "order": 22, "sections": [], "id": "23eb1117-49ab-4591-ab85-dbf86e479a4c"}, {"title": "To Be Filled", "short_title": "Week 36-39/50", "length": 28, "description": "To Be Filled", "order": 23, "sections": [], "id": "8910013f-0749-4fe9-a000-5a67a0d5a6d9"}, {"title": "To Be Filled", "short_title": "Week 40-43/50", "length": 28, "description": "To Be Filled", "order": 24, "sections": [], "id": "7d6d38fc-88de-46d4-bc0d-a4989fe5e9c3"}, {"title": "To Be Filled", "short_title": "Week 44-47/50", "length": 28, "description": "To Be Filled", "order": 25, "sections": [], "id": "556d525f-23ad-4e73-98b6-c794821c9408"}, {"title": "To Be Filled", "short_title": "Week 48-51/50", "length": 28, "description": "To Be Filled", "order": 26, "sections": [], "id": "cf5cc1d4-8acf-4351-8c94-113734607d0a"}], "id": "929f945d-604f-4684-b7a2-0c5223aa33f4"}, {"title": "program_ab", "modules": [{"title": "Week 1/20", "short_title": "ab_mod_1", "length": 14, "description": "Checking", "order": 1, "sections": [{"title": "ab_phy_1", "description": "", "activity_type": "activity_type", "activity_category": "activity", "metadata": {"url": "", "type": "user_input", "form_id": "", "started_at": null}, "id": "6476c276-c896-4d0a-a5b7-799e91e65392"}], "id": "8d1de0d4-e5d0-4561-9a81-3ad947e7d4af"}], "id": "fff225da-e940-4399-9a77-a7b5cd325dab"}, {"title": "PH0t4n2vbuK22E4_Program", "modules": [{"title": "5e4i8cScRPlHQCb Module", "short_title": "5e4i8cScRPlHQCb Module", "length": 7, "description": "CXhJ6iaP2lWI9Wt 10-03-2024 13:10", "order": 1, "sections": [], "id": "16122eda-8c59-4537-95ee-5b1b5494038a"}], "id": "8c95ac8b-b358-4939-a9ba-eee69a4d39e7"}, {"title": "9pE9kzzwq8SEyVi_Program", "modules": [{"title": "4VhlAxSiYq8JI39 Module", "short_title": "4VhlAxSiYq8JI39 Module", "length": 7, "description": "sPJ2TM8CiS3MRPA 10-04-2024 13:10", "order": 1, "sections": [], "id": "3afd9391-176f-4e0a-98b5-07469bd0f530"}], "id": "0e077990-736a-4689-9891-34b55e90657a"}, {"title": "OOnL2iQatazzDgK_Program", "modules": [{"title": "Kc9Yxmk2wBBcECg Module", "short_title": "Kc9Yxmk2wBBcECg Module", "length": 7, "description": "Gxa1u1aUX3OD8uV 10-05-2024 13:10", "order": 1, "sections": [], "id": "80b140f3-733b-4624-8839-4cc1f2d01740"}], "id": "3507d6ab-e730-4dc8-926a-9d0cdee414b6"}, {"title": "3fXWMJ3pI8xhuom_Program", "modules": [{"title": "40SiKfgRWpcCKVz Module", "short_title": "40SiKfgRWpcCKVz Module", "length": 7, "description": "DPHm6Q5k5lOoR50 10-06-2024 13:10", "order": 1, "sections": [], "id": "1c038662-e53c-43ad-b745-6a4b81e93010"}], "id": "be2ac215-1238-4318-8cb7-9813cf631fe4"}, {"title": "mx61JPlmJBlvETg_Program", "modules": [{"title": "Y3iJDYe3ixuDR4v Module", "short_title": "Y3iJDYe3ixuDR4v Module", "length": 7, "description": "VxHOhceBI0x6NXg 10-07-2024 13:10", "order": 1, "sections": [], "id": "1070db79-a2ce-4e1d-ba8a-0fa27ec9918f"}], "id": "cc671d69-79e1-4bc0-8754-4ace68d6380b"}, {"title": "MjAWaVVLoAHeAYE_Program", "modules": [{"title": "42vErViajaJ4K1G Module", "short_title": "42vErViajaJ4K1G Module", "length": 7, "description": "GRxMwAqvlrrkfa0 10-08-2024 13:10", "order": 1, "sections": [], "id": "e83965cc-ed29-4e1d-b186-402f46d6cf5a"}], "id": "2f0c7176-72e8-4677-bb98-4ddb8778985a"}, {"title": "RnqVUiDe8iY64mp_Program", "modules": [{"title": "kmfEzsIZPDJbGO3 Module", "short_title": "kmfEzsIZPDJbGO3 Module", "length": 7, "description": "pTXXbrCLEr1Im2q 10-09-2024 13:10", "order": 1, "sections": [], "id": "ea4fcf0e-1c14-4e52-9fec-ba7388aa3868"}], "id": "fc0b9ae2-dc70-4e7e-817a-bc620d60fd2a"}, {"title": "fOepJYmPeo291rt_Program", "modules": [{"title": "JEYr9p4OkW3tOc3 Module", "short_title": "JEYr9p4OkW3tOc3 Module", "length": 7, "description": "A96aGGxMItT502a 10-10-2024 13:10", "order": 1, "sections": [], "id": "6896a564-9be2-4437-bd71-5ee25d5f89fc"}], "id": "2d6a0916-9c9c-46b7-a80d-38b4a5b96452"}, {"title": "MwVgu7RIIijmTME_Program", "modules": [{"title": "UxuJvm5r332cke7 Module", "short_title": "UxuJvm5r332cke7 Module", "length": 7, "description": "q3HhLAjVhQeWJMh 10-11-2024 13:10", "order": 1, "sections": [], "id": "1b9d281a-7326-47ef-a6c1-4bef25c51cbe"}], "id": "334822e9-4f69-462c-9a6a-721090e4b268"}, {"title": "og4oQRzZSeZBSkP_Program", "modules": [{"title": "gk9QST2pEAY6AWl Module", "short_title": "gk9QST2pEAY6AWl Module", "length": 7, "description": "anYM9G6Hf73Sysf 10-12-2024 13:10", "order": 1, "sections": [], "id": "9da26cd1-b5a1-4e5f-b0f9-e406b414842f"}], "id": "4af3272f-732e-4f7b-a5dd-83aa5c1b17e5"}, {"title": "DI28a7pwlkXPQyA_Program", "modules": [{"title": "2zM6a1JfKTDeU3R Module", "short_title": "2zM6a1JfKTDeU3R Module", "length": 7, "description": "iPyeskAM4yBu9Yn 10-13-2024 13:10", "order": 1, "sections": [], "id": "20dd900b-36a8-41f1-a1a0-5e8a04b1bea1"}], "id": "c76f235b-3aef-4e9e-8366-1b324a481978"}, {"title": "lGvpOUQWmUe5lcQ_Program", "modules": [{"title": "Q1r0C8bZ0h7rIOG Module", "short_title": "Q1r0C8bZ0h7rIOG Module", "length": 7, "description": "6aqcnlxEXTZjYS7 10-14-2024 13:10", "order": 1, "sections": [], "id": "85eb2eaa-6bef-4922-8206-feca3bb37c85"}], "id": "c22ddee4-3551-42e5-8c1e-18adfde74e21"}, {"title": "2Rtt7AQNvTpq0rp_Program", "modules": [{"title": "A04jVdSmxf85bfo Module", "short_title": "A04jVdSmxf85bfo Module", "length": 7, "description": "nMnr1wxV6uDYHlR 10-15-2024 13:10", "order": 1, "sections": [], "id": "5c0e599a-7d27-4b9e-9f85-9451e7aa1d21"}], "id": "6065da0f-6630-4326-a129-cd92434684ff"}, {"title": "KdammqIEO8TmaqB_Program", "modules": [{"title": "YMbJT2oBvDV8ADz Module", "short_title": "YMbJT2oBvDV8ADz Module", "length": 7, "description": "UzgzNpDKsTXSRAc 10-16-2024 13:10", "order": 1, "sections": [], "id": "e29e05b7-0be3-4ab7-97d3-65f402c6a734"}], "id": "ba8858ae-3116-451f-a81a-b93abd4e2617"}, {"title": "XBzcZI7ORVOVOGr_Program", "modules": [{"title": "8UhtsEiQQRwvakO Module", "short_title": "8UhtsEiQQRwvakO Module", "length": 7, "description": "xmnstdMoLvqwGZm 10-17-2024 13:10", "order": 1, "sections": [], "id": "c40fddbf-0347-4e90-8be9-310e99cfe942"}], "id": "96f8aeff-016e-499f-b9b6-9fb8e85f3946"}, {"title": "mMShaOwvEojN4VM_Program", "modules": [{"title": "wISvGVELykae5VQ Module", "short_title": "wISvGVELykae5VQ Module", "length": 7, "description": "YBlpdEGx9xxJPjX 10-18-2024 13:10", "order": 1, "sections": [], "id": "936828b3-d5a5-4b4a-a8b5-403dcb0a52ac"}], "id": "2cb92b05-321a-42ad-a67b-c1754006fe51"}, {"title": "5yKEeYouq26kxfM_Program", "modules": [{"title": "asw94h475sghNzq Module", "short_title": "asw94h475sghNzq Module", "length": 7, "description": "DWz2T4DMGAfsEgn 10-19-2024 13:10", "order": 1, "sections": [], "id": "87ef12e1-4bca-45d8-9a8e-8b19eec0c255"}], "id": "98264aed-29ba-4128-9e62-55f422c32388"}, {"title": "D2j4R0mBJ0Lm359_Program", "modules": [{"title": "AIaHqSuaj5rRFeo Module", "short_title": "AIaHqSuaj5rRFeo Module", "length": 7, "description": "xe6iGP3m1Zb0Wat 10-20-2024 13:10", "order": 1, "sections": [], "id": "d1ac57b1-fbb6-4877-a6ff-6e29653dfd63"}], "id": "f78de246-25b7-4b56-9824-616619ce309e"}, {"title": "2eWbx0DCLkoPi9J_Program", "modules": [{"title": "1hfnyxocxwpAWxm Module", "short_title": "1hfnyxocxwpAWxm Module", "length": 7, "description": "SyUy9j5xJprmQy9 10-21-2024 13:10", "order": 1, "sections": [], "id": "0183e1b9-9bdf-4348-98b7-313379f122fb"}], "id": "49ada955-bc83-4fa2-9934-f068f0e2d48e"}, {"title": "WZOicj9sLCaTLAa_Program", "modules": [{"title": "yi6Xyg979enYkOi Module", "short_title": "yi6Xyg979enYkOi Module", "length": 7, "description": "eMKsoYXAFOKjloJ 10-22-2024 13:10", "order": 1, "sections": [], "id": "1bfc85b4-9d2b-4e3f-b00f-2b2eb06ab54c"}], "id": "d842f050-b688-40e5-be72-732d30aa25de"}, {"title": "6G5WdPURHvkUbZR_Program", "modules": [{"title": "WCdMk4hXptN7CBm Module", "short_title": "WCdMk4hXptN7CBm Module", "length": 7, "description": "TZGeLJ8gcZvcGsF 10-23-2024 13:10", "order": 1, "sections": [], "id": "b7409b46-6582-499d-bd76-7cda19b88481"}], "id": "663fb47b-cd0b-4dac-9dde-d612f5342dc8"}, {"title": "aBZRGyewDLsbuiV_Program", "modules": [{"title": "ug0lBVC0h4o5v8C Module", "short_title": "ug0lBVC0h4o5v8C Module", "length": 7, "description": "G8u1kYEF9itv4Kh 10-24-2024 13:10", "order": 1, "sections": [], "id": "55b1cdcb-693a-44fe-b7ee-2b5edf018219"}], "id": "9f42e92e-5ee7-49f4-82b3-765ee31b0479"}, {"title": "EwXGk6H1jkvuR2l_Program", "modules": [{"title": "wBJ6LqcNOWYf1nT Module", "short_title": "wBJ6LqcNOWYf1nT Module", "length": 7, "description": "WQOfPHhwqb40KtP 10-25-2024 13:10", "order": 1, "sections": [], "id": "c3e61a64-3558-46f6-bf99-532af2533611"}], "id": "cc54c40a-9085-4f42-8a15-40094957b113"}, {"title": "1vXB0C6rhgshqYw_Program", "modules": [{"title": "enWSIIssYKXmlKO Module", "short_title": "enWSIIssYKXmlKO Module", "length": 7, "description": "RjTMqiBdc6kRR65 10-26-2024 13:10", "order": 1, "sections": [], "id": "2a8da501-587d-49f0-a30f-37633ec55dd2"}], "id": "f0be46ab-6425-4384-8310-e28376e8564f"}, {"title": "UWPm3Bw1bo5MPvz_Program", "modules": [{"title": "NfjSUGU75MmqjsK Module", "short_title": "NfjSUGU75MmqjsK Module", "length": 7, "description": "JCBqJcpzkhpxYED 10-27-2024 13:10", "order": 1, "sections": [], "id": "c4695a3b-4487-4bd3-9405-5f42ebf9cd67"}], "id": "6ea15278-9664-464e-a263-90fd25ce81e9"}, {"title": "rkUzxO4cQIzQWFU_Program", "modules": [{"title": "hj2xUlvTOQfsPmC Module", "short_title": "hj2xUlvTOQfsPmC Module", "length": 7, "description": "ItGorzGb3WTdnPy 10-28-2024 13:10", "order": 1, "sections": [], "id": "f61106fe-08a9-475a-b050-67a20e133d48"}], "id": "f87877ed-9817-47f3-a556-f88deac972ac"}, {"title": "T9d9GMDWe6TQZOm_Program", "modules": [{"title": "GCNh8n72jPO2SxQ Module", "short_title": "GCNh8n72jPO2SxQ Module", "length": 7, "description": "xMCDS0M7Lk03JnX 10-29-2024 13:10", "order": 1, "sections": [], "id": "cbb678c4-0fad-4ce9-b9b4-655616791ba3"}], "id": "a06be509-ebcf-4e60-b4a6-5bab8ec24d15"}, {"title": "xWsr8lK5PqLtgCd_Program", "modules": [{"title": "aAToIqAYL8Ffi7g Module", "short_title": "aAToIqAYL8Ffi7g Module", "length": 7, "description": "2RHMBnzfQJmOQ4T 10-30-2024 13:10", "order": 1, "sections": [], "id": "092b6135-b4a3-415f-be32-f86583a4e410"}], "id": "1fb9f9dc-ce1d-4634-8116-dc398a0803c9"}, {"title": "iAUmzm7q9tsu5fQ_Program", "modules": [{"title": "u3IwG0aLAuiOPvy Module", "short_title": "u3IwG0aLAuiOPvy Module", "length": 7, "description": "qbTZ0RGQJQgaMrW 10-31-2024 13:10", "order": 1, "sections": [], "id": "513ae701-6735-4da3-a1ba-a5223101e065"}], "id": "deff4a23-06a8-4d2e-bd48-851c2685b0f2"}, {"title": "qObjlOOY1cKya9X_Program", "modules": [{"title": "5aUhLZXySijrtbc Module", "short_title": "5aUhLZXySijrtbc Module", "length": 7, "description": "WhcvERTpsSI37tg 11-01-2024 13:11", "order": 1, "sections": [], "id": "09b12e79-ba8c-426f-99e4-873b5ef8eb7f"}], "id": "773f5dc3-f56f-44b5-9b90-ec56425d6a6e"}, {"title": "k8Xwm4uD2Alsr2E_Program", "modules": [{"title": "2916wip8AMhtX2d Module", "short_title": "2916wip8AMhtX2d Module", "length": 7, "description": "oeSOeIGFBSvqZmS 11-02-2024 13:11", "order": 1, "sections": [], "id": "211bc22f-7265-46dc-bb19-f014d8f556b0"}], "id": "39cd0f71-a511-42b3-b11d-327dd0cbd24c"}, {"title": "xlFLMdhJAA6ZzPp_Program", "modules": [{"title": "wZIyJM0DY28PX0E Module", "short_title": "wZIyJM0DY28PX0E Module", "length": 7, "description": "kYryyiUH4mYPW84 11-03-2024 13:11", "order": 1, "sections": [], "id": "a720b7cf-92e4-492a-a0eb-98c990e9d8a2"}], "id": "b509357d-1ff0-4d66-94d5-d054fe187a54"}, {"title": "d7J2PkYCWBsWABo_Program", "modules": [{"title": "O23vh8HFsJ7C4Ty Module", "short_title": "O23vh8HFsJ7C4Ty Module", "length": 7, "description": "e7Bz1nle0jhxO0W 11-04-2024 13:11", "order": 1, "sections": [], "id": "3436855d-5fc6-4235-b73d-34d42ca8fc47"}], "id": "3749b068-393b-486b-a33b-b1673cad3549"}, {"title": "OGvX8WZGyRtJexI_Program", "modules": [{"title": "FjKfLJxmCKPhkf4 Module", "short_title": "FjKfLJxmCKPhkf4 Module", "length": 7, "description": "IJlMF4jFZW5TYx6 11-05-2024 13:11", "order": 1, "sections": [], "id": "a0c22782-88a2-49bc-97c9-151d1909aa8f"}], "id": "01fb95b9-e0c7-4514-ae15-dcf6c3a0f50f"}, {"title": "7rhXB92h8RLgUWY_Program", "modules": [{"title": "qOspUszxTtN77uh Module", "short_title": "qOspUszxTtN77uh Module", "length": 7, "description": "4x1oz7RjSAblxJl 11-06-2024 13:11", "order": 1, "sections": [], "id": "0f9b3ca4-5fd9-4395-8882-451bc630b635"}], "id": "e5bb55b7-c5f7-4268-8786-396f2378d031"}, {"title": "g3uiWX0D8VBQph6_Program", "modules": [{"title": "WPxoltnAkwS9dM6 Module", "short_title": "WPxoltnAkwS9dM6 Module", "length": 7, "description": "7Ot4trf8aPFW5l5 11-07-2024 13:11", "order": 1, "sections": [], "id": "1f4a4936-9091-4ae8-baa8-23ace0cc2756"}], "id": "24ce9253-3a3c-48b9-ab2e-c473ce8c3579"}, {"title": "yEhJPzp6pzMI4Ys_Program", "modules": [{"title": "ybtTdF7Gl2COpzJ Module", "short_title": "ybtTdF7Gl2COpzJ Module", "length": 7, "description": "nzFH1lYTqXJGfUL 11-08-2024 13:11", "order": 1, "sections": [], "id": "4f635834-2120-4bb8-871e-425ae5d6512a"}], "id": "b7bd8948-15d3-4482-a398-d7e69b36a9b1"}, {"title": "4sFcMacaHg2pu3L_Program", "modules": [{"title": "rDtTACrLMx2KQ1u Module", "short_title": "rDtTACrLMx2KQ1u Module", "length": 7, "description": "vK7wyDUslDfKY5L 11-09-2024 13:11", "order": 1, "sections": [], "id": "ad5e17b4-6302-4efa-abb0-0b53fabab895"}], "id": "92cab9da-fde4-4e15-8e15-677fa30877b6"}, {"title": "kM7IVchgvHNfGUv_Program", "modules": [{"title": "rTmPxPJMHxO8cQb Module", "short_title": "rTmPxPJMHxO8cQb Module", "length": 7, "description": "D2JWqYlZmMJn4Yw 11-10-2024 13:11", "order": 1, "sections": [], "id": "68b1f882-696b-4cf0-97f3-2f2af3167bf1"}], "id": "72d866ca-2ef4-4379-a7d5-052b2de826c2"}, {"title": "mg2O3hlu50p8Lc3_Program", "modules": [{"title": "L1u8aFYdRac0Zog Module", "short_title": "L1u8aFYdRac0Zog Module", "length": 7, "description": "xevkBqygpnE1u7p 11-11-2024 13:11", "order": 1, "sections": [], "id": "1e51045c-9d75-4130-ab10-49a26d4417a9"}], "id": "2f691f15-0330-41de-a0d2-5abdd2338298"}, {"title": "s9ZRatfSSeLYEk1_Program", "modules": [{"title": "hgHlFQjukl6Rpiu Module", "short_title": "hgHlFQjukl6Rpiu Module", "length": 7, "description": "mHASzUQIW4mJG9D 11-12-2024 13:11", "order": 1, "sections": [], "id": "848fb33d-c5d1-429c-aaba-4e35d79086ac"}], "id": "cd11dacd-0c7a-4c6f-9f15-6e2e3cd66e0e"}, {"title": "UIiUEVsBzJr0jnS_Program", "modules": [{"title": "CcLQF5eDOmZCUWh Module", "short_title": "CcLQF5eDOmZCUWh Module", "length": 7, "description": "bet4Vrq0Q2ezxWv 11-13-2024 13:11", "order": 1, "sections": [], "id": "95f88f26-9db1-44c6-aafc-28583e6b4885"}], "id": "fe0d6966-53f7-43ea-b380-890c5ed2a3a6"}, {"title": "XDMx4tBH7Zxbq2m_Program", "modules": [{"title": "gBwg5bHvdONk0K9 Module", "short_title": "gBwg5bHvdONk0K9 Module", "length": 7, "description": "qu3IU2R9VAGwAxd 11-14-2024 13:11", "order": 1, "sections": [], "id": "c1d211b1-d936-403b-b0ea-132b2495975a"}], "id": "35af31b8-6222-4d26-b285-7745f1f8a03b"}, {"title": "KMUqhpLRxjpUxfL_Program", "modules": [{"title": "mDqSIuwrPyURfWK Module", "short_title": "mDqSIuwrPyURfWK Module", "length": 7, "description": "PXCfMrsuqPdKAhs 11-15-2024 13:11", "order": 1, "sections": [], "id": "c0ae1263-62e2-4da4-86b1-55bdcba54f26"}], "id": "7eea99c3-7d9c-4af2-a36d-eb1143e2a646"}, {"title": "FfGa2eNpa2OcKW3_Program", "modules": [{"title": "ouq8QfM8KQ25Ozq Module", "short_title": "ouq8QfM8KQ25Ozq Module", "length": 7, "description": "44WStjqATeyIsmi 11-16-2024 13:11", "order": 1, "sections": [], "id": "04e16c5a-3613-44aa-8ce4-242507b95f00"}], "id": "b4d15e66-ec4a-4cab-84f8-9662ef474a07"}, {"title": "BUGmmru6m6vmlyu_Program", "modules": [{"title": "lim1az6EGqPpFCL Module", "short_title": "lim1az6EGqPpFCL Module", "length": 7, "description": "qyxi9PtzyJvztZf 11-17-2024 13:11", "order": 1, "sections": [], "id": "1343b09e-8904-4ca1-b14f-c11577039e9e"}], "id": "2c1fc334-c509-4c20-980e-550f4c568055"}, {"title": "hf2Fri7HKys2cfM_Program", "modules": [{"title": "jQOGgisZp5iWEvo Module", "short_title": "jQOGgisZp5iWEvo Module", "length": 7, "description": "DXPKvc0XCE5uErO 11-18-2024 13:11", "order": 1, "sections": [], "id": "d017ce7d-ee21-4437-ac67-6421941b3520"}], "id": "f6cce8e4-50e4-42a7-9a3e-9a5f506af55c"}, {"title": "5zyNcbbyFEQRxpY_Program", "modules": [{"title": "sMF42khOoWHEJRn Module", "short_title": "sMF42khOoWHEJRn Module", "length": 7, "description": "dlSlHBdnoRyV7SN 11-19-2024 13:11", "order": 1, "sections": [], "id": "3b9c0cee-4d54-4db6-b5f0-7cb4bdb78b48"}], "id": "f1f6b76d-dad3-4ee1-a768-e95223f30a65"}, {"title": "KdmtpVLFc9FgRRZ_Program", "modules": [{"title": "yrsIW4OXpHH6aQM Module", "short_title": "yrsIW4OXpHH6aQM Module", "length": 7, "description": "OZrkH6owcUkRDVD 11-20-2024 13:11", "order": 1, "sections": [], "id": "1e46456f-1380-4618-9ff2-b0a5a4462e6a"}], "id": "072ea419-daaa-4a8e-a916-ed99d843c885"}, {"title": "tPrkhCw6zoksVLz_Program", "modules": [{"title": "YMUwkaCcpFsYvQs Module", "short_title": "YMUwkaCcpFsYvQs Module", "length": 7, "description": "v810BWMyc1mfJGs 11-21-2024 13:11", "order": 1, "sections": [], "id": "9406692f-ed31-4af1-891d-612942c2e8d9"}], "id": "ef91a803-b5e2-48bf-91bb-bf806166aed0"}, {"title": "wMbJUYh6ejbWAQj_Program", "modules": [{"title": "e6vB1cudLX27UsM Module", "short_title": "e6vB1cudLX27UsM Module", "length": 7, "description": "kI0Gwdt4TIgU6e0 11-22-2024 13:11", "order": 1, "sections": [], "id": "3bc1b4ea-18ed-4bde-8969-2f1858b80c67"}], "id": "43feefb2-fd12-4b19-905c-bb063d53364d"}, {"title": "jxSadxKDubUDPD0_Program", "modules": [{"title": "Ygkl4xhOKhAhxjy <PERSON>", "short_title": "Ygkl4xhOKhAhxjy <PERSON>", "length": 7, "description": "LjcLmIC0IySQLFr 11-23-2024 13:11", "order": 1, "sections": [], "id": "44cb4f47-43dc-44f6-9a6f-cc6fc884937f"}], "id": "8d398a3a-574f-4043-ab1e-6cadebf05903"}, {"title": "bKHMSSy1FnYu8kR_Program", "modules": [{"title": "R1C9Z74pzyPzsoD Module", "short_title": "R1C9Z74pzyPzsoD Module", "length": 7, "description": "ZAQeydaNut8yNeE 11-24-2024 13:11", "order": 1, "sections": [], "id": "21a7ff0f-c108-442f-801a-ea5354bfecc1"}], "id": "915be2c9-564a-43d8-a00c-6e4c8de03e82"}, {"title": "myWmjYKOCCD1lZ7_Program", "modules": [{"title": "GZmUqA28wyJBAil Module", "short_title": "GZmUqA28wyJBAil Module", "length": 7, "description": "EmvaNbyshUiohE9 11-25-2024 13:11", "order": 1, "sections": [], "id": "f523ac48-9819-4f4f-b2a5-09499569205e"}], "id": "f54fea32-3817-474e-8149-7c960606f4db"}, {"title": "pxDjMnZrf3zXYfi_Program", "modules": [{"title": "SaE0vEOsnVPlNaa Module", "short_title": "SaE0vEOsnVPlNaa Module", "length": 7, "description": "LMnKuBaOlyGIf0B 11-26-2024 13:11", "order": 1, "sections": [], "id": "d98a2673-3606-4b5f-b9e9-5ca87245e8ea"}], "id": "c138af68-5e55-4127-8ab5-f86ad2a333c1"}, {"title": "bUYcmnZrdaZVZRM_Program", "modules": [{"title": "z24zWTWWoscWyDS Module", "short_title": "z24zWTWWoscWyDS Module", "length": 7, "description": "VTwPL2SEUnVCeu1 11-27-2024 13:11", "order": 1, "sections": [], "id": "5275f455-c3bd-4855-9274-60db15e4a747"}], "id": "d0c20ba0-3605-426d-89da-d208cb73a370"}, {"title": "6JjVB9gvQYCykQe_Program", "modules": [{"title": "ZQgj92xTleGXVWG Module", "short_title": "ZQgj92xTleGXVWG Module", "length": 7, "description": "zTLjmt7KIIXPwef 11-28-2024 13:11", "order": 1, "sections": [], "id": "01b8899d-6935-4e28-8499-94395a327e3f"}], "id": "285031aa-e8de-4af1-8084-6ce2d770fb82"}, {"title": "fooGfV4CNOIoUNk_Program", "modules": [], "id": "421c2ae4-fbfa-4640-b079-e82bb22c70cc"}]