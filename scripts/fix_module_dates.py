import asyncio
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from functools import partial
from itertools import pairwise
from pathlib import Path
from uuid import UUID

import click
import tortoise
import tortoise.transactions
from loguru import logger
from tabulate import tabulate
from tortoise import Tortoise

from ciba_participant.cohort.models import Cohor<PERSON>, CohortProgramModules
from ciba_participant.db import get_tortoise_orm_config
from ciba_participant.settings import get_settings


@click.group()
def cli(): ...


@cli.command()
def dry_run():
    logger.info("Running in dry run mode")
    asyncio.run(fix_module_dates(dry_run=True))


@cli.command()
def run():
    logger.info("Running in normal mode")
    asyncio.run(fix_module_dates(dry_run=False))


@dataclass
class FixOperation:
    cohort_program_module_id: UUID
    next_cohort_program_module_id: UUID
    old_ended_at: datetime
    new_ended_at: datetime
    next_started_at: datetime


def to_tuple(instance: object, attrs: list[str]):
    return tuple(getattr(instance, attr) for attr in attrs)


cohort_program_module_cache = defaultdict[UUID, CohortProgramModules]()


async def fix_module_dates(*, dry_run: bool):
    settings = get_settings()
    tortoise_config = get_tortoise_orm_config(settings.default_db_url)

    await Tortoise.init(config=tortoise_config)

    all_cohorts = await Cohort.all()

    logger.info(f"Found {len(all_cohorts)} cohorts")

    operations: list[FixOperation] = []

    for cohort in all_cohorts:
        modules = await CohortProgramModules.filter(cohort_id=cohort.id).order_by(
            "started_at"
        )

        cohort_program_module_cache.update({module.id: module for module in modules})

        if not modules:
            logger.info(f"No modules for cohort {cohort.name}")

        for prev_module, next_module in pairwise(modules):
            gap = next_module.started_at - prev_module.ended_at

            logger.debug(f"Gap between {prev_module.id} and {next_module.id} is {gap}")

            if gap.days == 1:
                operations.append(
                    FixOperation(
                        cohort_program_module_id=prev_module.id,
                        next_cohort_program_module_id=next_module.id,
                        old_ended_at=prev_module.ended_at,
                        new_ended_at=next_module.started_at,
                        next_started_at=next_module.started_at,
                    )
                )

    logger.info(f"{len(operations)} operations to be performed")

    operation_attributes = list(FixOperation.__annotations__.keys())

    operations_to_tuple = partial(to_tuple, attrs=operation_attributes)

    if not operations:
        logger.info("No operations to be performed")
        return

    logger.debug("Operations to be performed:")
    logger.debug(
        "\n"
        + tabulate(map(operations_to_tuple, operations), headers=operation_attributes)
    )

    if not dry_run:
        await execute_fix(operations)


async def execute_fix(operations: list[FixOperation]):
    async with tortoise.transactions.in_transaction():
        for operation in operations:
            cached_module = cohort_program_module_cache.get(
                operation.cohort_program_module_id
            )

            if not cached_module:
                raise Exception(
                    f"Module {operation.cohort_program_module_id} not found in cache. Weird"
                )

            cached_module.ended_at = operation.new_ended_at

            logger.debug(
                f"Setting module {cached_module.id} ended_at to {operation.new_ended_at}, was {operation.old_ended_at}"
            )

            await cached_module.save()

    logger.info("All operations completed")


if __name__ == "__main__":
    logs_folder = Path("logs")
    logs_folder.mkdir(exist_ok=True, parents=True)
    logger.add(logs_folder / "fix_module_dates_{time:YYYY_MM_DD_HH_mm_ss}.log")
    cli()
