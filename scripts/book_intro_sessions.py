import sys
import asyncio
import click
import tortoise.transactions
import pendulum
from uuid import UUID
from loguru import logger
from sorcery import dict_of
from progressbar import progressbar


import pandas as pd

from ciba_participant.participant.models import ParticipantStatus
from ciba_participant.classes.models import (
    Booking,
    BookingStatusEnum,
    LiveSession,
    MeetingTypeEnum,
)
from ciba_participant.cohort.models import <PERSON>hort
from ciba_participant.common.db import close_db, custom_init_db
from ciba_participant.settings import ENV, get_settings, Settings

WEBINAR_ID = UUID("1cc9f3fa-ae24-43dc-a45c-f6e47c2fa9bc")
CUTOFF_DATE = "2025-03-05"


async def init(
    dry_run=True,
    env: str = "local",
) -> Settings:
    logger.info(f"Fetching settings for {env} environment...")
    settings = get_settings(ENV=env)

    if dry_run is True:
        logger.info("Running in dry-run mode")
    else:
        logger.warning("Running in commit mode")

    await custom_init_db(settings)
    logger.info("DB initialized")

    return settings


async def book_attended_intro(participant_id: UUID, live_session_id: UUID):
    logger.info(f"Creating booking for participant {participant_id}")
    booking = await Booking.create(
        participant_id=participant_id,
        live_session_id=live_session_id,
        status=BookingStatusEnum.ATTENDED,
    )

    return booking.id


async def create_live_session(legacy_session: dict) -> UUID:
    # Needs to be created on DB

    new_session = await LiveSession.create(
        created_at=legacy_session["created_at"],
        updated_at=legacy_session["updated_at"],
        id=legacy_session["id"],
        title=legacy_session["title"],
        description=legacy_session["description"],
        meeting_start_time=legacy_session["meeting_start_time"],
        meeting_type=MeetingTypeEnum.SCHEDULED,
        zoom_id=legacy_session["zoom_id"],
        zoom_occurrence_id=legacy_session["zoom_occurrence_id"],
        timezone=legacy_session["timezone"],
        has_conflict=legacy_session["has_conflict"],
        recording_url=legacy_session["recording_url"],
        use_custom_meeting_link=legacy_session["use_custom_meeting_link"],
        custom_meeting_link=legacy_session["custom_meeting_link"],
        webinar_id=WEBINAR_ID,
    )

    return new_session.id


async def check_existing_bookings(
    env: str,
    dry_run=True,
):
    logger.remove()
    logger.add(sys.stderr, level="INFO")

    await init(dry_run=dry_run, env=env)

    async with tortoise.transactions.in_transaction() as connection:
        result = await connection.execute_query_dict(
            f"""
            select distinct c.id as id, c.created_at as cohort_created_at
            from legacy_live_session lls left join cohort c
            on lls.cohort_id =c.id
            where lls.meeting_start_time < '{CUTOFF_DATE}'::date
            order by cohort_created_at desc
            """
        )
        past_cohorts = list(map(lambda c: c["id"], result))
        past_cohorts = []

        INCLUDE_FROM_MARCH = [
            "b30dabe3-638d-4ed5-8535-81d5c36844c9",
            "8ab97dd5-477e-4631-8762-88ea9f51b90a",
        ]
        past_cohorts.extend(INCLUDE_FROM_MARCH)

        participant_to_cohort = {}
        participant_to_email = {}

        num_cohorts = len(past_cohorts)
        participant_ids: list[UUID] = []

        logger.info("Fetching past participants list...")
        for i in progressbar(range(len(past_cohorts)), redirect_stdout=True):
            cohort_id = past_cohorts[int(i)]
            cohort: Cohort | None = await Cohort.get_or_none(
                id=cohort_id
            ).prefetch_related("participants")
            if cohort is not None:
                for participant in cohort.participants:
                    if participant.status == ParticipantStatus.ACTIVE:
                        participant_ids.append(participant.id)
                        participant_to_cohort[participant.id] = str(cohort_id)
                        participant_to_email[participant.id] = str(participant.email)

        total_participants = len(participant_ids)
        num_participants_without_bookings = 0

        participant_ids: list[UUID] = list(set(participant_ids))
        updated_participants: list[dict] = []

        logger.info("Getting booking status for each participant...")

        for i in progressbar(range(len(participant_ids)), redirect_stdout=True):
            participant_id = participant_ids[int(i)]
            bookings = await Booking.filter(
                participant_id=participant_id,
                status=BookingStatusEnum.ATTENDED,
                live_session__webinar__id=WEBINAR_ID,
            )

            if len(bookings) == 0:
                num_participants_without_bookings += 1
                if dry_run is True:
                    logger.info(
                        f"{num_participants_without_bookings}/{total_participants} need fixing"
                    )
                else:
                    lls_result = await connection.execute_query_dict(
                        f"select * from legacy_live_session where cohort_id='{participant_to_cohort[participant_id]}' and meeting_start_time < '{CUTOFF_DATE}' LIMIT 1"
                    )
                    legacy_live_session = lls_result[0]

                    new_session = await LiveSession.get_or_none(
                        id=legacy_live_session["id"]
                    )

                    if new_session is None:
                        session_id: UUID = await create_live_session(
                            legacy_live_session
                        )
                    else:
                        session_id: UUID = new_session.id

                    booking_id = await book_attended_intro(participant_id, session_id)
                    updated_participants.append(
                        {
                            "id": str(participant_id),
                            "email": participant_to_email[participant_id],
                            "cohort_id": participant_to_cohort[participant_id],
                            "session_id": session_id,
                            "booking_id": booking_id,
                        }
                    )

        report = pd.DataFrame(updated_participants)
        if len(report) > 0:
            report.set_index("id").to_csv(
                f"./{pendulum.now().int_timestamp}-{env}-live_session_check.csv"
            )

        summary: dict = dict_of(
            num_cohorts, total_participants, num_participants_without_bookings
        )
        print(summary)


async def book_intro_sessions(dry_run: bool, env: str):
    settings = await init(dry_run=dry_run, env=env)

    live_sessions_created = []
    bookings_created = []
    participants_updated = []

    try:
        async with tortoise.transactions.in_transaction() as connection:
            # Cohorts with at least one live session
            legacy_result = await connection.execute_query_dict(
                f"select distinct cohort_id from legacy_live_session where meeting_start_time < '{CUTOFF_DATE}'::date"
            )
            cohorts_with_sessions = list(map(lambda c: c["cohort_id"], legacy_result))

            cohorts = await Cohort.filter(
                id__in=cohorts_with_sessions
            ).prefetch_related("participants")

            for cohort in cohorts:
                sessions_result = await connection.execute_query_dict(
                    f"select * from legacy_live_session where cohort_id='{str(cohort.id)}'"
                )

                for session in sessions_result:
                    if dry_run is False:
                        if len(cohort.participants) > 0:
                            session_id = await create_live_session(session)
                            for participant in cohort.participants:
                                # We'll assume that these participants have attended at least one session,
                                # so we'll create an ATTENDED booking so they can book non-intro classes
                                if participant.status == ParticipantStatus.ACTIVE:
                                    participants_updated.append(str(participant.id))
                                    booking_id = await book_attended_intro(
                                        participant.id, session_id
                                    )
                                    bookings_created.append(str(booking_id))
                    else:
                        live_sessions_created.append(str(""))
                        logger.info(
                            f"Session to create: {session['title']} for {len(cohort.participants)} participants"
                        )

            logger.success(f"{len(live_sessions_created)} live sessions created")
            logger.success(f"{len(bookings_created)} bookings created")
            logger.success(f"{len(participants_updated)} participants involved")

            with open("booking_update.txt", "w") as out_file:
                out_file.write("Live Sessions:\n")
                for ls in live_sessions_created:
                    out_file.write(f"{ls}\n")
                out_file.write("Bookings:\n")
                for ls in bookings_created:
                    out_file.write(f"{ls}\n")
                out_file.write("Participants:\n")
                for ls in participants_updated:
                    out_file.write(f"{ls}\n")

    except ConnectionRefusedError:
        logger.error(f"Error: Could not connect to {settings.ENV} DB")
    except Exception as e:
        logger.error(f"Unhandled exception: {type(e)}")
        logger.error(e)
    finally:
        await close_db()


async def async_manual_book(
    session_id: str, participants: list[str], commit, environment
):
    settings = await init(dry_run=not commit, env=environment)

    try:
        async with tortoise.transactions.in_transaction():
            for participant_id in participants:
                if commit:
                    await book_attended_intro(UUID(participant_id), UUID(session_id))
                else:
                    print(
                        f"booking to be created: -> participant {participant_id}, live_session {session_id}"
                    )

    except ConnectionRefusedError:
        logger.error(f"Error: Could not connect to {settings.ENV} DB")
    except Exception as e:
        logger.error(f"Unhandled exception: {type(e)}")
        logger.error(e)
    finally:
        await close_db()


@click.group()
def cli(): ...


@cli.command()
@click.option(
    "--commit",
    is_flag=True,
    help="Commit changes to DB. USE WITH CAUTION!",
)
@click.option(
    "-e",
    "--environment",
    type=str,
    default="local",
    help="Environment to use. Default: local",
)
def book(commit: bool = False, environment: str = "local"):
    if environment == ENV.PROD:
        MESSAGE = (
            "Running script on PROD environment. Do you want to continue? (yes/no)\n"
        )
        confirmation = input(MESSAGE)
        while confirmation != "yes":
            if confirmation == "no":
                print("Cancelled")
                return
            confirmation = input(MESSAGE)
    asyncio.run(book_intro_sessions(dry_run=not commit, env=environment))


@cli.command()
@click.option(
    "--commit",
    is_flag=True,
    help="Commit changes to DB. USE WITH CAUTION!",
)
@click.option(
    "-e",
    "--environment",
    type=str,
    default="local",
    help="Environment to use. Default: local",
)
def check(commit: bool = False, environment: str = "local"):
    if environment == ENV.PROD and commit is True:
        MESSAGE = (
            "Running script on PROD environment. Do you want to continue? (yes/no)\n"
        )
        confirmation = input(MESSAGE)
        while confirmation != "yes":
            if confirmation == "no":
                print("Cancelled")
                return
            confirmation = input(MESSAGE)
    asyncio.run(check_existing_bookings(dry_run=not commit, env=environment))


@cli.command()
@click.option(
    "--commit",
    is_flag=True,
    help="Commit changes to DB. USE WITH CAUTION!",
)
@click.option(
    "-e",
    "--environment",
    type=str,
    default="local",
    help="Environment to use. Default: local",
)
@click.option(
    "-f",
    "--file",
    type=str,
    help="File to process",
    required=True,
)
def manual_book(file: str, commit: bool = False, environment: str = "local"):
    df = pd.read_csv(file)
    session_map = (
        df[["session_id", "participant_id"]]
        .groupby("session_id")["participant_id"]
        .apply(list)
        .reset_index(name="participants")
    )

    for _, row in session_map.iterrows():
        session_id = row["session_id"]
        asyncio.run(
            async_manual_book(session_id, row["participants"], commit, environment)
        )


if __name__ == "__main__":
    cli()
