# User Enrollment/Re-enrollment Flow Diagram

This diagram illustrates the complete flow of how users can enroll or re-enroll to the system using the `land_participant` and `set_new_password` functions.

```mermaid
flowchart TD
    %% Main entry points
    Start([User Starts Enrollment]) --> LandParticipant[land_participant]
    ReEnroll([User Re-enrolls]) --> LandParticipant

    %% land_participant function flow
    LandParticipant --> CreateUserNoCohort[SoleraHandler.create_user_no_cohort]

    %% create_user_no_cohort detailed flow
    subgraph CreateUserNoCohortFlow["create_user_no_cohort flow"]
        GetSoleraUserInfo[Get Solera user info] --> CheckSoleraInfo{Info valid?}
        CheckSoleraInfo -->|No| ReturnRegFailed1[Return RegistrationFailed]

        CheckSoleraInfo -->|Yes| ExtractEmail[Extract email]
        ExtractEmail --> CheckEmail{Email exists?}
        CheckEmail -->|No| ReturnRegFailed2[Return RegistrationFailed]

        CheckEmail -->|Yes| PrepareUserDict[Prepare user dictionary]
        PrepareUserDict --> CheckExistingParticipant{Participant exists?}

        %% New participant flow
        CheckExistingParticipant -->|No| CreateParticipantWithCohortCheck[_create_participant_with_cohort_check]
        CreateParticipantWithCohortCheck --> CheckCohortAvailable1{Cohort available?}
        CheckCohortAvailable1 -->|No| ReturnRegFailed3[Return RegistrationFailed]
        CheckCohortAvailable1 -->|Yes| CreateNewUser[_create_new_user]
        CreateNewUser --> CheckUserCreated1{User created?}
        CheckUserCreated1 -->|No| ReturnRegFailed4[Return RegistrationFailed]
        CheckUserCreated1 -->|Yes| ReturnNewParticipant[Return new Participant]

        %% Existing participant flow
        CheckExistingParticipant -->|Yes| HandleExistingParticipant[_handle_existing_participant]
        HandleExistingParticipant --> CheckParticipantStatus{Participant status?}

        %% Active or Pending participant
        CheckParticipantStatus -->|ACTIVE/PENDING| ReturnExistingParticipant[Return existing Participant]

        %% Deleted participant
        CheckParticipantStatus -->|DELETED| DisenrollFromCohorts[Disenroll from active cohorts]
        DisenrollFromCohorts --> CheckCareplanID{Same careplan ID?}
        CheckCareplanID -->|Yes| ReturnRegFailed5[Return RegistrationFailed]

        CheckCareplanID -->|No| CheckCohortAvailable2{Cohort available?}
        CheckCohortAvailable2 -->|No| ReturnRegFailed6[Return RegistrationFailed]
        CheckCohortAvailable2 -->|Yes| ReenrollUser[_reenroll_user]
        ReenrollUser --> CheckUserReenrolled{User reenrolled?}
        CheckUserReenrolled -->|No| ReturnRegFailed7[Return RegistrationFailed]
        CheckUserReenrolled -->|Yes| ReturnReenrolledParticipant[Return reenrolled Participant]

        %% Other status
        CheckParticipantStatus -->|Other| ReturnDuplicatedError1[Return ParticipantDuplicatedError]
    end

    %% Back to land_participant flow
    CreateUserNoCohort --> CheckParticipantResult{Result type?}

    %% Error handling in land_participant
    CheckParticipantResult -->|ParticipantDuplicatedError| ReturnDuplicatedError[Return ParticipantDuplicatedError]
    CheckParticipantResult -->|null| ReturnDneError[Return ParticipantDneError]
    CheckParticipantResult -->|RegistrationFailed| ReturnRegistrationFailed[Return RegistrationFailed]

    %% Check if already enrolled
    CheckParticipantResult -->|Participant| CheckActiveStatus{Status is ACTIVE?}
    CheckActiveStatus -->|Yes| ReturnAlreadyEnrolled[Return AlreadyEnrolled]

    %% Successful path in land_participant
    CheckActiveStatus -->|No| GetSoleraProgram[Get solera_program_id]
    GetSoleraProgram --> FindProgram[Find program with matching title]
    FindProgram --> GetCohorts[Get cohorts for program]
    GetCohorts --> FilterCohorts[Filter cohorts with free places and future start]
    FilterCohorts --> CreateProgramGroupTypes[Create ProgramGroupType objects]
    CreateProgramGroupTypes --> ReturnCohorts[Return CohortsData with cohorts]

    %% User selects cohort and proceeds to set_new_password
    ReturnCohorts --> UserSelectsCohort[User selects cohort]
    UserSelectsCohort --> SetNewPassword[set_new_password]

    %% set_new_password function flow
    subgraph SetNewPasswordFlow["set_new_password flow"]
        GetUserEmail[Get user email from context]
        GetUserEmail --> SetPassword[Set new password for participant]
        SetPassword --> CheckMeta{Has participant meta?}

        %% Participant meta handling
        CheckMeta -->|Yes| UpdateMeta[Update existing meta]
        CheckMeta -->|No| CreateMeta[Create new meta]
        CreateMeta --> SetWeightData[Set weight data in meta]
        UpdateMeta --> SetWeightData

        %% Weight activity creation
        SetWeightData --> GetCohort[Get cohort by ID]
        GetCohort --> CreateWeightActivity[Create weight activity]
        CreateWeightActivity --> SetActivityDate[Set activity date based on cohort]
        SetActivityDate --> SaveActivity[Save participant activity]

        %% Cohort membership and booking
        SaveActivity --> CreateCohortMember[Create cohort member]
        CreateCohortMember --> UpdateBookingStatus[Update booking status to BOOKED]

        %% Chat assignment
        UpdateBookingStatus --> CheckEnvironment{Is production?}
        CheckEnvironment -->|Yes| AssignToChat[Assign participant to chat]
        CheckEnvironment -->|No| SkipChatAssignment[Skip chat assignment]

        AssignToChat --> ReturnEmail[Return ParticipantEmail]
        SkipChatAssignment --> ReturnEmail
    end

    %% Final states
    ReturnDuplicatedError --> EnrollmentFailed([Enrollment Failed])
    ReturnDneError --> EnrollmentFailed
    ReturnRegistrationFailed --> EnrollmentFailed
    ReturnAlreadyEnrolled --> EnrollmentFailed
    ReturnEmail --> EnrollmentComplete([Enrollment Complete])

    %% Styling
    classDef process fill:#f9f,stroke:#333,stroke-width:2px;
    classDef decision fill:#bbf,stroke:#333,stroke-width:2px;
    classDef endpoint fill:#bfb,stroke:#333,stroke-width:2px;
    classDef error fill:#fbb,stroke:#333,stroke-width:2px;

    class LandParticipant,SetNewPassword,GetSoleraProgram,FindProgram,GetCohorts,FilterCohorts,CreateProgramGroupTypes process;
    class CheckParticipantResult,CheckActiveStatus decision;
    class Start,ReEnroll,EnrollmentComplete endpoint;
    class ReturnDuplicatedError,ReturnDneError,ReturnRegistrationFailed,ReturnAlreadyEnrolled,EnrollmentFailed error;
```

## Detailed Process Explanation

### Enrollment Process

1. **Initial Entry Points**:

   - New user starts enrollment
   - Existing user re-enrolls

1. **land_participant Function** (Takes `look_up_key`):

   - Calls `SoleraHandler().create_user_no_cohort(look_up_key)`
   - Processes the result based on its type:
     - If ParticipantDuplicatedError → Returns ParticipantDuplicatedError
     - If null → Returns ParticipantDneError
     - If RegistrationFailed → Returns RegistrationFailed
     - If Participant with ACTIVE status → Returns AlreadyEnrolled
   - On success (Participant with non-ACTIVE status):
     - Gets the participant's Solera program ID
     - Finds a program with matching title
     - Gets cohorts for that program
     - Filters cohorts that have:
       - Free places (participants < cohort.limit)
       - Start date in the future
     - Returns `CohortsData` with filtered cohorts

1. **create_user_no_cohort Function** (Takes `key`):

   - Gets Solera user information using `_get_solera_user_info_safely`

     - If info is invalid → Returns RegistrationFailed

   - Extracts email from user info

     - If email is missing → Returns RegistrationFailed

   - Prepares user dictionary with `_prepare_user_dict`

   - Checks if participant already exists with `_get_existing_participant`

   - **If participant doesn't exist**:

     - Calls `_create_participant_with_cohort_check`:
       - Checks if a cohort is available for the program
         - If not available → Returns RegistrationFailed
       - Creates a new user with `_create_new_user`
         - If creation fails → Returns RegistrationFailed
       - Returns the new Participant

   - **If participant exists**:

     - Calls `_handle_existing_participant`:
       - If status is ACTIVE or PENDING → Returns existing Participant
       - If status is DELETED:
         - Disenrolls from any active cohorts
         - Checks if careplan ID (key) is different from previous enrollments
           - If same → Returns RegistrationFailed
         - Checks if a cohort is available for the program
           - If not available → Returns RegistrationFailed
         - Re-enrolls the user with `_reenroll_user`
           - If re-enrollment fails → Returns RegistrationFailed
         - Returns the re-enrolled Participant
       - For other statuses → Returns ParticipantDuplicatedError

1. **User Selection**:

   - User selects a cohort from the returned list

1. **set_new_password Function** (Takes `cohort_id`, `new_password`, `starting_weight`, `target_weight`, `session_id`):

   - Gets user email from context
   - Sets new password for participant
   - Handles participant metadata:
     - If metadata exists → Updates it
     - If no metadata → Creates new metadata
   - Sets weight data in metadata:
     - `user_reported_weight` = starting_weight
     - `user_target_weight` = target_weight
   - Gets cohort by ID
   - Creates weight activity record:
     - Sets activity date based on cohort start date
     - Saves the activity
   - Creates cohort membership:
     - Adds participant to selected cohort
   - Updates booking status to BOOKED
   - If in production environment:
     - Assigns participant to chat
   - Returns participant email

### Error Handling

- **ParticipantDneError**: Participant does not exist
- **ParticipantDuplicatedError**: Participant already exists but cannot be enrolled
- **RegistrationFailed**: Registration process failed (multiple possible causes)
- **AlreadyEnrolled**: Participant is already enrolled (status is ACTIVE)

### Re-enrollment Scenarios

1. **Same Careplan ID Re-enrollment**:

   - User tries to re-enroll with the same careplan ID
   - System detects this and returns RegistrationFailed
   - User cannot re-enroll with the same careplan ID

1. **Different Careplan ID Re-enrollment**:

   - User tries to re-enroll with a different careplan ID
   - System disenrolls from any active cohorts
   - Checks for available cohort
   - Re-enrolls the user with the new careplan ID
   - Returns the re-enrolled Participant

### Key Data Models

- **SoleraParticipant**: Contains participant information including Solera-specific IDs
- **ParticipantMeta**: Contains participant metadata including weight information
- **ParticipantActivity**: Records participant activities including weight measurements
- **Cohort**: Represents a group of participants in a program
- **CohortMembers**: Links participants to cohorts
