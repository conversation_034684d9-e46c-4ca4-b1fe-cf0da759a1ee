```mermaid
classDiagram
direction BT
class conversation {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(50) type
   varchar(255) sid
   varchar(255) unique_name
   varchar(255) friendly_name
   uuid id
}
class conversation_participant {
   uuid conversation_id
   uuid participant_id
}
class participant_chat_api {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(50) external_type
   varchar(36) external_id
   varchar(255) chat_identity
   uuid id
}

conversation --> conversation_participant:id
participant_chat_api --> conversation_participant:id
participant_api --> participant_chat_api:id as external_id


class builder_module {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(50) short_title
   varchar(255) title
   integer duration
   text description
   integer module_order
   uuid program_id
   uuid id
}
class builder_module_section {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(255) title
   text description
   varchar(255) type
   jsonb metadata
   uuid program_module_id
   uuid solera_activity_id
   uuid solera_milestone_id
   uuid id
}

class company {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(255) name
   uuid id
}
class participant_api {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(255) email
   varchar(255) first_name
   varchar(255) last_name
   varchar(255) group_id
   varchar(255) member_id
   varchar(100) status
   uuid company_id
   uuid cognito_sub
   varchar(255) medical_record
   boolean is_test
   timestamp with time zone last_reset
   varchar(255) heads_up_id
   varchar(255) heads_up_token
   varchar(255) solera_enrollment_id
   varchar(255) solera_key
   uuid solera_id
   varchar(255) solera_program_id
   boolean is_weight
   boolean is_activity
   jsonb metadata
   uuid id
}
class program {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(255) title
   text type
   uuid id
}
class program_course {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   timestamp with time zone started_at
   uuid program_id
   uuid id
}
class program_group {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(255) name
   timestamp with time zone started_at
   integer limit
   uuid program_course_id
   uuid id
}
class program_group_members {
   uuid participant_id
   uuid program_group_id
}
class program_module {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(50) short_title
   varchar(255) title
   timestamp with time zone started_at
   timestamp with time zone ended_at
   text description
   uuid program_course_id
   uuid program_group_id
   uuid parent_id
   uuid id
}
class program_module_section {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(255) title
   varchar(255) type
   text description
   jsonb metadata
   uuid program_module_id
   uuid solera_activity_id
   uuid solera_milestone_id
   uuid id
}
class program_module_section_progress {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(255) type
   jsonb metadata
   uuid participant_id
   uuid program_module_section_id
   uuid solera_activity_id
   uuid solera_milestone_id
   uuid id
}
class solera_activity {
   varchar(255) name
   varchar(7) metric  /* DATE: date↲INT: int↲TEXT: text↲BOOLEAN: boolean */
   varchar(255) property_name
   varchar(7) property_metric  /* DATE: date↲INT: int↲TEXT: text↲BOOLEAN: boolean */
   varchar(16) activity_type  /* ENROLL: Enrollment↲PLAY: ContentPlayed↲COACH: CoachInteractio... */
   uuid id
}
class solera_activity_progress {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(8) status  /* COMPLETE: COMPLETE↲DONE: DONE↲SEND: SEND↲ERROR: ERROR */
   uuid referenceId
   jsonb data
   jsonb error
   uuid activity_id
   uuid milestone_id
   uuid paricipant_id
   uuid id
}
class solera_milestone {
   varchar(255) name
   integer duration
   uuid id
}
class solera_milestone_progress {
   timestamp with time zone created_at
   timestamp with time zone updated_at
   varchar(8) status  /* COMPLETE: COMPLETE↲WAIT: WAIT */
   uuid referenceId
   timestamp with time zone solera_date
   uuid milestone_id
   uuid paricipant_id
   uuid id
}

participant_api <-- company:id
participant_api <-- solera_milestone_progress:paricipant_id

program_group_members <-- participant_api:id
program_group_members <-- program_group:id

program_group <-- program_course:id

program_course <-- program:id

program_module_section <-- solera_activity:id
program_module_section <-- solera_milestone:id
program_module_section <-- program_module:id

program_module_section_progress <-- participant_api:id
program_module_section_progress <-- program_module_section:id
program_module_section_progress <-- solera_activity:id
program_module_section_progress <-- solera_milestone:id

program_module <-- program_course:id
program_module <-- program_group:id

solera_activity_progress <-- solera_activity:id
solera_activity_progress <-- solera_milestone:id
solera_activity_progress <-- participant_api:id

builder_module <-- program:id

builder_module_section <-- solera_activity:id
builder_module_section <-- solera_milestone:id
builder_module_section <-- program_module:id
```
