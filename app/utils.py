import base64
import hashlib
import secrets
import string
from base64 import b64decode

from aws_encryption_sdk import EncryptionSDKClient
from aws_encryption_sdk.identifiers import CommitmentPolicy
from aws_encryption_sdk.key_providers.kms import StrictAwsKmsMasterKeyProvider
from Crypto import Random
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

from app.settings import get_settings

settings = get_settings()


class AESCipher:
    """AES Cipher class.

    Encrypt and decrypt values using secret.
    """

    def __init__(self, key: str, mode: int = AES.MODE_CBC) -> None:
        self.key = hashlib.sha256(key.encode()).digest()
        self.block_size = AES.block_size
        self.mode = mode

    def encrypt(self, value: str) -> bytes:
        """Encrypt value using AES CBC mode."""
        padded_value = pad(value.encode(), self.block_size)
        iv_str = Random.new().read(self.block_size)
        cipher = AES.new(self.key, self.mode, iv_str)
        return base64.b64encode(iv_str + cipher.encrypt(padded_value))

    def decrypt(self, enc: bytes) -> str:
        """Decrypt encrypted value using AES CBC mode."""
        enc = base64.b64decode(enc)
        iv_str = enc[: self.block_size]
        cipher = AES.new(self.key, self.mode, iv_str)
        return unpad(
            cipher.decrypt(enc[self.block_size :]), self.block_size
        ).decode("utf-8")


def decrypt(ciphertext: str) -> tuple[str, dict]:
    """Decrypt generated by Cognito tmp codes."""
    client = EncryptionSDKClient(
        commitment_policy=CommitmentPolicy.REQUIRE_ENCRYPT_ALLOW_DECRYPT
    )
    # Create an AWS KMS master key provider
    kms_kwargs = {"key_ids": [settings.COGNITO_KMS_KEY_ARN]}
    master_key_provider = StrictAwsKmsMasterKeyProvider(**kms_kwargs)
    encrypted_code = b64decode(ciphertext)
    # Decrypt the ciphertext
    decrypted, decrypted_header = client.decrypt(
        source=encrypted_code, key_provider=master_key_provider
    )
    code = decrypted.decode() if isinstance(decrypted, bytes) else decrypted
    return code, decrypted_header


class CouldNotFindValidPassword(Exception):
    """Exception for cases when valid password could not be generated."""


def generate_random_password(pwd_length: int = 12) -> str:
    """Generate random password.

    Random password should contain at least one uppercase letter,
    lowercase letter, digit and special char.
    """
    ascii_lowercase = string.ascii_lowercase
    ascii_uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = string.punctuation
    alphabet = ascii_lowercase + ascii_uppercase + digits + special_chars
    # give it only 100 attempts to find valid password
    for _ in range(100):
        pwd = ""
        for _ in range(pwd_length):
            pwd += "".join(secrets.choice(alphabet))

        if (
            any(char in ascii_lowercase for char in pwd)
            and any(char in ascii_uppercase for char in pwd)
            and any(char in digits for char in pwd)
            and any(char in special_chars for char in pwd)
        ):
            break
    else:
        raise CouldNotFindValidPassword
    return pwd
