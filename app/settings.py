from functools import lru_cache
from typing import Any
from urllib.parse import quote_plus
from ciba_participant.settings import Settings as CibaSettings, ENV


class Settings(CibaSettings):
    """Project settings class.

    All the vars with default values here are initialized from env variables.
    If env var is not specified, default value will be used.
    """

    PROJECT_NAME: str = "CibaHealth participant API"
    DEBUG: int = 0
    VERSION: str = "3.2.2"

    ENV: str = ENV.LOCAL
    DEV_EMAIL: str = "<EMAIL>"

    ALLOW_ORIGINS: str = "http://localhost:3000,http://localhost:3001"
    ALLOW_HEADERS: str = "*"
    ALLOW_METHODS: str = "*"

    REDIS_PASSWORD: str = ""
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379

    SENDGRID_API_KEY: str = ""
    SENDGRID_SENDER_EMAIL: str = "<EMAIL>"
    SENDGRID_SENDER_NAME: str = "Cibahealth"
    SENDGRID_REPLY_TO_EMAIL: str = "<EMAIL>"

    JWT_SECRET: str = ""
    ADMIN_HOST: str = "http://localhost:8080"
    UI_HOST: str = "http://localhost:3000"

    CELERY_CONFIG_MODULE: str = "app.tasks.celery_config"
    CELERY_BROKER_URL: str = "sqs://"
    CELERY_RESULT_EXPIRES: int = 1209600  # 14 days
    SQS_QUEUE: str = ""
    SQS_URL: str = ""
    SQS_REGION: str = "us-east-2"

    ADMIN_UI_HOST: str = ""
    CIBA_API_HOST: str = ""
    CIBA_API_KEY: str = ""

    CHAT_API_HOST: str = ""
    CHAT_API_KEY: str = ""

    SENTRY_DSN: str = ""
    SECRET_KEY: str = ""

    HEADSUP_API_URL: str = "https://app.headsuphealth.com/api/v1/"
    HEADSUP_DASHBOARD_URL: str = "https://app.headsuphealth.com/dashboard"
    HEADS_UP_ENCRYPTION_KEY: str = ""
    HEADS_UP_ORG_UUID: str = ""
    HEADS_UP_API_KEY: str = ""
    RPM_API_URL: str = ""
    RPM_API_KEY: str = ""

    SOLERA_API_URL: str = ""
    SOLERA_AUTH_URL: str = ""
    TEST_KEY: str = ""
    PARTICIPANT_ALLOWED_API_KEY: str = "XGZAsDvfXbtCMnMyXLDPYDwajRXAIbmC"
    PARTICIPANT_EMAIL_SQS_URL: str = ""
    ZENDESK_KEY_ID: str = ""
    ZENDESK_SIGN_IN_SECRET: str = ""
    KINESIS_STREAM_NAME: str = "ciba-engagement-data-stream"

    @property
    def default_db_url(self) -> str:
        """Construct default database url."""
        return (
            f"postgres://"
            f"{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}"
            f"@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}"
            f"/{self.POSTGRES_DB}"
        )

    @property
    def result_backend_url(self) -> str:
        """Construct result backend url."""
        return (
            f"db+postgresql://"
            f"{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}"
            f"@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}"
            f"/{self.POSTGRES_DB}"
        )

    @property
    def docs_enabled(self) -> bool:
        """Enable docs only for not prod envs."""
        return self.ENV != ENV.PROD


@lru_cache()
def get_settings(**kwargs: Any) -> Settings:
    """Initialize settings."""
    settings = Settings(**kwargs)
    return settings
