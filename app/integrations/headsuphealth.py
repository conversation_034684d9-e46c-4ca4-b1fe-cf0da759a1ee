# import json
# from typing import Optional
#
# import requests
#
# from app.settings import get_settings
#
# settings = get_settings()
# api = settings.HEADS_UP_API_KEY
#
#
# def get_endpoint(request_type: str) -> str:
#     match request_type:
#         case "user":
#             return "/clients"
#         case "link":
#             return "/secure_user_link_data/{heads_up_id}"
#         case _:
#             return "Something's wrong with the internet"
#
#
# def headsup_request(
#     sent_link: str,
#     post_data: Optional[dict] = None,
#     get_data: Optional[dict] = None,
# ) -> dict:
#     path = get_endpoint(sent_link)
#     url = (
#         settings.HEADSUP_API_URL
#         + "organizations/"
#         + settings.HEADS_UP_ORG_UUID
#         + path
#     )
#
#     headers = {
#         "Content-Type": "application/json",
#         "Authorization": "Bearer " + api,
#     }
#
#     if post_data:
#         # response = requests.post(url, json=post_data, headers=headers)
#         response = requests.request(
#             "POST", url, headers=headers, data=json.dumps(post_data)
#         )
#     if get_data:
#         url = url.format(**get_data)
#         response = requests.get(url, headers=headers)
#     return response.json()
#
#
# # def test_get_user():
# #     data = {
# #         "email": "<EMAIL>",
# #         "first": "Alice",
# #         "last": "Smith",
# #         "password": "12345678",
# #         "gaSource": "mycompany.com",
# #         "timeZone": "Mountain Time (US & Canada)",
# #         "termsOfServiceAgreement": True,
# #         "privacyPolicyAgreement": True,
# #     }
# #     url2 = "/clients"
# #     return headsup_request(url2, data)
