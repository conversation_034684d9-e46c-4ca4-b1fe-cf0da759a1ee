import logging

import boto3
from botocore.client import BaseClient, Config

from app.settings import Settings, get_settings

settings: Settings = get_settings()

logger = logging.getLogger("main")


def get_s3_client() -> BaseClient:
    """Init and return s3 client."""
    return boto3.client(
        "s3",
        region_name=settings.AWS_REGION,
        config=Config(
            s3={"addressing_style": "path"}, signature_version="s3v4"
        ),
    )


def create_presigned_url(
    bucket_name: str, object_name: str, expiration: int = 604800
) -> str:
    """Generate a presigned URL to share an S3 object.

    :param bucket_name: string
    :param object_name: string
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Presigned URL as string.
    """
    return get_s3_client().generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": bucket_name, "Key": object_name},
        ExpiresIn=expiration,
        HttpMethod="GET",
    )
