import json
from typing import Optional

import requests
import pendulum

from app.settings import get_settings

settings = get_settings()

LINK = settings.RPM_API_URL
X_KEY = settings.RPM_API_KEY


async def rpm_request(
    sent_link: str,
    get_data: Optional[dict] = None,
    post_data: Optional[dict] = None,
    correlation_id: Optional[str] = None,
) -> dict:
    """Request to RPM API."""
    sent_link = LINK + sent_link
    correlation_id = (
        str(pendulum.now().int_timestamp)
        if not correlation_id
        else str(correlation_id)
    )
    headers = {"X-Auth-Key": X_KEY, "X-Request-ID": correlation_id}
    # Handle for GET
    if get_data:
        url = sent_link.format(**get_data)
        response = requests.get(url, headers=headers)
    # Handle for POST
    elif post_data:
        response = requests.post(
            sent_link, data=json.dumps(post_data), headers=headers
        )
        print(response.request.body)
    else:
        response = requests.get(sent_link, headers=headers)

    return response.json()
