from typing import Optional

from pydantic import BaseModel

from ciba_participant.rpm_api.models import (
    DeviceStatusEnum,
    DeviceTypeEnum,
    DeviceModelEnum,
)


class ConnectionStatus(BaseModel):
    status: DeviceStatusEnum
    healthy: bool
    device: DeviceTypeEnum
    device_id: Optional[str] = None
    account_id: Optional[str] = None
    battery: Optional[int] = None
    signal: Optional[int] = None
    model: Optional[DeviceModelEnum] = None
