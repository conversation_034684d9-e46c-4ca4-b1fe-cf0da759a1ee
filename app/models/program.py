import json
from datetime import datetime
from enum import Enum
from hashlib import md5
from uuid import UUID

from pydantic import BaseModel
from tortoise.fields import (
    DatetimeField,
)
from ciba_participant.activity.models import ParticipantActivityEnum


class TimestampMixin:
    pass


# pylint: disable=invalid-name
class ModuleDataTypes(Enum):
    coaching_call = "coaching_call"
    dietitian_call = "dietitian_call"
    personal_success = "personal_success"
    curriculum = "curriculum"
    weight_type = "weight_type"
    food_type = "food_type"
    video_type = "video_type"
    quiz_type = "quiz_type"
    recipe_type = "recipe_type"
    activity_type = "activity_type"


NO_LIMIT = "-1"


class Program:
    """
    Program database model mock from v1.
    Leave it in v2 not to break graphql types
    """

    created_at: datetime
    updated_at: datetime
    id: UUID
    title: str
    type: str


class ProgramCourse:
    """
    Program course database model mock from v1
    Leave it in v2 not to break graphql types
    """

    created_at: datetime
    updated_at: datetime

    id: UUID
    program_id: UUID
    started_at = DatetimeField()


class ProgramModule:
    """Program module database model."""

    created_at: datetime
    updated_at: datetime
    id: UUID
    short_title: str
    title: str
    program_course: UUID
    started_at: datetime
    ended_at: datetime
    description: str
    parent_id: UUID | None
    program_group_id: UUID | None


class ProgramModuleSection:
    """Program module section database model."""

    created_at: datetime
    updated_at: datetime

    id: UUID
    title: str
    description: str
    type: str
    program_module_id: UUID
    metadata: dict


class ProgramGroup:
    """Program group database model."""

    created_at: datetime
    updated_at: datetime

    id: UUID
    name: str
    program_course_id: UUID
    participants: list[UUID]
    started_at: datetime
    limit: int

    @property
    def unique_name(self) -> str:
        """Generate unique group name"""
        return md5(f"{self.name}-{self.id}".encode()).hexdigest()


class MetadataModel(BaseModel):
    """Progress metadata pydantic model."""

    start_date: datetime
    end_date: datetime | None = None

    def is_completed(self) -> bool:
        """Check whether a section was completed."""
        return self.start_date is not None

    def to_dict(self) -> dict:
        """Convert all non json serializable fields."""
        return json.loads(self.json())


class PersonalSuccessMetadataModel(MetadataModel):
    """Personal success metadata pydantic model.

    Extend base progress metadata model with type form response id.
    """

    response_id: str | None = None

    def is_completed(self) -> bool:
        """Check whether a section was completed."""
        return self.response_id is not None


class ValueMetadataModel(MetadataModel):
    """Weight success metadata pydantic model.

    Extend base progress metadata model with type form response id.
    """

    value: str | None = None

    def is_completed(self) -> bool:
        """Check whether a section was completed."""
        return self.value is not None


class ProgramModuleSectionProgress(BaseModel):
    """Program module section progress model."""

    created_at: datetime
    updated_at: datetime
    id: UUID
    participant_id: UUID
    program_module_section_id: UUID
    type: ParticipantActivityEnum
    metadata: dict | PersonalSuccessMetadataModel | ValueMetadataModel
    is_completed: bool
