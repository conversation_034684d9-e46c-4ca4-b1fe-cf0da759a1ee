def get_tortoise_orm_config(db_url: str) -> dict:
    """Init tortoise orm config."""
    return {
        "connections": {"default": db_url},
        "apps": {
            "models": {
                "models": [
                    "aerich.models",
                    "ciba_participant.activity.models",
                    "ciba_participant.classes.models",
                    "ciba_participant.cohort.models",
                    "ciba_participant.participant.models",
                    "ciba_participant.program.models",
                ],
                "default_connection": "default",
            },
        },
    }
