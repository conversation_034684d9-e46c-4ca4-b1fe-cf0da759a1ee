from pathlib import Path

import sentry_sdk
import pendulum
from asgi_correlation_id import CorrelationIdMiddleware, correlation_id
from fastapi.exception_handlers import http_exception_handler
from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import Response
from strawberry.fastapi import BaseContext, GraphQLRouter
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from ciba_participant.db import get_tortoise_orm_config
from ciba_participant.helpers.timezone_middleware import (
    TimeZoneMiddleware,
    TIMEZONE_HEADER,
)

from app.auth.models import AuthContext
from app.graphql_api import IGNORE_ERRORS, schema
from app.log.logging import logger
from app.routers.devices import devices
from app.services.checks.service_connection import (
    check_cognito_connection,
    check_http_service,
    check_postgres,
    check_redis_connection,
    verify_s3_connection,
    verify_sqs_connection,
)
from app.settings import get_settings, ENV
from app.auth.api_key import APIKeyMiddleware

settings = get_settings()

app = FastAPI(
    title=settings.PROJECT_NAME,
    debug=bool(settings.DEBUG),
    version=settings.VERSION,
)
app.add_middleware(
    CorrelationIdMiddleware,
    header_name="X-Request-ID",
    update_request_header=True,
    generator=lambda: str(pendulum.now().int_timestamp),
    transformer=lambda correlation_id: correlation_id,
    validator=None,
)

logger.info("APP initialized")

sentry_sdk.init(
    dsn=settings.SENTRY_DSN,
    ignore_errors=IGNORE_ERRORS,  # type: ignore
    environment=settings.ENV,
    traces_sample_rate=1.0 if settings.ENV == ENV.PROD else 0.0,
    profiles_sample_rate=1.0 if settings.ENV == ENV.PROD else 0.0,
    enable_tracing=True,
    release=settings.VERSION,
    auto_session_tracking=True,
)

static_path = Path(__file__).parent / "static"
well_known_path = static_path / ".well-known"


def get_background_tasks(
    background_tasks: BackgroundTasks = Depends(),
) -> BackgroundTasks:
    """Get background tasks."""

    return background_tasks


class CustomContext(BaseContext):
    """CustomContext for strawberry."""

    user: AuthContext | None

    def __init__(self, user: AuthContext | None = None):
        super(BaseContext, self).__init__()  # pylint: disable=bad-super-call
        self.user = user
        self.background_tasks = get_background_tasks()


graphql_app = GraphQLRouter(
    schema,
    context_getter=CustomContext,
    graphql_ide="apollo-sandbox",
    # multipart_uploads_enabled=True,
)

app.add_middleware(
    CORSMiddleware,
    allow_headers=settings.ALLOW_HEADERS.split(","),
    allow_origins=settings.ALLOW_ORIGINS.split(","),
    allow_methods=settings.ALLOW_METHODS.split(","),
    allow_credentials=True,
    expose_headers=[TIMEZONE_HEADER],
)
app.add_middleware(APIKeyMiddleware)
app.add_middleware(TimeZoneMiddleware)

app.include_router(graphql_app, prefix="/graphql")
app.include_router(devices, prefix="/api")


@app.get("/health")
async def healthcheck() -> bool:
    """Health check endpoint."""
    conn = Tortoise.get_connection("default")
    await conn.execute_query("SELECT 1;")
    return True


@app.get("/ready")
async def readiness() -> dict:
    """Readiness check endpoint."""
    results = {
        "postgres": await check_postgres(settings),
        "redis": await check_redis_connection(settings),
        "chat_api_service": await check_http_service(settings.CHAT_API_HOST),
        "cognito": await check_cognito_connection(settings),
        "s3": await verify_s3_connection(settings),
        "sqs": await verify_sqs_connection(settings),
    }
    if all(results.values()):
        return {"status": "healthy", "details": results}
    raise HTTPException(
        status_code=503, detail={"status": "unhealthy", "details": results}
    )


@app.get("/{path:path}")
async def serve_apple(path: str):
    """Serve apple-app-site-association file."""

    file_path = static_path / path
    if file_path.exists() and file_path.is_file():
        return FileResponse(file_path, media_type="application/octet-stream")


@app.get("/.well-known/{path:path}")
async def serve_apple_well_known(path: str):
    """Serve apple-app-site-association file."""

    file_path = well_known_path / path
    if file_path.exists() and file_path.is_file():
        return FileResponse(file_path, media_type="application/octet-stream")
    return {"error": f"File {path} not found."}


@app.get("/.well-known/{path:path}")
async def serve_android(path: str):
    """Serve assetlinks.json file."""

    file_path = well_known_path / path
    if file_path.exists() and file_path.is_file():
        return FileResponse(file_path, media_type="application/json")

    return {"error": f"File {path} not found."}


@app.get("/version")
async def version() -> dict:
    """Get version."""

    return {"version": settings.VERSION}


register_tortoise(
    app,
    config=get_tortoise_orm_config(settings.default_db_url),
    add_exception_handlers=True,
)


@app.exception_handler(Exception)
async def unhandled_exception_handler(
    request: Request, exc: Exception
) -> Response:
    return await http_exception_handler(
        request,
        HTTPException(
            500,
            "Internal server error",
            headers={"X-Request-ID": correlation_id.get() or ""},
        ),
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
