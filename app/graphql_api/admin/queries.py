from uuid import UUID

import strawberry
from ciba_participant.classes.crud import WebinarRepository
from ciba_participant.participant.data_preparer import extend_provider_data
from tortoise.exceptions import BaseORMException

from app.log.logging import logger
from app.auth.permissions import IsAuthenticated
from app.graphql_api.admin.types import ProviderType
from app.settings import get_settings

settings = get_settings()


# pylint: disable=unused-argument
async def get_providers(ids: list[UUID] | None = None) -> list[ProviderType]:
    """Return list of providers."""

    provider_data = await extend_provider_data()

    provider_type_users = []

    try:
        for user in provider_data:
            user_data = ProviderType(
                id=user["id"],
                full_name=f"{user['first_name']} {user['last_name']}",
                first_name=user["first_name"],
                last_name=user["last_name"],
                email=user["email"],
                chat_identity=user["chat_identity"],
                is_admin=user["role"] == "admin",
                old_chat_identity=user["old_chat_identity"],
            )
            provider_type_users.append(user_data)
        return provider_type_users
    except Exception as e:
        logger.exception(e)
        raise e


async def get_classes_creators() -> list[ProviderType]:
    """Return list of classes creators."""
    try:
        creators = await WebinarRepository.get_creators()
        provider_type_users = []

        for user in creators:
            user_data = ProviderType(
                id=user.id,
                full_name=user.full_name(),
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                chat_identity=user.chat_identity,
                is_admin=user.role == "admin",
            )
            provider_type_users.append(user_data)
        return provider_type_users
    except BaseORMException as e:
        logger.exception(e)
        raise e


@strawberry.type
class AdminQuery:
    """Admin graphql queries."""

    get_providers: list[ProviderType] = strawberry.field(
        resolver=get_providers, permission_classes=[IsAuthenticated]
    )
    get_classes_creators: list[ProviderType] = strawberry.field(
        resolver=get_classes_creators, permission_classes=[IsAuthenticated]
    )
