from typing import Optional
from uuid import UUID

import strawberry

# from ciba_participant.participant.models import ParticipantStatus, AutorizedRole


# ParticipantStatusEnum = strawberry.enum(ParticipantStatus)
# AuthorizedRoleEnum = strawberry.enum(AutorizedRole)


@strawberry.type
class ProviderType:
    """Provider graphql type."""

    id: UUID
    full_name: str
    first_name: str
    last_name: str
    email: str
    chat_identity: str
    description: Optional[str] = None
    avatar_url: Optional[str] = None
    is_admin: Optional[bool] = None
    old_chat_identity: Optional[str] = None
