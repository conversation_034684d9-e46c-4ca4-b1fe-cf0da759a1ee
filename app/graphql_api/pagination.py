from math import ceil
from typing import Generic, List, Type, TypeVar, Union, Optional

from strawberry import type as strawberry_type
from tortoise.models import Model
from tortoise.query_utils import Prefetch
from tortoise.queryset import QuerySet

GenericType = TypeVar("GenericType")  # pylint: disable=invalid-name


@strawberry_type
class Connection(Generic[GenericType]):
    """Represents a paginated relationship between page info and result set."""

    page_info: "PageInfo"
    items: list["GenericType"]
    success: bool = True
    error: Optional[str] = None


@strawberry_type
class PageInfo:
    """Pagination context with page/per_page based pagination."""

    has_next_page: bool
    has_previous_page: bool
    current_page: int
    per_page: int
    last_page: int
    total: int


TModel = TypeVar("TModel", bound=Model)  # pylint: disable=invalid-name


def _generate_query(
    query: QuerySet[TModel],
    prefetch_related: Union[bool, List[Union[str, Prefetch]]],
) -> QuerySet[TModel]:
    if prefetch_related:
        if prefetch_related is True:
            # pylint: disable=protected-access
            prefetch_related = [*query.model._meta.fetch_fields]

        return query.prefetch_related(*prefetch_related)

    return query


class Params:
    """Class for query params."""

    def __init__(self, per_page: int, page: int):
        self.per_page = per_page
        self.page = page


async def paginate(
    query: Union[QuerySet[TModel], Type[TModel]],
    params: Params,
    prefetch_related: Union[bool, List[Union[str, Prefetch]]] = False,
) -> Connection:
    """Paginate through database objects and return result."""
    if not isinstance(query, QuerySet):
        query = query.all()

    total = await query.count()
    items = (
        await _generate_query(query, prefetch_related)
        .offset((params.page - 1) * params.per_page)
        .limit(params.per_page)
        .all()
    )

    cursor = (params.page - 1) * params.per_page
    last_page = ceil(total / params.per_page)
    return Connection(
        page_info=PageInfo(
            has_previous_page=last_page + 1 >= params.page,
            has_next_page=len(items) + cursor < total,
            current_page=params.page,
            per_page=params.per_page,
            last_page=last_page,
            total=total,
        ),
        items=list(items),  # type: ignore
    )


__all__ = [
    "paginate",
    "Connection",
    "Params",
]
