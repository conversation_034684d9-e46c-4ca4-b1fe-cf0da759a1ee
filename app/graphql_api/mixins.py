from dataclasses import fields
from inspect import iscoroutinefunction
from typing import List, Optional, TypeVar

from tortoise.models import Model

TModel = TypeVar("TModel", bound=Model)  # pylint: disable=invalid-name


class MarshalMixin:
    """Mixin that able to transform instance to appropriate dataclass.

    We can customise attribute access using methods inside class that inherits
    this mixin with following patter.
    get_<field_name>(instance)
    <field_name> is an attribute of a dataclass

    Example:
        @staticmethod
        def get_name(instance: <YourInstance>):
            return "something"
    We also can provide aliases that maps instance attributes
    to a dataclass one

    _aliases = {<dataclass_attribute_key>: <instance_attribute_key>}

    Example:
        _aliases = {"name": "first_name"}
    Where "name" is dataclass attribute and "first_name" is instance attribute.
    """

    _aliases: Optional[dict] = None
    _ignore_missing_attributes = False
    _instance: Model

    @classmethod
    async def marshal(  # type: ignore
        cls, instance: TModel, defaults: Optional[dict] = None
    ):
        """Initialize class with an instance."""
        data = {}
        for field in fields(cls):
            field_name = field.name
            if defaults and field_name in defaults:
                data[field_name] = defaults[field_name]
                continue
            custom = f"get_{field_name}"
            if hasattr(cls, custom):
                method = getattr(cls, custom)
                if iscoroutinefunction(method):
                    data[field_name] = await method(instance)
                else:
                    data[field_name] = method(instance)
            else:
                if cls._aliases and cls._aliases.get(field_name):
                    field_name = cls._aliases[field.name]
                if hasattr(instance, field_name):
                    data[field.name] = getattr(instance, field_name)
        type_instance = cls(**data)
        type_instance._instance = instance
        return type_instance

    @classmethod
    async def marshal_many(  # type: ignore
        cls, instances: List, defaults: Optional[dict] = None
    ):
        """Marshal list of instances."""
        return [
            await cls.marshal(instance, defaults) for instance in instances
        ]
