import datetime
import time
from decimal import Decimal
from typing import Any, Optional
from uuid import <PERSON><PERSON><PERSON>

import strawberry
from strawberry.scalars import <PERSON><PERSON><PERSON>
from tortoise.fields import (
    BigIntField,
    BooleanField,
    CharEnumField,
    CharField,
    DateField,
    DatetimeField,
    DecimalField,
    FloatField,
    IntEnumField,
    IntField,
    JSONField,
    SmallIntField,
    TextField,
    TimeDeltaField,
    TimeField,
    UUIDField,
)
from tortoise.models import Model

maper = {
    BigIntField: int,
    BooleanField: bool,
    CharEnumField: str,
    CharField: str,
    DateField: datetime.date,
    DatetimeField: datetime.datetime,
    DecimalField: Decimal,
    JSONField: JSON,
    FloatField: float,
    IntEnumField: int,
    IntField: int,
    SmallIntField: int,
    TextField: str,
    TimeDeltaField: int,
    TimeField: time,
    UUIDField: UUID,
}

ALL = "__all__"


@strawberry.type
class SimpleResponseType:
    """
    Simple response type.
    """

    success: bool
    error: Optional[str] = strawberry.field(default=None)


class MetaContainer:
    """Meta data container.

    Keeps info about model and model fields that should be included or excluded
    """

    __slots__ = (
        "model",
        "fields",
        "exclude",
    )

    def __init__(self, meta: Optional["MetaContainer"] = None):
        self.model: Model | None = getattr(meta, "model", None)
        self.fields: list | str = getattr(meta, "fields", ALL)
        self.exclude: list = getattr(meta, "exclude", [])


class MetaGraphQlType(type):
    """Metaclass that construct graphql type dynamically based on db model."""

    class Meta(MetaContainer):
        pass

    _meta = Meta(None)

    # pylint: disable=bad-mcs-classmethod-argument
    def __new__(cls, *args: Any, **kwargs: Any) -> type:
        """Make strawberry graphql types dynamically based on db models."""
        ins = super().__new__(cls, *args, **kwargs)
        ch_meta = ins.Meta()
        meta = cls.Meta(ch_meta)
        if meta.model:
            for field, field_type in meta.model._meta.fields_map.items():
                if (
                    (field in meta.fields or meta.fields == "__all__")
                    and field not in meta.exclude
                    and field not in getattr(meta.model, "hidden", [])
                ):
                    if annotation := maper.get(type(field_type)):
                        if field in ins.__annotations__:
                            continue
                        if field_type.null is True:
                            ins.__annotations__[field] = Optional[annotation]
                        else:
                            ins.__annotations__[field] = annotation
        return ins
