from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

import strawberry
from strawberry.types import Info
from tortoise.expressions import Q
from ciba_participant.program.models import (
    Program,
    ProgramModule,
    ProgramModuleSection,
)
from ciba_participant.cohort.models import (
    Cohort,
    CohortMembers,
    CohortMembershipStatus,
)
from ciba_participant.classes.models import (
    Booking,
    BookingStatusEnum,
)
from ciba_participant.activity.models import ParticipantActivity, TrendEnum
from ciba_participant.cohort.models import CohortProgramModules
from ciba_participant.participant.models import Participant
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityDevice,
)

from app.settings import get_settings
from app.log.logging import logger
from app.auth.permissions import CanViewParticipant, IsAuthenticated
from app.graphql_api.program.types import (
    ModuleProgressType,
    ProgramCourseType,
    ProgramModuleType,
    ProgressDataType,
    MilestoneProgressDataType,
)

settings = get_settings()


async def render_module_with_sections(
    cohort_module: CohortProgramModules,
) -> tuple[list[ProgramModuleSection], ProgramModule]:
    """Render module with sections."""
    program_module_data: ProgramModule = await cohort_module.program_module

    sections: list[ProgramModuleSection] = await program_module_data.sections  # type: ignore

    required_fields = {
        ParticipantActivityEnum.ARTICLE: ["url"],
        ParticipantActivityEnum.PLAY: ["url"],
        ParticipantActivityEnum.RECIPES: ["url"],
        ParticipantActivityEnum.QUIZ: ["form_id"],
    }

    # Check section for url's and form id's, to not have problem with types
    for section in sections[
        :
    ]:  # Use a copy of the list to avoid modifying it while iterating
        # Generic validation for other activity types
        required_keys = required_fields.get(section.activity_type, [])
        missing_keys = [
            key for key in required_keys if not section.metadata.get(key)
        ]

        if missing_keys:
            sections.remove(section)
            logger.error(
                "Missing %s for section: %s",
                ", ".join(missing_keys),
                section.id,
            )

    return sections, program_module_data


async def process_program_module(
    cohort: Cohort, program_module: CohortProgramModules
) -> ProgramModuleType:
    sections, program_module_data = await render_module_with_sections(
        cohort_module=program_module
    )

    return ProgramModuleType(
        created_at=program_module_data.created_at,
        updated_at=program_module_data.updated_at,
        id=str(program_module_data.id),
        title=program_module_data.title,
        short_title=program_module_data.short_title,
        description=program_module_data.description,
        started_at=program_module.started_at,
        ended_at=program_module.ended_at,
        sections=sections,
        program_group=cohort,
    )


async def get_participant_program_modules(
    participant_id: UUID, info: Info
) -> list[ProgramModuleType]:
    """Get participant modules info by participant id.

    For now, we just get program modules from first participant group.
    """
    cohort_member = await CohortMembers.filter(
        participant_id=participant_id,
        status=CohortMembershipStatus.ACTIVE,
    ).first()
    if not cohort_member:
        raise ValueError(f"No cohort found for participant: {participant_id}")
    cohort = await cohort_member.cohort
    cohort_program_modules = await cohort.program_modules.all().order_by(
        "started_at"
    )

    output: list[ProgramModuleType] = []

    current_module: ProgramModuleType | None = None

    for program_module in cohort_program_modules:
        pm_type = await process_program_module(cohort, program_module)

        if pm_type.calculate_current():
            current_module = pm_type
            pm_type.current = True
        else:
            pm_type.current = False

        output.append(pm_type)

    if len(output) > 0 and current_module is None:
        output[0].current = True

    return output


async def get_participant_program_module(
    participant_id: UUID, program_module_id: UUID
) -> ProgramModuleType:
    """Get participant module info by participant id."""
    cohort_member = await CohortMembers.filter(
        participant_id=participant_id,
        status=CohortMembershipStatus.ACTIVE,
    ).first()

    if not cohort_member:
        raise ValueError(f"No cohort found for participant: {participant_id}")

    cohort = await cohort_member.cohort
    requested_module = await cohort.program_modules.filter(
        program_module_id=program_module_id
    ).first()

    if not requested_module:
        raise ValueError(
            f"Program module with ID '{program_module_id}' not found."
        )

    response = await process_program_module(cohort, requested_module)

    response.current = response.calculate_current()

    return response


async def get_participant_program_courses(
    participant_id: UUID,
) -> list[ProgramCourseType]:
    """Get participant program courses info by participant id."""
    program_courses = (
        await Program.filter(program_groups__participants__id=participant_id)
        .all()
        .order_by("started_at")
    )
    return [
        await ProgramCourseType.marshal(program_course)
        for program_course in program_courses
    ]


def get_activity_type_value(activity_type):
    """Returns the value of the activity_type."""
    if isinstance(activity_type, Enum):
        return activity_type.value
    if isinstance(activity_type, str):
        return activity_type
    raise TypeError("activity_type must be an Enum or string")


def filter_activities(
    activities,
    activity_type: ParticipantActivityEnum,
    start_date: datetime,
    end_date: datetime,
    device: Optional[ParticipantActivityDevice] = None,
) -> list[ParticipantActivity]:
    """Filters activities by type, device, and date range."""
    if not activities:
        logger.error(
            "No activities found for: %s - %s - %s - %s",
            activity_type,
            device,
            start_date,
            end_date,
        )
        return []
    return [
        activity
        for activity in activities
        if activity.activity_type == activity_type
        and (device is None or activity.activity_device == device)
        and start_date <= activity.created_at <= end_date
    ]


async def map_to_progress_data_type(
    section: ProgramModuleSection,
    activity: ParticipantActivity,
    values: list[ParticipantActivity],
) -> ProgressDataType | None:
    """Map activity to progress data type."""
    if not activity:
        return ProgressDataType.create(
            section_id=section.id,
            section_type=get_activity_type_value(section.activity_type),
            is_completed=False,
        )

    if (
        section.activity_type == ParticipantActivityEnum.GROUP
        and not section.metadata.get("url")
    ):
        return None

    metadata = {
        "start_date": activity.created_at,
        "end_date": (
            activity.created_at
            if activity.activity_type != ParticipantActivityEnum.GROUP
            else None
        ),
    }

    if activity.activity_type in [
        ParticipantActivityEnum.WEIGHT,
        ParticipantActivityEnum.ACTIVITY,
    ]:
        metadata["value"] = str(activity.value)
        values.sort(key=lambda x: x.created_at)
    else:
        values = []

    return ProgressDataType.create(
        section_id=section.id if section else None,
        section_type=get_activity_type_value(section.activity_type),
        is_completed=True,
        metadata=metadata,
        values=values,
    )


async def calculate_weight_trend(
    participant_id: UUID,
    cohort_module: CohortProgramModules,
) -> tuple[TrendEnum, TrendEnum] | tuple[TrendEnum, None] | tuple[None, None]:
    """Calculate the weight trend for the participant from the start of the cohort,
    until the end date of the given module."""
    participant_activities = await ParticipantActivity.filter(
        participant_id=participant_id,
        activity_type=ParticipantActivityEnum.WEIGHT,
        activity_device__in=[
            ParticipantActivityDevice.MANUAL_INPUT,
            ParticipantActivityDevice.WITHINGS,
        ],
        created_at__gte=cohort_module.cohort.started_at,
        created_at__lte=cohort_module.ended_at,
    ).all()
    # Find first weight input for this module
    first_module_weight_activity = (
        await ParticipantActivity.filter(
            participant_id=participant_id,
            activity_type=ParticipantActivityEnum.WEIGHT,
            activity_device__in=[
                ParticipantActivityDevice.MANUAL_INPUT,
                ParticipantActivityDevice.WITHINGS,
            ],
            created_at__gte=cohort_module.started_at,
            created_at__lte=cohort_module.ended_at,
        )
        .order_by("created_at")
        .first()
    )

    # Find previous weight input
    previous_module_weight_activity = None
    if first_module_weight_activity:
        previous_module_weight_activity = (
            await ParticipantActivity.filter(
                participant_id=participant_id,
                activity_type=ParticipantActivityEnum.WEIGHT,
                activity_device__in=[
                    ParticipantActivityDevice.MANUAL_INPUT,
                    ParticipantActivityDevice.WITHINGS,
                ],
                created_at__lt=first_module_weight_activity.created_at,
            )
            .order_by("-created_at")
            .first()
        )

    if not participant_activities:
        return None, None

    if len(participant_activities) == 1:
        return TrendEnum.FLAT, TrendEnum.FLAT

    first = min(participant_activities, key=lambda x: x.created_at).value
    last = max(participant_activities, key=lambda x: x.created_at).value

    def compare(
        first_value: float | int, last_value: float | int
    ) -> TrendEnum:
        if first_value < last_value:
            return TrendEnum.UP
        if first_value > last_value:
            return TrendEnum.DOWN
        return TrendEnum.FLAT

    if not first_module_weight_activity or not previous_module_weight_activity:
        return compare(first, last), TrendEnum.FLAT
    return compare(first, last), compare(
        previous_module_weight_activity.value,
        first_module_weight_activity.value,
    )


async def get_module_progress(
    program_module_id: UUID,
    participant_id: UUID,
    info: Info,
) -> ModuleProgressType | None:
    """Get program progress by participant id."""
    module_progress = ModuleProgressType(
        id=str(program_module_id),
        completed=False,
        sections=[],
        class_activities=[],
        chat_activities=[],
    )

    try:
        participant = await Participant.filter(id=participant_id).get()
        await participant.fetch_related("cohorts")

        active_cohorts = [
            cohort
            for cohort in participant.cohorts
            if cohort.status == CohortMembershipStatus.ACTIVE
        ]

        try:
            participant_cohort_id = active_cohorts[-1].cohort_id
        except IndexError:
            logger.exception("No active cohort found for participant")
            return None
    except Exception as e:
        logger.exception(f"Error finding participant: {e}")
        return None

    cohort_module = await (
        CohortProgramModules.filter(
            cohort_id=participant_cohort_id,
            program_module_id=program_module_id,
        )
        .prefetch_related("cohort")
        .get()
    )

    sections, _ = await render_module_with_sections(cohort_module)

    if not sections:
        logger.error("No sections found for module: %s", program_module_id)
        return module_progress

    section_map = {section.activity_type: section for section in sections}
    section_ids = [section.id for section in sections]
    participant_activities_module_sections = (
        await ParticipantActivity.filter(participant_id=participant_id)
        .filter(Q(section_id__in=section_ids) | Q(section_id=None))
        .all()
    )

    no_section_activities = [
        activity
        for activity in participant_activities_module_sections
        if activity.section_id is None
    ]
    if None in sections:
        sections.remove(None)

    section_id_to_section = {section.id: section for section in sections}
    section_activity_map = {section: [] for section in sections}

    for activity in participant_activities_module_sections:
        section = section_id_to_section.get(activity.section_id)
        if section:
            section_activity_map[section].append(activity)

    module_start_date = cohort_module.started_at
    module_end_date = cohort_module.ended_at

    if no_section_activities:
        module_no_section_weight = filter_activities(
            activities=no_section_activities,
            activity_type=ParticipantActivityEnum.WEIGHT,
            start_date=module_start_date,
            end_date=module_end_date,
        )

        module_no_section_activity_activities = filter_activities(
            activities=no_section_activities,
            activity_type=ParticipantActivityEnum.ACTIVITY,
            start_date=module_start_date,
            end_date=module_end_date,
        )

        if weight_section := section_map.get(ParticipantActivityEnum.WEIGHT):
            section_activity_map[weight_section].extend(
                module_no_section_weight
            )

        if activity_section := section_map.get(
            ParticipantActivityEnum.ACTIVITY
        ):
            section_activity_map[activity_section].extend(
                module_no_section_activity_activities
            )

    section_progress_data = []

    COMPLETED_STATUSES = [
        BookingStatusEnum.ATTENDED,
        BookingStatusEnum.WATCHED_RECORDING,
    ]

    bookings = (
        await Booking.filter(
            participant_id=participant_id,
            status__in=COMPLETED_STATUSES + [BookingStatusEnum.BOOKED],
        )
        .filter(
            Q(live_session__meeting_start_time__gt=module_start_date)
            & Q(live_session__meeting_start_time__lt=module_end_date)
        )
        .prefetch_related("live_session")
    )

    module_progress.class_activities.extend(
        list(
            map(
                lambda b: MilestoneProgressDataType(
                    completed=(b.status in COMPLETED_STATUSES),
                    title=b.live_session.title,
                    activity_date=b.updated_at,
                ),
                bookings,
            )
        )
    )

    chat_activities = (
        await ParticipantActivity.filter(participant_id=participant_id)
        .filter(activity_type=ParticipantActivityEnum.COACH.value)
        .filter(
            Q(created_at__gt=module_start_date)
            & Q(created_at__lt=module_end_date)
        )
    )

    module_progress.chat_activities.extend(
        list(
            map(
                lambda c: MilestoneProgressDataType(
                    completed=True,
                    activity_date=c.created_at,
                    title="",
                ),
                chat_activities,
            )
        )
    )

    for section, activities in section_activity_map.items():
        if activities:
            latest_activity = max(activities, key=lambda x: x.created_at)
            progress_data = await map_to_progress_data_type(
                section, latest_activity, values=activities
            )

            if progress_data:
                section_progress_data.append(progress_data)
        else:
            progress_data = ProgressDataType.create(
                section_id=section.id if section else None,
                section_type=get_activity_type_value(section.activity_type),
                is_completed=False,
                metadata={},
            )
            section_progress_data.append(progress_data)

    compared_weight, program_weight_trend = await calculate_weight_trend(
        participant_id, cohort_module
    )

    module_progress.completed = (
        all(progress_data.completed for progress_data in section_progress_data)
        and any(
            class_progress_data.completed
            for class_progress_data in module_progress.class_activities
        )
        and any(
            chat_progress_data.completed
            for chat_progress_data in module_progress.chat_activities
        )
    )
    module_progress.sections = section_progress_data
    module_progress.program_weight_trend = program_weight_trend
    module_progress.previous_module_weight_trend = compared_weight

    return module_progress


@strawberry.type()
class ProgramQuery:
    """Program graphql queries."""

    get_participant_program_module: ProgramModuleType = strawberry.field(
        resolver=get_participant_program_module,
        permission_classes=[IsAuthenticated, CanViewParticipant],
    )
    get_participant_program_modules = strawberry.field(
        resolver=get_participant_program_modules,
        permission_classes=[IsAuthenticated, CanViewParticipant],
    )
    get_participant_program_courses = strawberry.field(
        resolver=get_participant_program_courses,
        permission_classes=[IsAuthenticated, CanViewParticipant],
    )
    get_module_progress = strawberry.field(
        resolver=get_module_progress,
        permission_classes=[IsAuthenticated, CanViewParticipant],
    )
