from dataclasses import asdict
from datetime import datetime
from typing import Optional

import strawberry

from app.graphql_api.program.pydantic_models import (
    FILE,
    FOOD,
    INPUT,
    TYPE_FORM,
    VIDEO,
    ZOOM,
)
from app.models.program import (
    ModuleDataTypes,
)


@strawberry.input
class PersonalSuccessInput:
    """Personal success section progress input."""

    response_id: Optional[str] = None
    value: Optional[str] = None


@strawberry.input
class ProgramModuleUpdateInput:
    """Program module update input."""

    title: str
    short_title: str
    started_at: datetime
    ended_at: datetime
    description: str | None


@strawberry.input
class SectionPersonalSuccessInput:
    """Personal success input."""

    form_id: str | None = None
    type: str = TYPE_FORM


@strawberry.input
class SectionCurriculumInput:
    """Curriculum input."""

    url: str | None = None
    type: str = FILE


@strawberry.input
class SectionCoachingCallInput:
    """Coaching call input."""

    started_at: datetime | None = None
    url: str | None = None
    recording_url: str | None = None
    type: str = ZOOM


@strawberry.input
class SectionVideoTypeInput:
    """Coaching call input."""

    url: str | None = None
    type: str = VIDEO
    is_intro: bool = False


@strawberry.input
class SectionRecipeTypeInput:
    """Coaching call input."""

    url: str | None = None
    type: str = FILE


@strawberry.input
class WeightTypeInput:
    """Weight input."""

    type: str = INPUT


@strawberry.input
class FoodTypeInput:
    """Food input."""

    type: str = FOOD


@strawberry.input
class ActivityTypeInput:
    """Activity input."""

    type: str = INPUT


@strawberry.input
class QuizTypeInput:
    """Quiz input."""

    type: str = FILE


@strawberry.input
class ProgramModuleSectionInput:
    """Program module section input."""

    title: str
    description: str | None = None
    personal_success: SectionPersonalSuccessInput | None = None
    curriculum: SectionCurriculumInput | None = None
    coaching_call: SectionCoachingCallInput | None = None
    video_type: SectionVideoTypeInput | None = None
    recipe_type: SectionRecipeTypeInput | None = None
    weight_type: WeightTypeInput | None = None
    food_type: FoodTypeInput | None = None
    activity_type: ActivityTypeInput | None = None
    quiz_type: QuizTypeInput | None = None

    TYPE_MAP = {
        "personal_success": (
            SectionPersonalSuccessInput,
            ModuleDataTypes.personal_success.value,
        ),
        "curriculum": (
            SectionCurriculumInput,
            ModuleDataTypes.curriculum.value,
        ),
        "coaching_call": (
            SectionCoachingCallInput,
            ModuleDataTypes.coaching_call.value,
        ),
        "video_type": (
            SectionVideoTypeInput,
            ModuleDataTypes.video_type.value,
        ),
        "recipe_type": (
            SectionRecipeTypeInput,
            ModuleDataTypes.recipe_type.value,
        ),
        "weight_type": (WeightTypeInput, ModuleDataTypes.weight_type.value),
        "food_type": (FoodTypeInput, ModuleDataTypes.food_type.value),
        "activity_type": (
            ActivityTypeInput,
            ModuleDataTypes.activity_type.value,
        ),
        "quiz_type": (QuizTypeInput, ModuleDataTypes.quiz_type.value),
    }

    def to_dict(self) -> dict:
        """Convert input to dict to update section instance."""
        input_data = asdict(self)
        for field, (input_type, module_type) in self.TYPE_MAP.items():
            if getattr(self, field):
                input_data["metadata"] = asdict(getattr(self, field))
                if field == "coaching_call":
                    started_at = input_data["metadata"]["started_at"]
                    input_data["metadata"]["started_at"] = (
                        started_at.isoformat() if started_at else started_at
                    )
                    input_data["type"] = module_type
                    break
                input_data["metadata"]["type"] = input_type.type
                input_data["type"] = module_type
                break
        else:
            raise ValueError(
                "One of [personal_success, curriculum, coaching_call] "
                "should be provided."
            )
        return input_data


@strawberry.input
class ProgramModuleInput:
    """Program module input."""

    title: str
    short_title: str
    description: str
    started_at: datetime
    ended_at: datetime
