# pylint: disable=no-member

from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

import pendulum
import strawberry
from ciba_participant.activity.models import (
    ParticipantActivity,
    TrendEnum,
)
from ciba_participant.program.models import (
    Program,
    ProgramModule,
    ProgramModuleSection,
)

from app.graphql_api.mixins import MarshalMixin
from app.graphql_api.participant.types import ParticipantType
from app.graphql_api.program.pydantic_models import (
    FILE,
    FOOD,
    INPUT,
    TYPE_FORM,
    ZOOM,
)
from app.graphql_api.types import MetaGraphQlType
from app.integrations.s3 import create_presigned_url
from app.models.program import (
    ProgramModuleSectionProgress,
    ModuleDataTypes,
)
from app.settings import get_settings

settings = get_settings()


@strawberry.type
class PersonalSuccessType:
    """Personal success module graphql type."""

    url: str | None = None
    form_id: str
    type: str = TYPE_FORM


@strawberry.type
class CurriculumType:
    """Curriculum module graphql type."""

    url: str
    type: str = FILE
    is_intro: bool = False

    @strawberry.field
    def signed_url(self) -> str:
        """Generate pre signed url from a bucket or return url."""
        if isinstance(self.url, str) and self.url.startswith("http"):
            return self.url
        return create_presigned_url(settings.AWS_BUCKET_NAME, self.url)


@strawberry.type
class CoachingCallType:
    """Coaching call module graphql type."""

    url: str
    started_at: str | None = None
    recording_url: str | None = None
    type: str = ZOOM


@strawberry.type
class DietitianCallType:
    """Dietitian call module graphql type."""

    url: str
    started_at: str | None = None
    recording_url: str | None = None
    type: str = ZOOM


@strawberry.type
class WeightType:
    """Weight call module graphql type."""

    type: str = INPUT


@strawberry.type
class VideoType:
    """Video call module graphql type."""

    url: str
    type: str = FILE
    is_intro: bool = False

    @strawberry.field
    def signed_url(self) -> str:
        """Generate pre signed url from a bucket or return url."""
        if isinstance(self.url, str) and self.url.startswith("http"):
            return self.url
        return create_presigned_url(settings.AWS_BUCKET_NAME, self.url)


@strawberry.type
class FoodType:
    """Food call module graphql type."""

    type: str = FOOD


@strawberry.type
class ActivityType:
    """Activity call module graphql type."""

    type: str = INPUT


@strawberry.type
class RecipeType:
    """Recipe module graphql type."""

    url: str
    type: str = FILE

    @strawberry.field
    def signed_url(self) -> str:
        """Generate pre signed url from a bucket or return url."""
        if isinstance(self.url, str) and self.url.startswith("http"):
            return self.url
        return create_presigned_url(settings.AWS_BUCKET_NAME, self.url)


@strawberry.type
class QuizType:
    """Personal success module graphql type."""

    form_id: str
    type: str = TYPE_FORM


@strawberry.type
class ProgramModuleSectionType:
    """Program module section graphql type."""

    id: str
    title: str
    type: strawberry.enum(ModuleDataTypes)  # type: ignore
    description: str | None = None
    metadata: (
        PersonalSuccessType
        | CurriculumType
        | CoachingCallType
        | DietitianCallType
        | WeightType
        | FoodType
        | VideoType
        | ActivityType
    )

    @strawberry.field
    def type(self):
        """Return module type."""
        return self.activity_type.value

    @strawberry.field  # type: ignore
    def data(
        self,
    ) -> (
        PersonalSuccessType
        | CurriculumType
        | CoachingCallType
        | DietitianCallType
        | WeightType
        | FoodType
        | VideoType
        | QuizType
        | RecipeType
        | ActivityType
    ):
        """Return metadata module type."""
        module_type = module_types.get(self.activity_type.value)
        assert module_type is not None
        meta = {k: v for k, v in self.metadata.items() if v not in [None, ""]}
        data = module_type(**meta)
        return data


module_types = {
    ModuleDataTypes.dietitian_call.value: DietitianCallType,
    ModuleDataTypes.coaching_call.value: CoachingCallType,
    ModuleDataTypes.curriculum.value: CurriculumType,
    ModuleDataTypes.personal_success.value: PersonalSuccessType,
    ModuleDataTypes.weight_type.value: WeightType,
    ModuleDataTypes.food_type.value: FoodType,
    ModuleDataTypes.video_type.value: VideoType,
    ModuleDataTypes.quiz_type.value: QuizType,
    ModuleDataTypes.recipe_type.value: RecipeType,
    ModuleDataTypes.activity_type.value: ActivityType,
}


@strawberry.type
class ProgramGroupType:
    """ProgramGroup model based graphql type."""

    id: str
    name: str
    created_at: datetime
    updated_at: datetime
    started_at: datetime
    limit: int
    program_course_id: Optional[UUID] = strawberry.field(
        deprecation_reason="Program course is out of logic",
        default=UUID("00000000-00000000-00000000-00000000"),
    )

    participants: list[ParticipantType]


@strawberry.type
class ProgramModuleType:
    """ProgramModule model based graphql type."""

    id: str
    short_title: str
    title: str
    started_at: datetime
    ended_at: datetime
    description: str | None
    created_at: datetime
    updated_at: datetime

    sections: list[ProgramModuleSectionType]
    program_group: ProgramGroupType

    _current: strawberry.Private[bool | None] = None

    def set_current(self, current: bool):
        """Set current program module."""
        self._current = current

    @strawberry.field
    def current(self) -> bool:
        """Current module strawberry field."""
        if self._current is not None:
            return self._current
        return self.calculate_current()

    def calculate_current(self) -> bool:
        """Define whether a module is current based on dates."""
        return self.started_at <= pendulum.now(timezone.utc) < self.ended_at


@strawberry.type
class LandingCohortsSelectionType(MarshalMixin, metaclass=MetaGraphQlType):
    """Program model based graphql type."""

    program_group: ProgramGroupType


@strawberry.type
class LandingProgramModuleSectionType(MarshalMixin, metaclass=MetaGraphQlType):
    """ProgramModule model based graphql type."""

    class Meta:
        model = ProgramModuleSection

    program_module: ProgramModuleType


@strawberry.type
class ProgramType(MarshalMixin, metaclass=MetaGraphQlType):
    """Program model based graphql type."""

    class Meta:
        model = Program


@strawberry.type
class ProgramCourseType(MarshalMixin, metaclass=MetaGraphQlType):
    """ProgramCourse model based graphql type."""

    # pylint: disable=fixme
    class Meta:
        model = Program

    program: ProgramType


@strawberry.type
class ProgressMetadataType:
    """Common success module graphql type."""

    start_date: str = datetime.now().isoformat()
    end_date: str | None = datetime.now().isoformat()


@strawberry.type
class CurriculumProgressMetadataType(ProgressMetadataType):
    """Curriculum success module graphql type."""


@strawberry.type
class DietitianCallProgressMetadataType(ProgressMetadataType):
    """DietitianCall success module graphql type."""


@strawberry.type
class CoachingCallProgressMetadataType(ProgressMetadataType):
    """CoachingCall success module graphql type."""


@strawberry.type
class PersonalSuccessProgressMetadataType(ProgressMetadataType):
    """Personal success module graphql type."""

    response_id: str | None = None


@strawberry.type
class WeightTypeProgressMetadataType(ProgressMetadataType):
    """WeightType success module graphql type."""

    activity_id: Optional[UUID] = None
    value: str | None = None
    device_type: str | None = None


@strawberry.type
class VideoTypeProgressMetadataType(ProgressMetadataType):
    """FoodProgress success module graphql type."""

    value: str | None = None


@strawberry.type
class FoodTypeProgressMetadataType(ProgressMetadataType):
    """FoodProgress success module graphql type."""

    value: str | None = None


@strawberry.type
class ActivityTypeProgressMetadataType(ProgressMetadataType):
    """ActivityType success module graphql type."""

    activity_id: Optional[UUID] = None
    value: str | None = None
    device_type: str | None = None


@strawberry.type
class QuizTypeProgressMetadataType(ProgressMetadataType):
    """QuizType success module graphql type."""

    response_id: str | None = None


@strawberry.type
class RecipeTypeProgressMetadataType(ProgressMetadataType):
    """ecipeType success module graphql type."""


@strawberry.type
class NotMetadataType:
    """Not completed metadata type."""

    value: bool = False


@strawberry.type
class NotValueType:
    """Not completed value type."""

    value: bool = False


progress_types = {
    # no additional fields
    ModuleDataTypes.curriculum.value: CurriculumProgressMetadataType,
    ModuleDataTypes.dietitian_call.value: DietitianCallProgressMetadataType,
    ModuleDataTypes.coaching_call.value: CoachingCallProgressMetadataType,
    ModuleDataTypes.recipe_type.value: RecipeTypeProgressMetadataType,
    # value
    ModuleDataTypes.weight_type.value: WeightTypeProgressMetadataType,
    ModuleDataTypes.video_type.value: VideoTypeProgressMetadataType,
    ModuleDataTypes.food_type.value: FoodTypeProgressMetadataType,
    ModuleDataTypes.activity_type.value: ActivityTypeProgressMetadataType,
    # response_id
    ModuleDataTypes.quiz_type.value: QuizTypeProgressMetadataType,
    ModuleDataTypes.personal_success.value: (
        PersonalSuccessProgressMetadataType
    ),
}


@strawberry.type
class MilestoneProgressDataType:
    completed: bool
    activity_date: Optional[datetime]
    title: Optional[str]


@strawberry.type
class ProgressDataType:
    section_id: str
    section_type: str
    metadata: (
        DietitianCallProgressMetadataType
        | CoachingCallProgressMetadataType
        | CurriculumProgressMetadataType
        | PersonalSuccessProgressMetadataType
        | WeightTypeProgressMetadataType
        | FoodTypeProgressMetadataType
        | VideoTypeProgressMetadataType
        | QuizTypeProgressMetadataType
        | RecipeTypeProgressMetadataType
        | ActivityTypeProgressMetadataType
        | NotMetadataType
    )
    values: Optional[
        list[WeightTypeProgressMetadataType | ActivityTypeProgressMetadataType]
    ] = None
    completed: bool

    # pylint: disable=too-many-arguments
    @classmethod
    def create(
        cls,
        *,
        section_id: UUID,
        section_type: str,
        is_completed: bool,
        metadata: dict | None = None,
        values: list[ParticipantActivity] | None = None,
    ) -> "ProgressDataType":
        """Create progress data type."""
        if is_completed:
            progress_type = progress_types.get(section_type)

            type_values = [
                progress_type(
                    activity_id=value.id,
                    start_date=value.created_at,
                    end_date=value.created_at,
                    value=value.value,
                    device_type=value.activity_device.value,
                )
                for value in values
                if values
            ]

            return cls(
                section_id=str(section_id),
                section_type=section_type,
                metadata=(
                    NotMetadataType()
                    if metadata is None
                    else progress_type(**metadata)
                ),
                values=type_values,
                completed=True,
            )

        return cls(
            section_id=str(section_id),
            section_type=section_type,
            metadata=NotMetadataType(),
            values=[],
            completed=False,
        )


@strawberry.type
class ModuleProgressType:
    """ModuleProgress model based graphql type."""

    id: str
    completed: bool
    program_weight_trend: strawberry.enum(TrendEnum) | None = None
    previous_module_weight_trend: strawberry.enum(TrendEnum) | None = None
    sections: Optional[list[ProgressDataType]]
    class_activities: Optional[list[MilestoneProgressDataType]]
    chat_activities: Optional[list[MilestoneProgressDataType]]

    @staticmethod
    async def is_completed(
        progress_sections: list[ProgramModuleSectionProgress],
        program_module: ProgramModule,
    ) -> bool:
        """Check whether a module is completed."""
        module_sections = (
            await program_module.sections.all().count()  # type: ignore
        )
        completed_sections = [
            progress_section.id
            for progress_section in progress_sections
            if progress_section.is_completed
        ]
        return len(completed_sections) == module_sections


@strawberry.type
class ProgramGroupCourseType:
    """success upload with count of updates"""

    completed: bool
    sections_add: list[str]
    sections_not: list[str]
