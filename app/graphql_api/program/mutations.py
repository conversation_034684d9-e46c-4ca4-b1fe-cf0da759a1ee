from datetime import datetime, timezone
from typing import Annotated, Optional, Union
from uuid import UUID, uuid4

import pendulum
import strawberry
from strawberry.types import Info
from tortoise.expressions import Q

from ciba_participant.participant.models import (
    SoleraParticipant,
    ParticipantStatus,
)
from ciba_participant.classes.models import LiveSession
from ciba_participant.cohort.models import (
    CohortMembers,
    CohortMembershipStatus,
)
from ciba_participant.program.models import (
    Program,
    ProgramModuleSection,
)
from ciba_participant.activity.crud import ParticipantActivityRepository
from ciba_participant.activity.schemas import ParticipantActivityInput
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ActivityUnit,
    ParticipantActivity,
)
from ciba_participant.solera.handler import (
    Solera<PERSON><PERSON><PERSON>,
    RegistrationFailed as SoleraRegistrationFailed,
)
from ciba_participant.classes.models import BookingStatusEnum

from app.graphql_api.program.queries import get_module_progress
from app.graphql_api.mocks.company import NoCompanyData
from app.log.logging import logger
from app.settings import get_settings
from app.auth.permissions import IsAuthenticated
from app.graphql_api.participant.errors import (
    ParticipantDneError,
    ParticipantDuplicatedError,
    RegistrationFailed,
    AlreadyEnrolled,
)
from ciba_participant.solera.handler import (
    ParticipantDuplicatedError as SoleraParticipantDuplicatedError,
)
from app.graphql_api.program.errors import (
    DoesNotBelongToCourse,
    ProgramGroupCourse,
)
from app.graphql_api.program.inputs import (
    PersonalSuccessInput,
)
from app.graphql_api.program.services import ProgressService
from app.graphql_api.program.types import (
    ModuleProgressType,
    ProgramGroupType,
)
from app.graphql_api.classes.mutations.update_booking_status import (
    update_status_by_participant_id,
)


settings = get_settings()


@strawberry.type
class CohortsData:
    cohorts: list[ProgramGroupType]


LandResponse = Annotated[
    Union[
        None,
        CohortsData,
        ProgramGroupCourse,
        ParticipantDneError,
        ParticipantDuplicatedError,
        NoCompanyData,
        RegistrationFailed,
        AlreadyEnrolled,
    ],
    strawberry.union(
        "LandResponse",
        [
            CohortsData,
            ProgramGroupCourse,
            ParticipantDneError,
            ParticipantDuplicatedError,
            NoCompanyData,
            RegistrationFailed,
            AlreadyEnrolled,
        ],
    ),
]


async def make_progress(
    info: Info,
    section_id: UUID,
    program_module_id: Optional[UUID] = None,
    data: Optional[PersonalSuccessInput] = None,
) -> ModuleProgressType | DoesNotBelongToCourse | None:
    """Make program progress."""
    participant_id = info.context.user.sub

    section_has_user_activity = (
        await ParticipantActivity()
        .filter(
            participant_id=participant_id,
        )
        .filter(Q(section_id=section_id) | Q(live_session_id=section_id))
        .first()
    )

    program_module_section = (
        await ProgramModuleSection().filter(id=section_id).get_or_none()
    )
    cohort_member = (
        await CohortMembers()
        .filter(
            participant_id=participant_id,
            status=CohortMembershipStatus.ACTIVE,
        )
        .prefetch_related("cohort")
        .get_or_none()
    )

    if not cohort_member:
        logger.warning("User does not belong to any cohort")
        return DoesNotBelongToCourse()

    if not program_module_section:
        live_session = await LiveSession.filter(id=section_id).get_or_none()
        if live_session:
            await update_status_by_participant_id(
                participant_id=participant_id,
                live_session_id=section_id,
                status=(
                    BookingStatusEnum.WATCHED_RECORDING
                    if live_session.recording_url
                    else BookingStatusEnum.ATTENDED
                ),
            )

            program_module_section = ProgramModuleSection(
                id=section_id,
                title=live_session.title,
                description=live_session.description,
                created_at=pendulum.now("UTC"),
                updated_at=pendulum.now("UTC"),
                metadata={
                    "started_at": live_session.meeting_start_time,
                    "url": live_session.zoom_link,
                    "type": "zoom",
                },
                program_module_id=uuid4(),  # Use random uuid to not have validation issues
                activity_type=ParticipantActivityEnum.GROUP,
                activity_category=ParticipantActivityCategory.ACTIVITY,
            )

    activity_date = pendulum.now("UTC")
    if not section_has_user_activity:
        cohort_start_date = pendulum.instance(cohort_member.cohort.started_at)
        if cohort_start_date > activity_date:
            activity_date = pendulum.datetime(
                cohort_start_date.year,
                cohort_start_date.month,
                cohort_start_date.day,
                activity_date.hour,
                activity_date.minute,
                activity_date.second,
            ).add(days=1)

        await ProgressService.make_progress(
            program_module_section,
            participant_id,
            data,
            custom_date=activity_date,
        )

    elif section_has_user_activity.activity_type in [
        ParticipantActivityEnum.WEIGHT,
        ParticipantActivityEnum.ACTIVITY,
    ]:
        await ProgressService.make_progress(
            program_module_section,
            participant_id,
            data,
        )

    module_progress = None
    if program_module_id:
        module_progress = await get_module_progress(
            program_module_id=program_module_id,
            participant_id=info.context.user.sub,
            info=info,
        )

    return module_progress


async def add_chat_activity(
    participant_id: str,
) -> bool:
    """Make program progress."""

    try:
        activity = ParticipantActivityInput(
            participant_id=participant_id,
            activity_type=ParticipantActivityEnum.COACH,
            activity_category=ParticipantActivityCategory.ACTIVITY,
            activity_device=ParticipantActivityDevice.MANUAL_INPUT,
            unit=ActivityUnit.ACTION,
            value="1",
        )
        await ParticipantActivityRepository.create_participant_activity(
            activity
        )
        return True
    # pylint: disable=broad-exception-caught
    except Exception as e:
        logger.exception("Got exception: %s", e)
        return False


def find_closest_section(
    sections: list[LiveSession],
) -> LiveSession:
    """Find the closest section to the current time."""
    now = pendulum.now("UTC")  # Get current datetime in UTC
    closest_section = None
    smallest_diff = pendulum.duration(
        years=1000
    )  # Use a large initial difference

    for section in sections:
        # Parse the ISO formatted date string using Pendulum
        started_at = pendulum.instance(section.meeting_start_time, tz="UTC")

        # Calculate the absolute duration
        # difference between now and the started_at time
        time_diff = abs(started_at - now)  # type: ignore # noqa: E501, F541,F401, F841

        # Check if this time difference
        # is smaller than the smallest found so far
        if time_diff < smallest_diff:
            smallest_diff = time_diff
            closest_section = section

    return closest_section


# pylint: disable=too-many-locals
async def land_participant(look_up_key: str) -> LandResponse:
    """Land participant solera participant."""
    participant = await SoleraHandler().create_user_no_cohort(look_up_key)

    if isinstance(participant, SoleraParticipantDuplicatedError):
        logger.warning(
            "Participant already exists for look_up: %s", look_up_key
        )
        return ParticipantDuplicatedError()

    if not participant:
        logger.warning("No participant for look_up: %s", look_up_key)
        return ParticipantDneError()

    if isinstance(participant, SoleraRegistrationFailed):
        return RegistrationFailed()

    if participant.status == ParticipantStatus.ACTIVE:
        return AlreadyEnrolled()

    solera_participant = await SoleraParticipant.filter(
        participant_id=participant.id,
        status=ParticipantStatus.ACTIVE,
    ).first()
    participant_solera_program = solera_participant.solera_program_id

    # here we get program id that belong to is the same as solera program
    program = await Program.filter(
        title=participant_solera_program,
    ).first()

    # here we get cohort that belong to specific program id and is starting in the future
    cohorts = await program.cohorts

    cohorts_filtered = []
    logger.info("Found %s cohorts for program %s", len(cohorts), program.title)
    for cohort in cohorts:
        participants = await cohort.participants
        have_free_places = len(participants) < cohort.limit
        starting_future = cohort.started_at > datetime.now(timezone.utc)
        if have_free_places and starting_future:
            cohorts_filtered.append(cohort)
    logger.info(
        "Filtered %s cohorts for program %s",
        len(cohorts_filtered),
        program.title,
    )

    selected_cohorts: list[ProgramGroupType] = []

    for cohort in cohorts_filtered:
        group = ProgramGroupType(
            id=cohort.id,
            name=cohort.name,
            started_at=cohort.started_at,
            created_at=cohort.created_at,
            updated_at=cohort.updated_at,
            limit=cohort.limit,
            participants=[],
        )
        selected_cohorts.append(group)

    logger.info(
        "Selected %s cohorts for program %s",
        len(selected_cohorts),
        program.title,
    )

    return CohortsData(cohorts=selected_cohorts)


@strawberry.type()
class ProgramMutation:
    """Program graphql mutations."""

    add_chat_activity = strawberry.field(
        resolver=add_chat_activity,
        permission_classes=[IsAuthenticated],
    )
    make_progress = strawberry.field(
        resolver=make_progress,
        permission_classes=[IsAuthenticated],
    )
    land_participant = strawberry.field(
        resolver=land_participant, permission_classes=[]
    )
