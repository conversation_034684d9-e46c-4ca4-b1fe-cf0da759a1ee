from dataclasses import asdict
from datetime import datetime
from typing import Optional
from uuid import UUID
from ciba_participant.activity.models import (
    ParticipantActivity,
    ActivityUnit,
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
)
from ciba_participant.program.models import ProgramModuleSection

from app.graphql_api.program.inputs import PersonalSuccessInput


class ProgressService:
    """Service that keeps program progress flow logic."""

    @classmethod
    async def make_progress(
        cls,
        program_section: ProgramModuleSection,
        participant_id: UUID,
        data: PersonalSuccessInput | None,
        custom_date: Optional[datetime] = None,
    ) -> ParticipantActivity:
        """Save program progress."""
        # Convert the object to a dictionary
        input_data_dict = asdict(data) if data else {}
        # Filter out keys with None values
        input_data = {
            k: v for k, v in input_data_dict.items() if v is not None
        }

        unit = ActivityUnit.ACTION
        value = input_data.get("value", "1")
        category = ParticipantActivityCategory.ACTIVITY
        if program_section.activity_type == ParticipantActivityEnum.WEIGHT:
            unit = ActivityUnit.LB
            category = ParticipantActivityCategory.WEIGHT

        participant_activity = ParticipantActivity(
            participant_id=participant_id,
            activity_type=program_section.activity_type,
            activity_device=ParticipantActivityDevice.MANUAL_INPUT,
            activity_category=category,
            value=value,
            unit=unit,
        )

        if custom_date:
            participant_activity.created_at = custom_date

        if program_section.activity_type == ParticipantActivityEnum.GROUP:
            participant_activity.live_session_id = program_section.id
        else:
            participant_activity.section_id = program_section.id

        await participant_activity.save()

        return participant_activity
