import strawberry


@strawberry.type
class DoesNotBelongToCourse:
    """Error type for cases when a participant does not belong to a course."""

    message: str = "You do not belong to a program course."


@strawberry.type
class ModuleHasNotStarted:
    """Error type for cases when a module has not started yet."""

    message: str = "<PERSON><PERSON><PERSON> has not started yet."


@strawberry.type
class ParticipantAlreadyInGroup:
    """Error type for cases when a user already assigned to a group"""

    message: str = (
        "Participant already assigned to some group. "
        "Remove from old one and reassign."
    )


@strawberry.type
class ProgramGroupCourse:
    """Error for cases when a group or course dont exist"""

    message: str = "Check UUID!."


@strawberry.type
class ProgramGroupCourseFile:
    """Error for cases when a filee dont exist or not wright"""

    message: str = "File not exist or not normal!."


@strawberry.type
class NotExist:
    """Error to say that smth is not exist"""

    message: str = "Not exist"
