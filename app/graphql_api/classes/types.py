from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry

from app.graphql_api.admin.types import ProviderType
from app.graphql_api.classes.enums import (
    ClassTopic,
    TimeOfDay,
    ClassStatus,
    BookingStatus,
)


@strawberry.input
class ClassFilter:
    search: Optional[str] = None
    topic: Optional[ClassTopic] = None  # type: ignore
    health_coach: Optional[UUID] = None
    time_of_day: Optional[TimeOfDay] = None  # type: ignore
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


@strawberry.type
class ClassElement:
    id: UUID
    topic: ClassTopic  # type: ignore
    title: str
    description: str
    started_at: datetime
    status: ClassStatus
    presenter: Optional[ProviderType]
    join_link: Optional[str] = strawberry.field(name="joinLink", default=None)
    recording_link: Optional[str] = strawberry.field(
        name="recordingLink", default=None
    )


@strawberry.type
class UpdateBookingResponseType:
    booking_id: Optional[UUID] = strawberry.field(
        name="bookingId", default=None
    )
    success: bool
    error: Optional[str] = None


@strawberry.input
class BookingFilter(ClassFilter):
    with_recording: Optional[bool] = None
    booking_status: Optional[list[BookingStatus]] = ()  # type: ignore
