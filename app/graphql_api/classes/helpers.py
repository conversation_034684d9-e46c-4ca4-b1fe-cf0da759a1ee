from datetime import datetime, timedelta
from uuid import UUID

import pendulum
from ciba_participant.classes.models import (
    LiveSession,
    BookingStatusEnum,
    Booking,
)

from app.graphql_api.classes.enums import ClassStatus


def check_join_availability(class_date: datetime) -> bool:
    """
    method to check if a class is about to start or has already started
    """
    class_date_instance = pendulum.instance(class_date)
    return (
        class_date_instance.subtract(minutes=30)
        <= pendulum.now()
        < class_date_instance.add(hours=1)
    )


def check_participant_booking(
    bookings: list[Booking], participant_id: UUID
) -> bool:
    """
    method to check if a participant is registered in a list of bookings
    """
    for booking in bookings:
        if booking.participant_id == participant_id:
            return True

    return False


def get_class_status(
    session: LiveSession, participant_id: UUID
) -> ClassStatus:
    """
    method to get a class status
    """
    if pendulum.now() > session.meeting_start_time + timedelta(hours=1):
        return ClassStatus.PAST

    bookings = (
        [
            booking
            for booking in session.bookings
            if booking.status == BookingStatusEnum.BOOKED
        ]
        if session.bookings
        else []
    )

    if check_participant_booking(bookings, participant_id):
        if check_join_availability(session.meeting_start_time):
            return ClassStatus.AVAILABLE_TO_JOIN

        return ClassStatus.BOOKED

    if len(bookings) >= session.webinar.max_capacity:
        return ClassStatus.FULLY_BOOKED

    return ClassStatus.AVAILABLE_TO_BOOK
