from typing import Optional
from uuid import UUID
import pendulum

from strawberry import Info
from ciba_participant.classes.models import (
    Booking,
    LiveSession,
    BookingStatusEnum,
)
from ciba_participant.classes.service import get_sessions_by_time_of_day
from ciba_participant.helpers.timezone_conversion import get_timezone_name
from tortoise.queryset import Q, QuerySet

from app.graphql_api.admin.types import ProviderType
from app.graphql_api.classes.types import (
    BookingFilter,
    ClassElement,
    ClassStatus,
)
from app.graphql_api.pagination import Connection, paginate, Params
from app.graphql_api.classes.helpers import (
    get_class_status,
    check_join_availability,
)


async def get_requested_page(
    base_query: QuerySet[Booking],
    page: int = 1,
    per_page: int = 10,
) -> Connection[Booking]:
    """
    Method to get the requested upcoming classes page
    :return: upcoming classes page
    """
    return await paginate(
        base_query,
        params=Params(page=page, per_page=per_page),
        prefetch_related=[
            "live_session",
            "live_session__bookings",
            "live_session__bookings__participant",
            "live_session__webinar",
            "live_session__webinar__host",
        ],
    )


def map_response(
    upcoming_class: LiveSession,
    participant_id: UUID,
    status: BookingStatusEnum,
) -> ClassElement:
    """
    Method to map a live session db entity into a class element response.
    :return: Class element object
    """
    available_to_join = check_join_availability(
        upcoming_class.meeting_start_time
    )
    is_in_the_past = (
        pendulum.instance(upcoming_class.meeting_start_time).add(
            minutes=upcoming_class.webinar.duration
        )
        <= pendulum.now()
    )
    host = None

    if available_to_join is True and status in [
        BookingStatusEnum.BOOKED,
        BookingStatusEnum.ATTENDED,
    ]:
        booking_status = ClassStatus.AVAILABLE_TO_JOIN
    elif is_in_the_past and status in [
        BookingStatusEnum.ATTENDED,
        BookingStatusEnum.WATCHED_RECORDING,
    ]:
        booking_status = ClassStatus.PAST
    else:
        booking_status = get_class_status(upcoming_class, participant_id)

    if upcoming_class.webinar.host:
        host = ProviderType(
            id=upcoming_class.webinar.host.id,
            email=upcoming_class.webinar.host.email,
            first_name=upcoming_class.webinar.host.first_name,
            last_name=upcoming_class.webinar.host.last_name,
            full_name=upcoming_class.webinar.host.full_name(),
            chat_identity=upcoming_class.webinar.host.chat_identity,
        )

    return ClassElement(
        id=upcoming_class.id,
        topic=upcoming_class.webinar.topic,
        title=upcoming_class.title,
        description=upcoming_class.description,
        started_at=upcoming_class.meeting_start_time,
        status=booking_status,
        presenter=host,
        join_link=upcoming_class.zoom_link,
        recording_link=upcoming_class.recording_url,
    )


# Utility function that can be patched (for compatibility with unit tests)
def get_one_hour_ago():
    return pendulum.now().subtract(hours=1)


def handle_booking_status(
    base_query: QuerySet, booking_status: list[BookingStatusEnum]
) -> QuerySet:
    one_hour_ago = get_one_hour_ago()

    status_query = Q()
    for status in booking_status:
        if status == BookingStatusEnum.ATTENDED:
            status_query = status_query | (
                Q(status=BookingStatusEnum.WATCHED_RECORDING)
                | Q(
                    status=BookingStatusEnum.ATTENDED,
                    live_session__meeting_start_time__lte=one_hour_ago,
                )
                | Q(
                    status=BookingStatusEnum.BOOKED,
                    live_session__meeting_start_time__lte=one_hour_ago,
                )
            )

        elif status == BookingStatusEnum.BOOKED:
            status_query = status_query | (
                Q(
                    status__in=[
                        BookingStatusEnum.ATTENDED,
                        BookingStatusEnum.BOOKED,
                    ],
                )
                & Q(
                    live_session__meeting_start_time__gte=one_hour_ago,
                )
            )

        else:
            status_query = status_query | Q(status=status)

    return base_query.filter(status_query)


async def build_base_query(
    participant_id: UUID,
    timezone_str: str,
    filters: Optional[BookingFilter] = None,
) -> QuerySet:
    """
    Method to create the query from provided filters.
    :return: query
    """
    ascending = True
    base_query = Booking.filter(
        participant_id=participant_id
    ).prefetch_related("live_session__webinar")

    if not filters:
        return base_query

    if filters.search:
        base_query = base_query.filter(
            Q(live_session__title__icontains=filters.search)
        )

    if filters.topic:
        base_query = base_query.filter(
            live_session__webinar__topic=filters.topic
        )

    if filters.health_coach:
        base_query = base_query.filter(
            live_session__webinar__host_id=filters.health_coach
        )

    if filters.with_recording:
        base_query = base_query.filter(
            live_session__recording_url__isnull=False
        )

    if filters.booking_status:
        if isinstance(filters.booking_status, list):
            if BookingStatusEnum.ATTENDED in filters.booking_status:
                ascending = False

            base_query = handle_booking_status(
                base_query, filters.booking_status
            )
        else:
            raise Exception(
                "Invalid booking status. Expected type: list[BookingStatusEnum]"
            )

    if filters.start_date:
        base_query = base_query.filter(
            live_session__meeting_start_time__gte=filters.start_date,
        )
    elif (
        filters.start_date is None
        and filters.booking_status == BookingStatusEnum.BOOKED
    ):
        base_query = base_query.filter(
            live_session__meeting_start_time__gte=pendulum.now().subtract(
                hours=1
            ),
        )

    if filters.end_date:
        base_query = base_query.filter(
            live_session__meeting_start_time__lte=filters.end_date,
        )

    if filters.time_of_day:
        sessions = await get_sessions_by_time_of_day(
            timezone_str=timezone_str, time_of_day=filters.time_of_day
        )
        session_id_list = [session.id for session in sessions]
        base_query = base_query.filter(live_session__id__in=session_id_list)

    return base_query.order_by(
        "live_session__meeting_start_time"
        if ascending
        else "-live_session__meeting_start_time"
    )


async def get_bookings(
    info: Info,
    participant_id: UUID,
    page: int = 1,
    per_page: int = 10,
    filters: Optional[BookingFilter] = None,
) -> Connection[ClassElement]:
    timezone = get_timezone_name(info)

    base_query = await build_base_query(
        participant_id=participant_id, timezone_str=timezone, filters=filters
    )
    bookings_data = await get_requested_page(
        base_query,
        page,
        per_page,
    )

    bookings = [
        map_response(b.live_session, participant_id, b.status)
        for b in bookings_data.items
    ]

    return Connection(page_info=bookings_data.page_info, items=bookings)
