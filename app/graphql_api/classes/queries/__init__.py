from typing import Optional
from uuid import UUID

import strawberry
from strawberry import Info

from app.auth.permissions import IsAuthenticated
from app.graphql_api.classes.queries.upcoming_classes import get_classes
from app.graphql_api.classes.queries.bookings_by_participant import (
    get_bookings,
)
from app.graphql_api.classes.queries.intro_sessions import get_intro_sessions
from app.graphql_api.classes.types import (
    ClassElement,
    ClassFilter,
    BookingFilter,
)
from app.graphql_api.pagination import Connection
from app.log.logging import logger


async def get_upcoming_classes(
    info: Info,
    participant_id: UUID,
    page: int = 1,
    per_page: int = 10,
    filters: Optional[ClassFilter] = None,
) -> Connection[ClassElement]:
    """
    resolver to get upcoming classes for a specific participant.
    :return: list of upcoming classes.
    """
    logger.info(
        "getting page %d with # %d upcoming classes for participant %s with filters %s",
        page,
        per_page,
        participant_id,
        filters,
    )
    classes = await get_classes(info, participant_id, page, per_page, filters)

    return classes


async def get_bookings_by_participant(
    info: Info,
    participant_id: UUID,
    page: int = 1,
    per_page: int = 10,
    filters: Optional[BookingFilter] = None,
) -> Connection[ClassElement]:
    bookings = await get_bookings(
        info, participant_id, page, per_page, filters
    )
    return bookings


async def get_intro_sessions_by_cohort(cohort_id: UUID):
    return await get_intro_sessions(cohort_id)


@strawberry.type()
class ClassesQuery:
    """Classes graphql queries."""

    get_upcoming_classes: Connection[ClassElement] = strawberry.field(
        resolver=get_upcoming_classes, permission_classes=[IsAuthenticated]
    )

    get_bookings_by_participant: Connection[ClassElement] = strawberry.field(
        resolver=get_bookings_by_participant,
        permission_classes=[IsAuthenticated],
    )

    get_intro_sessions_by_cohort: list[ClassElement] = strawberry.field(
        resolver=get_intro_sessions_by_cohort,
    )
