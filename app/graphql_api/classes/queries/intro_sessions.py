from uuid import UUID
import pendulum

from app.graphql_api.admin.types import ProviderType
from app.graphql_api.classes.types import ClassElement
from ciba_participant.cohort.models import Cohort
from ciba_participant.classes.models import Webinar, TopicEnum
from app.graphql_api.classes.enums import (
    ClassTopic,
    ClassStatus,
)


async def get_intro_sessions(cohort_id: UUID) -> list[ClassElement]:
    cohort = await Cohort.get_or_none(id=cohort_id)

    if cohort is None:
        raise Exception("Cohort not found")

    cohort_start = pendulum.instance(cohort.started_at)
    a_week_after = cohort_start.add(weeks=1)

    webinars = await Webinar.filter(
        topic=TopicEnum.INTRO_SESSION
    ).prefetch_related("sessions", "host")

    intro_sessions: list[ClassElement] = []

    for webinar in webinars:
        for session in webinar.sessions:
            then = pendulum.instance(session.meeting_start_time)
            if cohort_start < then < a_week_after:
                host = webinar.host
                intro_session = ClassElement(
                    id=session.id,
                    topic=ClassTopic.INTRO_SESSION,
                    title=webinar.title,
                    description=webinar.description,
                    started_at=then,
                    status=ClassStatus.AVAILABLE_TO_BOOK,
                    presenter=ProviderType(
                        id=host.id,
                        full_name=host.full_name(),
                        first_name=host.first_name,
                        last_name=host.last_name,
                        email=host.email,
                        chat_identity=host.chat_identity,
                    ),
                )
                intro_sessions.append(intro_session)

    return intro_sessions
