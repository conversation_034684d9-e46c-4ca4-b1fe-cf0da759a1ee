from typing import Optional
from uuid import UUID

import pendulum
from strawberry import Info
from ciba_participant.cohort.models import (
    CohortMembers,
    CohortMembershipStatus,
)
from ciba_participant.classes.models import (
    LiveSession,
    Booking,
    TopicEnum,
    BookingStatusEnum,
)
from ciba_participant.classes.service import get_sessions_by_time_of_day
from ciba_participant.helpers.timezone_conversion import get_timezone_name
from ciba_participant.log.logging import logger
from tortoise.queryset import Q, QuerySet

from app.graphql_api.admin.types import ProviderType
from app.graphql_api.classes.helpers import get_class_status
from app.graphql_api.classes.types import (
    ClassElement,
    ClassFilter,
    ClassStatus,
)
from app.graphql_api.pagination import Connection, paginate, Params


# Utility function that can be patched (for compatibility with unit tests)
def get_dates_query_filter(start_date, end_date):
    return {
        "meeting_start_time__gte": start_date,
        "meeting_start_time__lte": end_date,
    }


def get_start_date(filters, cohort_start):
    if filters and filters.start_date:
        filter_start_date = filters.start_date
    else:
        filter_start_date = pendulum.now()

    return max(filter_start_date, cohort_start)


async def build_base_query(
    timezone_str: str,
    participant_id: UUID,
    filters: Optional[ClassFilter] = None,
    has_booked_intro: bool = False,
    ignore_time_restriction: bool = False,
) -> QuerySet:
    """
    Method to create the upcoming classes query from provided filters.
    :return: upcoming classes query
    """
    cohort_member = (
        await CohortMembers.filter(
            participant_id=participant_id,
            status=CohortMembershipStatus.ACTIVE,
        )
        .prefetch_related("cohort")
        .first()
    )

    if not cohort_member:
        logger.info(f"Participant: {participant_id}, is not in active cohort")
        return LiveSession.filter(id__isnull=True)

    cohort_start = cohort_member.cohort.started_at
    start_date = get_start_date(filters, cohort_start)

    if not has_booked_intro:
        if ignore_time_restriction:
            end_date = pendulum.now().add(weeks=1)
        else:
            end_date = pendulum.instance(cohort_start).add(weeks=1)

        topic_query = Q(webinar__topic=TopicEnum.INTRO_SESSION)
    else:
        date_limit = pendulum.now().add(days=30)
        end_date = (
            min(filters.end_date, date_limit)
            if filters and filters.end_date
            else date_limit
        )
        topic_query = ~Q(webinar__topic=TopicEnum.INTRO_SESSION)

    base_query = LiveSession.filter(topic_query).filter(
        **get_dates_query_filter(start_date, end_date)
    )

    if not filters:
        return base_query

    if filters.topic:
        base_query = base_query.filter(webinar__topic=filters.topic)
    if filters.time_of_day:
        sessions = await get_sessions_by_time_of_day(
            timezone_str=timezone_str, time_of_day=filters.time_of_day
        )
        sessions_ids = [session.id for session in sessions]

        base_query = base_query.filter(id__in=sessions_ids)
    if filters.health_coach:
        base_query = base_query.filter(webinar__host_id=filters.health_coach)
    if filters.search:
        base_query = base_query.filter(Q(title__icontains=filters.search))

    return base_query.order_by("meeting_start_time")


async def get_requested_page(
    base_query: QuerySet[LiveSession],
    page: int = 1,
    per_page: int = 10,
) -> Connection[LiveSession]:
    """
    Method to get the requested upcoming classes page
    :return: upcoming classes page
    """
    return await paginate(
        base_query,
        params=Params(page=page, per_page=per_page),
        prefetch_related=[
            "bookings",
            "webinar",
            "bookings__participant",
            "webinar__host",
        ],
    )


def map_response(
    upcoming_class: LiveSession, participant_id: UUID
) -> ClassElement:
    """
    Method to map a live session db entity into a class element response.
    :return: Class element object
    """
    return ClassElement(
        id=upcoming_class.id,
        topic=upcoming_class.webinar.topic,
        title=upcoming_class.title,
        description=upcoming_class.description,
        started_at=upcoming_class.meeting_start_time,
        status=get_class_status(upcoming_class, participant_id),
        presenter=ProviderType(
            id=upcoming_class.webinar.host.id,
            email=upcoming_class.webinar.host.email,
            first_name=upcoming_class.webinar.host.first_name,
            last_name=upcoming_class.webinar.host.last_name,
            full_name=upcoming_class.webinar.host.full_name(),
            chat_identity=upcoming_class.webinar.host.chat_identity,
        ),
        join_link=upcoming_class.zoom_link,
        recording_link=upcoming_class.recording_url,
    )


async def get_classes(
    info: Info,
    participant_id: UUID,
    page: int = 1,
    per_page: int = 10,
    filters: Optional[ClassFilter] = None,
) -> Connection[ClassElement]:
    """
    Method to get the upcoming classes with status based on participant_id
    """
    intro_booking: Booking = (
        await Booking.filter(participant_id=participant_id)
        .filter(
            Q(participant_id=participant_id)
            & Q(live_session__webinar__topic=TopicEnum.INTRO_SESSION)
            & (
                Q(status=BookingStatusEnum.ATTENDED)
                | Q(status=BookingStatusEnum.WATCHED_RECORDING)
            )
        )
        .first()
    )

    has_booked_intro = intro_booking is not None

    timezone = get_timezone_name(info)
    base_query = await build_base_query(
        timezone, participant_id, filters, has_booked_intro
    )

    async def get_data():
        upcoming_classes = await get_requested_page(base_query, page, per_page)
        items: list[ClassElement] = [
            map_response(upcoming_class, participant_id)
            for upcoming_class in upcoming_classes.items
        ]

        items = list(
            filter(
                lambda c: c.status
                in [ClassStatus.AVAILABLE_TO_BOOK, ClassStatus.FULLY_BOOKED],
                items,
            )
        )

        return (items, upcoming_classes.page_info)

    items, page_info = await get_data()

    if len(items) == 0 and has_booked_intro is False:
        base_query = await build_base_query(
            timezone_str=timezone,
            participant_id=participant_id,
            filters=filters,
            has_booked_intro=has_booked_intro,
            ignore_time_restriction=True,
        )

        items, page_info = await get_data()

    return Connection(
        page_info=page_info,
        items=items,
    )
