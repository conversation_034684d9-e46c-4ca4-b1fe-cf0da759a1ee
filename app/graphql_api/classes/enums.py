from enum import Enum

import strawberry
from ciba_participant.classes.models import (
    TopicEnum,
    TimeOfDayEnum,
    BookingStatusEnum,
)

ClassTopic = strawberry.enum(TopicEnum)
TimeOfDay = strawberry.enum(TimeOfDayEnum)
BookingStatus = strawberry.enum(BookingStatusEnum)


@strawberry.enum
class ClassStatus(str, Enum):
    AVAILABLE_TO_BOOK = "availableToBook"
    FULLY_BOOKED = "fullyBooked"
    BOOKED = "booked"
    AVAILABLE_TO_JOIN = "availableToJoin"
    PAST = "past"
