import strawberry

from app.auth.permissions import IsAuthenticated
from app.graphql_api.classes.mutations.update_booking_status import (
    update_booking_status,
)
from app.graphql_api.classes.types import UpdateBookingResponseType


@strawberry.type()
class ClassesMutation:
    """Classes graphql mutations."""

    update_booking_status: UpdateBookingResponseType = strawberry.field(
        resolver=update_booking_status, permission_classes=[IsAuthenticated]
    )
