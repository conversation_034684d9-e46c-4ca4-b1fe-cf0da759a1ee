from uuid import UUID

from strawberry.types import Info

from app import get_settings
from app.graphql_api.classes.types import UpdateBookingResponseType
from ciba_participant.classes.models import (
    Booking,
    BookingStatusEnum,
    LiveSession,
    TopicEnum,
)
from ciba_participant.common.aws_handler import (
    SQSNotification,
    NotificationType,
    EmailNotificationEvent,
    send_to_sqs,
)

settings = get_settings()


def _send_email_notification(
    participant_id: UUID,
    live_session_id: UUID,
    email_event: EmailNotificationEvent,
) -> None:
    """
    Helper function to send email notifications via SQS
    """
    sqs_notification = SQSNotification(
        type=NotificationType.SQS,
        email_event=email_event,
        data={
            "participant_id": participant_id,
            "live_session_id": live_session_id,
        },
    )
    send_to_sqs(
        queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
        message_body=sqs_notification.model_dump_json(),
    )


async def create_booking(
    live_session_id: UUID, participant_id: UUID, status: BookingStatusEnum
) -> UpdateBookingResponseType:
    """
    Helper function to create a new booking
    """
    booking = await Booking.create(
        live_session_id=live_session_id,
        participant_id=participant_id,
        status=status,
    )

    _send_email_notification(
        participant_id=participant_id,
        live_session_id=live_session_id,
        email_event=EmailNotificationEvent.CLASS_BOOKED,
    )

    return UpdateBookingResponseType(success=True, booking_id=booking.id)


async def handle_booked_status(
    booking: Booking, status: BookingStatusEnum
) -> UpdateBookingResponseType:
    """
    Helper function to handle the BOOKED status
    """
    if booking.status != BookingStatusEnum.CANCELED:
        return UpdateBookingResponseType(
            success=False, error="Booking was already made"
        )

    booking.status = status
    await booking.save()
    return UpdateBookingResponseType(success=True, booking_id=booking.id)


async def _handle_new_booking(
    session: LiveSession,
    participant_id: UUID,
    live_session_id: UUID,
    status: BookingStatusEnum,
) -> UpdateBookingResponseType:
    """
    Handle creating a new booking when participant doesn't have an existing one
    """
    if len(session.bookings) >= session.webinar.max_capacity:
        return UpdateBookingResponseType(
            success=False,
            error="Session is full",
        )

    if status == BookingStatusEnum.BOOKED:
        return await create_booking(live_session_id, participant_id, status)

    return UpdateBookingResponseType(success=False, error="Booking not found")


async def _handle_existing_booking(
    session: LiveSession,
    participant_booking: Booking,
    participant_id: UUID,
    live_session_id: UUID,
    status: BookingStatusEnum,
) -> UpdateBookingResponseType:
    """
    Handle updating an existing booking
    """
    # Handle BOOKED status specifically
    if status == BookingStatusEnum.BOOKED:
        response = await handle_booked_status(participant_booking, status)
        if response.success:
            _send_email_notification(
                participant_id=participant_id,
                live_session_id=live_session_id,
                email_event=EmailNotificationEvent.CLASS_BOOKED,
            )
        return response

    # Check if booking status cannot be changed
    if participant_booking.status in [BookingStatusEnum.ATTENDED]:
        return UpdateBookingResponseType(
            success=False,
            error=f"Booking was already {participant_booking.status.value.lower()}",
        )

    # Update status if it's different
    if participant_booking.status == status:
        return UpdateBookingResponseType(
            success=True, booking_id=participant_booking.id
        )

    # Send appropriate email notifications based on status
    if status == BookingStatusEnum.CANCELED:
        _send_email_notification(
            participant_id=participant_id,
            live_session_id=live_session_id,
            email_event=EmailNotificationEvent.CANCELLED_SESSION_BY_USER,
        )

    if session.webinar.topic == TopicEnum.INTRO_SESSION and status in [
        BookingStatusEnum.ATTENDED,
        BookingStatusEnum.WATCHED_RECORDING,
    ]:
        _send_email_notification(
            participant_id=participant_id,
            live_session_id=live_session_id,
            email_event=EmailNotificationEvent.CLASSES_UNLOCKED,
        )

    # Update and save the booking
    participant_booking.status = status
    await participant_booking.save()

    return UpdateBookingResponseType(
        success=True, booking_id=participant_booking.id
    )


async def update_status_by_participant_id(
    participant_id: UUID,
    live_session_id: UUID,
    status: BookingStatusEnum,
) -> UpdateBookingResponseType:
    """
    Update booking status for a participant in a live session
    """
    # Get session with related data
    session = await LiveSession.get_or_none(id=live_session_id)
    if session is None:
        return UpdateBookingResponseType(
            success=False,
            error="Session not found",
        )

    await session.fetch_related("bookings", "webinar")

    # Get existing booking for participant
    participant_booking = await Booking.filter(
        live_session_id=live_session_id,
        participant_id=participant_id,
    ).first()

    # Handle case where participant doesn't have a booking
    if not participant_booking:
        return await _handle_new_booking(
            session, participant_id, live_session_id, status
        )

    # Handle case where participant has an existing booking
    return await _handle_existing_booking(
        session, participant_booking, participant_id, live_session_id, status
    )


async def update_booking_status(
    info: Info,
    live_session_id: UUID,
    status: BookingStatusEnum,
) -> UpdateBookingResponseType:
    """
    Method to update the booking status
    """
    participant_id = info.context.user.sub
    return await update_status_by_participant_id(
        participant_id, live_session_id, status
    )
