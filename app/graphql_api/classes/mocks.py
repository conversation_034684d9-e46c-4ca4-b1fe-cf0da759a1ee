from datetime import timedelta

import pendulum

from app.graphql_api.classes.enums import ClassStatus, ClassTopic

PRESENTER = {
    "id": "06322539-2b85-4488-a719-2f41065d0143",
    "full_name": "<PERSON>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "chat_identity": "<PERSON><PERSON>",
    "description": "Health Coach",
    "avatar_url": "https://cdn4.iconfinder.com/data/icons/coronavirus/64/doctor-advise-warning-suggestion-avatar-512.png",
}

UPCOMING_CLASSES = [
    {
        "id": "1a5c49c3-84cf-4f16-82f4-b8809289a4d9",
        "topic": ClassTopic.EDUCATIONAL,
        "title": "Sleep mock",
        "description": "Drifting off to sleep should be a gentle release, not a nightly battle.  Creating a restful "
        "environment is key.  Think cool, dark, and quiet – a sanctuary free from the distractions of "
        "glowing screens and buzzing notifications.  A consistent sleep schedule, even on weekends, "
        "helps regulate your body's natural clock.",
        "date": pendulum.now(),
        "status": ClassStatus.FULLY_BOOKED,
        "presenter": PRESENTER,
    },
    {
        "id": "ca706f60-072b-404b-8268-7e2786c01715",
        "topic": ClassTopic.HEALTH_AND_WELLNESS,
        "title": "Activity mock",
        "description": "Finding joy in movement is the secret to a sustainable and healthy lifestyle.  It's not about "
        "punishing workouts or grueling gym sessions, but rather discovering activities you genuinely "
        "enjoy.",
        "date": pendulum.now() + timedelta(minutes=30),
        "status": ClassStatus.AVAILABLE_TO_JOIN,
        "presenter": PRESENTER,
        "join_link": "https://fakeimg.pl/600x400",
    },
    {
        "id": "456a6ff7-f7a2-4796-8078-920369c893f3",
        "topic": ClassTopic.MENTAL_HEALTH,
        "title": "Stress mock",
        "description": "Stress is an inevitable part of life, but learning to manage it effectively is crucial for"
        "well-being. It's not about eliminating stress entirely, but rather developing healthy coping "
        "mechanisms.",
        "date": pendulum.now() + timedelta(hours=2),
        "status": ClassStatus.BOOKED,
        "presenter": PRESENTER,
        "join_link": "https://fakeimg.pl/600x400",
    },
    {
        "id": "0353d866-5ac5-457e-b280-f8195b67b1c0",
        "topic": ClassTopic.FOOD,
        "title": "Recipes mock",
        "description": "Eating healthy doesn't have to be bland or boring.  It's about embracing the vibrant world of "
        "flavors and nourishing your body with wholesome ingredients.",
        "date": pendulum.now() + timedelta(hours=12),
        "status": ClassStatus.AVAILABLE_TO_BOOK,
        "presenter": PRESENTER,
    },
    {
        "id": "ca798041-ef25-4479-b8e3-de1f17f2000c",
        "topic": ClassTopic.INTRO_SESSION,
        "title": "Motivation mock",
        "description": "Motivation isn't a magical switch that flips on and stays on; it's a fire you need to tend. "
        "It flickers and fades, requiring consistent fuel and a steady hand. Start by identifying your "
        "'why' – the deep-seated reason behind your goals.",
        "date": pendulum.now() + timedelta(days=1),
        "status": ClassStatus.AVAILABLE_TO_BOOK,
        "presenter": PRESENTER,
    },
]
