from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry

from app.graphql_api.content_library.enums import (
    MaterialTagEnum,
    MaterialStatusEnum,
    MaterialActivityEnum,
)


@strawberry.type
class BaseContentMaterialElement:
    id: UUID
    added_at: datetime
    mime_type: str
    title: str
    description: str
    tags: list[MaterialTagEnum]
    link: Optional[str]
    link_expiration: Optional[datetime] = strawberry.field(default=None)


@strawberry.type
class ContentMaterialElement(BaseContentMaterialElement):
    status: MaterialStatusEnum
    activity_types: list[MaterialActivityEnum]
    is_favorite: Optional[bool] = strawberry.field(default=False)


@strawberry.type
class ContentMaterialList:
    success: bool
    error: Optional[str] = strawberry.field(default=None)
    items: list[ContentMaterialElement]
    total: int
    total_pages: int
