from uuid import UUID

import strawberry
from strawberry.types import Info
from ciba_participant.content_library.service import (
    record_content_progress,
    handle_favorite_status,
)
from ciba_participant.log.logging import logger
from ciba_participant.participant.exceptions import (
    ParticipantNotInCohortException,
)

from app.graphql_api.types import SimpleResponseType
from app.auth.permissions import IsAuthenticated


async def record_content_interaction(
    info: Info,
    content_material_id: UUID,
    download: bool = False,
) -> SimpleResponseType:
    """
    Resolver to handle content interactions
    """
    participant_id = info.context.user.sub

    try:
        await record_content_progress(
            participant_id=participant_id,
            content_material_id=content_material_id,
            download=download,
        )
    except ParticipantNotInCohortException:
        return SimpleResponseType(
            success=False, error="Participant is not yet in cohort"
        )
    except ValueError:
        return SimpleResponseType(
            success=False, error="Content material not found"
        )
    except Exception as e:
        logger.error(e)
        return SimpleResponseType(success=False)

    return SimpleResponseType(success=True)


async def toggle_favorite_status(
    info: Info,
    content_material_id: UUID,
) -> SimpleResponseType:
    """
    Resolver to handle favorite status of content material
    """
    participant_id = info.context.user.sub

    try:
        await handle_favorite_status(
            participant_id=participant_id,
            content_material_id=content_material_id,
        )
    except ValueError:
        return SimpleResponseType(
            success=False, error="Content material not found"
        )
    except Exception as e:
        logger.error(e)
        return SimpleResponseType(success=False)

    return SimpleResponseType(success=True)


@strawberry.type()
class ContentLibraryMutation:
    """Content library graphql mutations."""

    create_content_interaction: SimpleResponseType = strawberry.field(
        resolver=record_content_interaction,
        permission_classes=[IsAuthenticated],
    )

    toggle_favorite_status: SimpleResponseType = strawberry.field(
        resolver=toggle_favorite_status,
        permission_classes=[IsAuthenticated],
    )
