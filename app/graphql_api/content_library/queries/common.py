from typing import Optional
from uuid import UUID

from ciba_participant.content_library.crud import (
    ContentMaterialRepository,
    MaterialInteractionRepository,
)
from ciba_participant.content_library.crud.get_content_material import (
    MaterialFilters,
)
from ciba_participant.content_library.models import ContentMaterial
from ciba_participant.participant.crud import ParticipantRepository

from app.graphql_api.content_library.types import (
    ContentMaterialElement,
    BaseContentMaterialElement,
)
from app.graphql_api.pagination import Connection, PageInfo
from app.models.program import Program
from app.log.logging import logger


async def get_participant_program(participant_id: UUID) -> Optional[Program]:
    """
    Helper to retrieve a participant program.
    :param participant_id: ID of the participant.
    :return: The participant program or None if not found.
    """
    logger.info("Retrieving program for participant %s", participant_id)

    return await ParticipantRepository.get_participant_program(participant_id)


async def get_content_material_data(
    participant_id: UUID,
    filters: MaterialFilters,
    page: int = 1,
    per_page: int = 10,
) -> Connection[ContentMaterialElement]:
    """
    Method to query the content material data.
    :param participant_id: ID of the participant
    :param filters: Filters to be applied on the query
    :param page: Page number
    :param per_page: amount of elements per page
    :return: List of retrieved content materials
    """
    favorite_materials = set(
        await MaterialInteractionRepository.get_favorite_content(
            participant_id=participant_id
        )
    )

    result = await ContentMaterialRepository.get_material(
        page=page,
        per_page=per_page,
        filters=filters,
    )

    return Connection(
        page_info=PageInfo(
            has_next_page=page < result.total_pages,
            has_previous_page=page > 1,
            current_page=page,
            per_page=per_page,
            last_page=result.total_pages,
            total=result.total,
        ),
        items=[
            map_to_response(entity, favorite_materials)
            for entity in result.items
        ],
    )


async def get_all_content_material_data(
    page: int = 1,
    per_page: int = 10,
    filters: MaterialFilters = MaterialFilters(),
) -> Connection[BaseContentMaterialElement]:
    """Method to get all the available content material

    Take the provided filters and pagination parameters to retrieve all
    the available content material on participant database.

    Args:
        page (int): The page number for the request
        per_page (int): The amount of items per page
        filters (MaterialFilters): The filters to be applied on the query
    Returns:
        Connection[BaseContentMaterialElement]: List of the content material that matches the filtering criteria
    """
    result = await ContentMaterialRepository.get_material(
        page=page,
        per_page=per_page,
        filters=filters,
    )

    return Connection(
        page_info=PageInfo(
            has_next_page=page < result.total_pages,
            has_previous_page=page > 1,
            current_page=page,
            per_page=per_page,
            last_page=result.total_pages,
            total=result.total,
        ),
        items=[map_to_base_material(entity) for entity in result.items],
    )


def map_to_base_material(
    entity: ContentMaterial,
) -> BaseContentMaterialElement:
    """
    Method to map a content material db entity to a base content material element.

    Args:
        entity (ContentMaterial): Content material to map
    Returns:
        BaseContentMaterialElement: Mapped content material
    """
    material_tags = [material_tag.tag for material_tag in entity.tags]

    return BaseContentMaterialElement(
        id=entity.id,
        added_at=entity.created_at,
        mime_type=entity.mime_type,
        tags=material_tags,
        link=entity.link,
        link_expiration=entity.file_url_expiration,
        title=entity.title,
        description=entity.description,
    )


def map_to_response(
    entity: ContentMaterial, favorite_materials: set[UUID]
) -> ContentMaterialElement:
    """
    Method to map a content material db entity to a content material element.
    :param entity: Content material model entity
    :param favorite_materials: Set of participant favorite materials
    :return: Content material serialized element
    """
    tags = [content_tag.tag for content_tag in entity.tags]
    activity_types = [
        activity.activity_type for activity in entity.activity_types
    ]

    return ContentMaterialElement(
        id=entity.id,
        added_at=entity.created_at,
        mime_type=entity.mime_type,
        title=entity.title,
        description=entity.description,
        status=entity.status,
        link=entity.link,
        link_expiration=entity.file_url_expiration,
        tags=tags,
        activity_types=activity_types,
        is_favorite=entity.id in favorite_materials,
    )


def build_error_response(
    error: str, page: int = 1, per_page: int = 10
) -> Connection:
    """
    Method to build an unsuccessful response for queries
    :param error: error message
    :param page: page number
    :param per_page: amount of elements per page
    :return: A response with success equal to False, an error message and empty items
    """
    return Connection(
        page_info=PageInfo(
            has_next_page=False,
            has_previous_page=False,
            current_page=page,
            per_page=per_page,
            last_page=page,
            total=0,
        ),
        error=str(error),
        success=False,
        items=[],
    )
