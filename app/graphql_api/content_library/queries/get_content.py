from typing import Optional
from uuid import UUID

from ciba_participant.content_library.crud.get_content_material import (
    MaterialFilters as MaterialQueryFilters,
)
from ciba_participant.program.models import Program
from tortoise.exceptions import BaseORMException

from app.graphql_api.content_library.constants import NO_PROGRAM
from app.graphql_api.content_library.enums import MaterialStatusEnum
from app.graphql_api.content_library.exceptions import MaterialQueryException
from app.graphql_api.content_library.inputs import MaterialFilters
from app.graphql_api.content_library.queries.common import (
    get_content_material_data,
)
from app.graphql_api.content_library.types import ContentMaterialElement
from app.graphql_api.pagination import Connection
from app.helper.validations.pagination import validate_pagination_params
from app.log.logging import logger


async def get_content(
    participant_id: UUID,
    program: Program,
    page: int = 1,
    per_page: int = 10,
    filters: Optional[MaterialFilters] = None,
) -> Connection[ContentMaterialElement]:
    """
    Method to query the content material with the provided filters.
    """
    validate_pagination_params(page, per_page)

    if not program:
        raise ValueError(NO_PROGRAM)

    query_filters = MaterialQueryFilters(
        program_id=program.id,
        status=MaterialStatusEnum.ACTIVE,
        **(vars(filters) if filters else {}),
    )

    try:
        return await get_content_material_data(
            participant_id, query_filters, page, per_page
        )
    except BaseORMException as error:
        logger.exception(error)
        raise MaterialQueryException(
            "An error occurred while reading the requested content material."
        )
