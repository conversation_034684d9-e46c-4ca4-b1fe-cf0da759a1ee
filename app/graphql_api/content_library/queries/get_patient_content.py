from typing import Optional

from tortoise.exceptions import BaseORMException
from ciba_participant.content_library.crud.get_content_material import (
    MaterialFilters,
)

from app.graphql_api.content_library.inputs import FavoriteFilters
from app.graphql_api.content_library.queries import build_error_response
from app.graphql_api.content_library.queries.common import (
    get_all_content_material_data,
)
from app.graphql_api.content_library.types import BaseContentMaterialElement
from app.graphql_api.pagination import Connection
from app.helper.validations.pagination import validate_pagination_params
from app.log.logging import logger


async def resolve_patient_content(
    page: int = 1,
    per_page: int = 10,
    filters: Optional[FavoriteFilters] = None,
) -> Connection[BaseContentMaterialElement]:
    """
    Resolver to handle the content material retrieval for patients
    :return:
    """
    try:
        validate_pagination_params(page, per_page)
    except ValueError as validation_error:
        logger.error(
            f"Invalid pagination parameters. Page={page} & PerPage={per_page}"
        )
        return build_error_response(str(validation_error), page, per_page)

    logger.info(
        "Retrieving patient content materials with filters: %s",
        filters,
    )

    try:
        return await get_all_content_material_data(
            page,
            per_page,
            MaterialFilters(**(vars(filters))) if filters else None,
        )
    except BaseORMException as error:
        logger.exception(
            "An error occurred reading materials for patient: %s", error
        )

        # Error provided in this format to support localization on patient API
        return build_error_response("content_read_error", page, per_page)
