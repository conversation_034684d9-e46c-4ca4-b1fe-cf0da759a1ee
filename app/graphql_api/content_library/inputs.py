from datetime import datetime
from typing import List, Optional

import strawberry

from app.graphql_api.content_library.enums import MaterialTagEnum


@strawberry.input
class FavoriteFilters:
    tags: Optional[List[MaterialTagEnum]] = strawberry.field(default=None)
    search: Optional[str] = strawberry.field(default=None)


@strawberry.input
class MaterialFilters(FavoriteFilters):
    start_date: Optional[datetime] = strawberry.field(default=None)
    end_date: Optional[datetime] = strawberry.field(default=None)
