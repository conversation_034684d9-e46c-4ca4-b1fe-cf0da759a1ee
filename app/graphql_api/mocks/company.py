from uuid import UUID
from enum import Enum

import strawberry


@strawberry.enum
class CompanyOrderByEnum(Enum):
    """Enum for GraphQL order by field."""

    NAME = "name"


@strawberry.enum
class OrderDirection(Enum):
    """Enum for GraphQL order by direction."""

    DESC = "-"
    ASC = ""


@strawberry.input
class CompaniesSortType:
    """GraphQl companies sort input.

    Direction is DESC by default.
    """

    order_by: CompanyOrderByEnum
    direction: OrderDirection = OrderDirection.DESC


@strawberry.type
class CompanyType:
    """Company model based graphql type."""

    id: UUID
    name: str


@strawberry.type
class NoCompanyData:
    """No company data type."""

    message: str = "No company data found."


async def get_companies(
    # sort: CompaniesSortType | None = None, search: str | None = None
) -> list[CompanyType]:
    """Return list of companies.

    Can be searched by name.
    """
    return [
        CompanyType(
            id=UUID("1374ac1f-aeb5-4863-b38e-6612e542d811"),
            name="Solera",
        )
    ]


@strawberry.type
class CompanyQuery:
    """Company graphql queries."""

    get_companies = strawberry.field(resolver=get_companies)
