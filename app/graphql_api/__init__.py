import logging
from typing import Any, List, Optional

import strawberry
from strawberry.extensions import Mask<PERSON>rro<PERSON>
from strawberry.types import Execution<PERSON>ontext
from graphql import GraphQLError

from ciba_participant.participant.models import Participant

from app.graphql_api.content_library.mutations import ContentLibraryMutation
from app.graphql_api.content_library.queries import ContentLibraryQuery
from app.auth.exceptions import InvalidToken
from app.graphql_api.admin.queries import AdminQuery
from app.graphql_api.classes.queries import ClassesQuery
from app.graphql_api.classes.mutations import ClassesMutation
from app.graphql_api.participant.mutations import ParticipantMutation
from app.graphql_api.participant.queries import ParticipantQuery
from app.graphql_api.program.mutations import ProgramMutation
from app.graphql_api.program.queries import ProgramQuery
from app.settings import ENV, get_settings
from app.graphql_api.mocks.company import CompanyQuery

logger = logging.getLogger("main")
settings = get_settings()

IGNORE_ERRORS = [InvalidToken, PermissionError]


class QlSchema(strawberry.Schema):
    """Custom graphql schema.

    We can extend original graphql schema here.
    """

    def process_errors(
        self,
        errors: List[GraphQLError],
        execution_context: Optional[ExecutionContext] = None,
    ) -> None:
        """Extend error processing."""
        for error in errors:
            extra = None
            if execution_context and execution_context.context.user:
                if isinstance(execution_context.context.user, Participant):
                    extra = {"user_id": str(execution_context.context.user.id)}
                else:
                    extra = {
                        "user_id": str(execution_context.context.user.sub)
                    }
            if (
                error.original_error
                and type(error.original_error) not in IGNORE_ERRORS
            ):
                # this one occurs when unexpected error has happened
                logger.error(error, exc_info=error.original_error, extra=extra)
            else:
                # this one occurs when validation error has happened
                # we need it to avoid errors to appear in sentry
                logger.warning(error, extra=extra)


@strawberry.type
class Query(
    AdminQuery,
    ParticipantQuery,
    ProgramQuery,
    CompanyQuery,
    ClassesQuery,
    ContentLibraryQuery,
):
    """Main graphql query class."""


@strawberry.type
class Mutation(
    ParticipantMutation,
    ProgramMutation,
    ClassesMutation,
    ContentLibraryMutation,
):
    """Main graphql mutation class."""


def should_mask_error(error: Any) -> bool:
    """Mask unexpected errors."""
    if settings.ENV in [ENV.LOCAL, ENV.DEV, ENV.STG]:
        return False
    # if error is GraphQLError, and it was not caused by some other error,
    # it means it is GraphQL schema validation error.
    # It is safe to show it
    if isinstance(error, GraphQLError) and not error.original_error:
        return False
    # if error is in the list of known errors that should be ignored,
    # it is safe to show it
    if (
        type(error) in IGNORE_ERRORS
        or type(error.original_error) in IGNORE_ERRORS
    ):
        return False
    return True


schema = QlSchema(
    query=Query,
    mutation=Mutation,
    extensions=[
        MaskErrors(should_mask_error),
    ],
)
