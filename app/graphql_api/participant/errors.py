import strawberry


@strawberry.type
class ParticipantDuplicatedError:
    """Error for cases when a participant already signed up."""

    message: str = "Participant was already signed up."


@strawberry.type
class ParticipantDneError:
    """Error for cases when a participant does not exist."""

    message: str = "Participant does not exist."


@strawberry.type
class InvalidVerifyChangePasswordToken:
    """Error for cases when a change password verification token is invalid."""

    message: str = "Invalid verify change password token."


@strawberry.type
class VerifyChangePasswordTokenExpired:
    """Error for cases when a change password verification token expired."""

    message: str = "Verify change password token has expired."


@strawberry.type
class ParentError:
    """Error for cases when a parent is not found."""

    message: str = "Main error"


@strawberry.type
class InvalidTimezone(ParentError):
    """Invalid Time zone error."""

    message: str = "Timezone is invalid."


@strawberry.type
class InvalidDataSend(ParentError):
    """Invalid Data Send zone error."""

    message: str = "Invalid Data Send "


@strawberry.type
class RegistrationFailed:
    """Error at save solera user stage"""

    message: str = "User is not registered"


@strawberry.type
class AlreadyEnrolled(ParentError):
    """Error for cases when a participant is already enrolled in a program."""

    message: str = "Participant is already enrolled in a program."
