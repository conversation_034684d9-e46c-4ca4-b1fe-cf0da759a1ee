# pylint: disable=duplicate-code
from uuid import UUID
from typing import Optional

import strawberry
from strawberry.types import Info
from tortoise.transactions import in_transaction

from app.common.types import ShortResponse
from ciba_participant.activity.crud import ParticipantActivityRepository
from ciba_participant.cohort.crud import CohortMembersRepository
from ciba_participant.chat_api.chat_api import assign_participant_to_chat
from ciba_participant.participant.models import Participant, ParticipantMeta
from ciba_participant.participant.crud import (
    ParticipantRepository,
    ParticipantMetaRepository,
    ParticipantMetaCreate,
    ParticipantMetaUpdate,
)
from ciba_participant.cohort.models import Cohort
from ciba_participant.participant.service import ParticipantService
from ciba_participant.settings import ENV
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    ActivityUnit,
    ParticipantActivityCategory,
)
from ciba_participant.classes.models import BookingStatusEnum
from ciba_participant.rpm_api.api import (
    disconnect_device,
    pair_transtek_device,
    get_single_device_status,
)
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.models import (
    DeviceTypeEnum,
    DeviceStatusEnum,
    DeviceModelEnum,
)
from app.settings import get_settings
from app.log.logging import logger
from app.services.devices.handler import RPMRequestHandler
from app.routers.devices import set_user_weight_status
from app.auth.permissions import IsAdmin, IsAuthenticated, IsSoleraUser
from app.graphql_api.classes.mutations.update_booking_status import (
    update_status_by_participant_id,
)
from app.graphql_api.participant.enums import ParticipantStatusEnum
from app.graphql_api.participant.inputs import SignUpInput
from app.graphql_api.participant.errors import (
    InvalidVerifyChangePasswordToken,
    ParticipantDneError,
    ParticipantDuplicatedError,
    VerifyChangePasswordTokenExpired,
)
from app.graphql_api.participant.types import (
    ParticipantEmail,
    ParticipantType,
    VerifyChangePasswordTokenType,
    AddActivityResultType,
    DeviceDetailedResponse,
)
import pendulum

from ciba_participant.solera.mint_vault.api import delete_solera_activity

settings = get_settings()


async def sign_up_participant(
    info: Info,
    data: SignUpInput,
) -> ParticipantType | ParticipantDuplicatedError:
    """Signup participant mutation."""
    data = ParticipantService(
        participant_id=None,
        email=data.email.lower(),
        first_name=data.first_name,
        last_name=data.last_name,
        group_id=UUID(data.group_id),
        member_id=UUID(data.member_id),
        status=ParticipantStatusEnum.PENDING.value,
        is_test=False,
    )
    participant = await ParticipantRepository.sign_up(data, info)
    # pylint: disable=duplicate-code
    if participant:
        return ParticipantType(
            id=participant.id,
            email=participant.email,
            first_name=participant.first_name,
            last_name=participant.last_name,
            chat_identity=participant.chat_identity,
            group_id=participant.group_id,
            member_id=participant.member_id,
            status=participant.status,
            cognito_sub=None,
            medical_record=participant.medical_record,
            heads_up_token=None,
            solera_id=None,
            solera_program_id=None,
            is_weight=False,
        )
    return ParticipantDuplicatedError()


async def update_status(
    info: Info,
    participant_id: UUID,
    status: ParticipantStatusEnum,
) -> ParticipantType | ParticipantDneError:
    """Update participant status."""

    participant = await ParticipantRepository.update_status(
        participant_id=participant_id, status=status.value, info=info
    )
    if participant:
        return ParticipantType(
            id=participant.id,
            email=participant.email,
            first_name=participant.first_name,
            last_name=participant.last_name,
            chat_identity=participant.chat_identity,
            group_id=participant.group_id,
            member_id=participant.member_id,
            status=participant.status,
            cognito_sub=None,
            medical_record=participant.medical_record,
            heads_up_token=None,
            solera_id=None,
            solera_program_id=None,
            is_weight=False,
        )
    return ParticipantDneError()


async def forgot_password(
    info: Info,
    email: str,
) -> bool:
    """Forgot password flow."""
    email = email.lower()
    await ParticipantRepository.resend_confirmation_link(
        info=info, email=email
    )
    return await ParticipantRepository.forgot_password(email)


async def verify_change_password(
    info: Info,
    token: str,
) -> (
    VerifyChangePasswordTokenType
    | InvalidVerifyChangePasswordToken
    | VerifyChangePasswordTokenExpired
):
    """Verify change password."""
    resp = await ParticipantRepository.verify_change_password_token(
        token, info
    )
    if "InvalidVerifyChangePasswordToken" in resp.keys():
        return InvalidVerifyChangePasswordToken()
    if "VerifyChangePasswordTokenExpired" in resp.keys():
        return VerifyChangePasswordTokenExpired()
    return VerifyChangePasswordTokenType(**resp)


async def resend_confirmation_link_by_token(
    info: Info,
    token: str,
) -> bool:
    """Resend participant confirmation link by token."""
    return await ParticipantRepository.resend_confirmation_link(
        token=token, info=info
    )


async def resend_confirmation_link(
    info: Info,
    email: str,
) -> bool:
    """Resend participant confirmation link by email."""
    email = email.lower()
    return await ParticipantRepository.resend_confirmation_link(
        info=info, email=email
    )


async def set_new_password(
    info: Info,
    cohort_id: str,
    new_password: str,
    starting_weight: float,
    target_weight: float,
    session_id: UUID,
) -> ParticipantEmail:
    """Set new password for participant."""
    correlation_id = info.context.request.headers.get(
        "X-Request-ID", pendulum.now().int_timestamp
    )

    email = info.context.user.email.lower()
    participant = await ParticipantRepository.set_new_password(
        info=info,
        email=email,
        new_password=new_password,
        correlation_id=correlation_id,
    )

    metas: list[ParticipantMeta] = participant.participant_meta
    meta: ParticipantMeta = metas[0] if len(metas) >= 1 else None

    if meta is None:
        meta = await ParticipantMetaRepository.create_participant_meta(
            ParticipantMetaCreate(participant_id=participant.id, metadata={})
        )

    if meta.metadata is None:
        meta.metadata = {}

    meta.metadata["user_reported_weight"] = starting_weight
    meta.metadata["user_target_weight"] = target_weight

    cohort = await Cohort.filter(id=cohort_id).first()

    input_data = {
        "participant": participant,
        "value": starting_weight,
        "unit": ActivityUnit.LB.value,
        "activity_type": ParticipantActivityEnum.WEIGHT.value,
        "activity_device": ParticipantActivityDevice.MANUAL_INPUT.value,
        "activity_category": ParticipantActivityCategory.WEIGHT.value,
    }
    participant_activity = ParticipantActivity(**input_data)
    cohort_created_at = pendulum.instance(cohort.started_at).add(days=1)

    participant_activity.created_at = cohort_created_at
    participant_activity.updated_at = cohort_created_at
    await participant_activity.save()

    await ParticipantMetaRepository.update_participant_meta(
        ParticipantMetaUpdate(
            id=meta.id,
            participant_id=participant.id,
            metadata=meta.metadata,
        )
    )

    participant_type = ParticipantType(
        id=participant.id,
        email=participant.email,
        first_name=participant.first_name,
        last_name=participant.last_name,
        chat_identity=participant.chat_identity,
        group_id=participant.group_id,
        member_id=participant.member_id,
        status=participant.status,
        cognito_sub=participant.cognito_sub,
        medical_record=participant.medical_record,
        program_group_id=cohort_id,
        heads_up_token=None,
        solera_id=None,
        solera_program_id=None,
        is_weight=False,
    )

    participant_id = (
        info.context.user.id
        if settings.ENV not in [ENV.LOCAL, ENV.TEST]
        else info.context.user.sub
    )
    async with in_transaction():
        cohort = await Cohort.filter(id=cohort_id).get()
        await CohortMembersRepository.create_cohort_member(
            cohort_id=cohort.id,
            participant_id=participant_id,
        )

        await update_status_by_participant_id(
            participant_id, session_id, BookingStatusEnum.BOOKED
        )

        if settings.ENV not in [ENV.LOCAL, ENV.TEST]:
            if isinstance(participant_type, ParticipantType):
                await assign_participant_to_chat(
                    cohort.unique_name,
                    participant_id,
                    participant_type.chat_identity,
                )

    return ParticipantEmail(email=email)


async def add_weight_data(
    info: Info,
    value: float,
    activity_device: str,
) -> AddActivityResultType:
    """Add weight data to participant."""
    try:
        activity_device = ParticipantActivityDevice(activity_device).value
    except ValueError as e:
        logger.error("Invalid activity device: %s: %s", activity_device, e)
        return AddActivityResultType(success=False)

    participant = (
        await Participant().filter(id=info.context.user.sub).get_or_none()
    )
    if not participant:
        return AddActivityResultType(success=False)

    input_data = {
        "participant": participant,
        "value": value,
        "unit": ActivityUnit.LB.value,
        "activity_type": ParticipantActivityEnum.WEIGHT.value,
        "activity_device": activity_device,
        "activity_category": ParticipantActivityCategory.WEIGHT.value,
    }
    participant_activity = ParticipantActivity(**input_data)
    await participant_activity.save()
    return AddActivityResultType(success=True)


async def unpair_device(
    participant_id: UUID,
    device_id: Optional[UUID] = None,
    device_model: Optional[DeviceModelEnum] = None,
    device_type: Optional[DeviceTypeEnum] = DeviceTypeEnum.TRANSTEK,
) -> DeviceDetailedResponse:
    """Unpair device from participant."""
    correlation_id = str(pendulum.now().int_timestamp)

    # Support both TRANSTEK and WITHINGS device types
    if device_type not in [DeviceTypeEnum.TRANSTEK, DeviceTypeEnum.WITHINGS]:
        return DeviceDetailedResponse(
            success=False, message="Device type not supported"
        )

    try:
        participant = await Participant.filter(id=participant_id).get_or_none()

        if participant is None:
            raise ValueError("Participant not found")

        logger.info(
            f"[Correlation ID: {correlation_id}] Disconnecting participant {participant_id} from {device_type} device"
        )

        # Handle Transtek and Withings devices differently
        if device_type == DeviceTypeEnum.TRANSTEK:
            return await _unpair_transtek_device(
                participant_id=participant_id,
                device_model=device_model,
                correlation_id=correlation_id,
                device_id=device_id,
            )
        else:  # WITHINGS
            return await _unpair_withings_device(
                participant=participant,
                correlation_id=correlation_id,
            )

    except RPMCallError as e:
        logger.exception(
            f"[Correlation ID: {correlation_id}] Error unpairing device: {e}"
        )
        return DeviceDetailedResponse(success=False, message=str(e))
    except Exception as e:
        logger.exception(
            f"[Correlation ID: {correlation_id}] Unexpected error unpairing device: {e}"
        )
        return DeviceDetailedResponse(
            success=False, message="Unexpected error"
        )


async def _unpair_transtek_device(
    participant_id: UUID,
    correlation_id: str,
    device_id: UUID,
    device_model: Optional[DeviceModelEnum] = None,
) -> DeviceDetailedResponse:
    """Handle Transtek device unpairing - direct disconnect."""
    logger.debug(
        f"[Correlation ID: {correlation_id}] Processing Transtek device unpairing"
    )

    # Get participant for weight status update
    participant = await Participant.filter(id=participant_id).get_or_none()
    if not participant:
        return DeviceDetailedResponse(
            success=False,
            message="Participant not found",
            device_status="unknown",
            requires_authentication=False,
        )

    # For Transtek devices, use the direct disconnect_device API
    await disconnect_device(
        participant_id=participant_id,
        device_model=device_model,
        device_id=device_id,
        device_type=DeviceTypeEnum.TRANSTEK,
    )

    # Set weight status to False for successful Transtek device unpairing
    logger.info(
        f"[Correlation ID: {correlation_id}] Setting user weight status to False for Transtek device"
    )

    weight_status_updated = await set_user_weight_status(
        participant, False, correlation_id
    )

    if weight_status_updated:
        logger.info(
            f"[Correlation ID: {correlation_id}] Successfully updated weight status to False"
        )
    else:
        logger.warning(
            f"[Correlation ID: {correlation_id}] Failed to update weight status for Transtek device"
        )

    return DeviceDetailedResponse(
        success=True,
        message="Transtek device unpaired successfully",
        device_status="disconnected",
        requires_authentication=False,
    )


async def _unpair_withings_device(
    participant: Participant,
    correlation_id: str,
) -> DeviceDetailedResponse:
    """Handle Withings device unpairing - includes weight status management."""
    logger.debug(
        f"[Correlation ID: {correlation_id}] Processing Withings device unpairing"
    )

    # Convert device type to ParticipantActivityDevice for RPM handler
    activity_device = ParticipantActivityDevice.WITHINGS

    # Call RPM disconnect service (similar to disconnect endpoint)
    logger.debug(
        f"[Correlation ID: {correlation_id}] Calling RPM disconnect service"
    )

    response = await RPMRequestHandler().disconnect(
        type_device=activity_device.value,
        member_type="participant",
        member_id=str(participant.id),
        correlation_id=correlation_id,
    )

    if response is None:
        logger.warning(
            f"[Correlation ID: {correlation_id}] No response from RPM disconnect service"
        )
        return DeviceDetailedResponse(
            success=False,
            message="Unable to disconnect from device service",
            device_status="unknown",
            requires_authentication=False,
        )

    logger.info(
        f"[Correlation ID: {correlation_id}] Response from RPM disconnect service: {response}"
    )

    disconnected: bool = response.get("disconnected", False)

    if disconnected:
        logger.info(
            f"[Correlation ID: {correlation_id}] Setting user weight status to False for Withings device"
        )

        # Update weight status to False when Withings device is disconnected
        weight_status_updated = await set_user_weight_status(
            participant, False, correlation_id
        )

        if weight_status_updated:
            logger.info(
                f"[Correlation ID: {correlation_id}] Successfully updated weight status to False"
            )
        else:
            logger.warning(
                f"[Correlation ID: {correlation_id}] Failed to update weight status"
            )

        return DeviceDetailedResponse(
            success=True,
            message="Withings device unpaired successfully",
            device_status="disconnected",
            requires_authentication=False,
        )
    else:
        logger.warning(
            f"[Correlation ID: {correlation_id}] Withings device was not disconnected successfully"
        )
        return DeviceDetailedResponse(
            success=False,
            message="Failed to disconnect Withings device",
            device_status="connected",
            requires_authentication=False,
        )


async def pair_device(
    participant_id: UUID,
    device_type: Optional[DeviceTypeEnum] = DeviceTypeEnum.TRANSTEK,
    imei: Optional[str] = None,
    serial_number: Optional[str] = None,
    mail: Optional[str] = None,
    site: Optional[str] = None,
) -> DeviceDetailedResponse:
    """Pair device with participant."""
    correlation_id = str(pendulum.now().int_timestamp)

    # Support both TRANSTEK and WITHINGS device types
    if device_type not in [DeviceTypeEnum.TRANSTEK, DeviceTypeEnum.WITHINGS]:
        return DeviceDetailedResponse(
            success=False, message="Device type not supported"
        )

    try:
        participant = await Participant.filter(id=participant_id).get_or_none()

        if participant is None:
            raise ValueError("Participant not found")

        # Use participant email if not provided
        participant_email = mail or participant.email

        logger.debug(
            f"[Correlation ID: {correlation_id}] Starting device pairing for participant {participant_id}, device type: {device_type}"
        )

        # Handle Transtek devices differently from Withings
        if device_type == DeviceTypeEnum.TRANSTEK:
            return await _pair_transtek_device(
                participant_id=participant_id,
                participant=participant,
                participant_email=participant_email,
                imei=imei,
                serial_number=serial_number,
                correlation_id=correlation_id,
            )
        else:  # WITHINGS
            return await _pair_withings_device(
                participant_id=participant_id,
                participant=participant,
                participant_email=participant_email,
                site=site,
                correlation_id=correlation_id,
            )

    except RPMCallError as e:
        logger.exception(
            f"[Correlation ID: {correlation_id}] Error pairing device: {e}"
        )
        return DeviceDetailedResponse(success=False, message=str(e))
    except Exception as e:
        logger.exception(
            f"[Correlation ID: {correlation_id}] Unexpected error pairing device: {e}"
        )
        return DeviceDetailedResponse(
            success=False, message="Unexpected error"
        )


async def _pair_transtek_device(
    participant_id: UUID,
    participant: Participant,
    participant_email: str,
    imei: Optional[str],
    serial_number: Optional[str],
    correlation_id: str,
) -> DeviceDetailedResponse:
    """Handle Transtek device pairing - direct pairing with IMEI/serial."""
    logger.debug(
        f"[Correlation ID: {correlation_id}] Processing Transtek device pairing"
    )

    # For Transtek devices, we directly pair without checking status first
    # This is because Transtek uses a different pairing model with physical device identifiers
    if not imei and not serial_number:
        return DeviceDetailedResponse(
            success=False,
            message="IMEI or serial number is required for Transtek device pairing",
            requires_authentication=False,
        )

    logger.debug(
        f"[Correlation ID: {correlation_id}] Pairing Transtek device with IMEI: {imei}, Serial: {serial_number}"
    )

    await pair_transtek_device(
        participant_id=participant_id,
        participant_email=participant_email,
        imei=imei,
        serial_number=serial_number,
    )

    # Set weight status to True for successful Transtek device pairing
    logger.info(
        f"[Correlation ID: {correlation_id}] Setting user weight status to True for Transtek device"
    )

    weight_status_updated = await set_user_weight_status(
        participant, True, correlation_id
    )

    if weight_status_updated:
        logger.info(
            f"[Correlation ID: {correlation_id}] Successfully updated weight status to True"
        )
    else:
        logger.warning(
            f"[Correlation ID: {correlation_id}] Failed to update weight status for Transtek device"
        )

    return DeviceDetailedResponse(
        success=True,
        message="Transtek device paired successfully",
        device_status="paired",
        requires_authentication=False,
    )


async def _pair_withings_device(
    participant_id: UUID,
    participant: Participant,
    participant_email: str,
    site: Optional[str],
    correlation_id: str,
) -> DeviceDetailedResponse:
    """Handle Withings device pairing - OAuth-style authentication flow."""
    logger.debug(
        f"[Correlation ID: {correlation_id}] Processing Withings device pairing"
    )

    # Step 1: Check current device status for Withings
    device_status = await get_single_device_status(
        participant_id=str(participant_id), device_type=DeviceTypeEnum.WITHINGS
    )

    logger.debug(
        f"[Correlation ID: {correlation_id}] Withings device status - healthy: {device_status.healthy}, "
        f"status: {device_status.status}, token: {'present' if device_status.token else 'none'}"
    )

    # Step 2: If device is healthy and connected, ensure weight status is set
    if (
        device_status.healthy
        and device_status.status == DeviceStatusEnum.CONNECTED
    ):
        # Set weight status to True for healthy connected Withings device
        logger.info(
            f"[Correlation ID: {correlation_id}] Setting user weight status to True for healthy Withings device"
        )

        weight_status_updated = await set_user_weight_status(
            participant, True, correlation_id
        )

        if weight_status_updated:
            logger.info(
                f"[Correlation ID: {correlation_id}] Successfully updated weight status to True"
            )
        else:
            logger.warning(
                f"[Correlation ID: {correlation_id}] Failed to update weight status for Withings device"
            )

        return DeviceDetailedResponse(
            success=True,
            message="Withings device is already connected and healthy",
            device_status=device_status.status.value,
            requires_authentication=False,
        )

    # Step 3: If device needs authentication, get auth URL
    if device_status.status == DeviceStatusEnum.NOT_CONNECTED or (
        not device_status.healthy and not device_status.token
    ):
        logger.debug(
            f"[Correlation ID: {correlation_id}] Withings device needs authentication, getting auth URL"
        )

        # Get cohort start date for sync_start_date
        cohort = await Cohort.filter(
            cohortmembers__participant_id=participant_id
        ).first()

        sync_start_date = (
            int(pendulum.instance(cohort.started_at).timestamp())
            if cohort and cohort.started_at
            else int(pendulum.now().subtract(days=30).timestamp())
        )

        # Convert device_type to ParticipantActivityDevice for get_device_auth
        activity_device = ParticipantActivityDevice.WITHINGS

        auth_response = await RPMRequestHandler().get_device_auth(
            participant=participant,
            type_device=activity_device,
            mail=participant_email,
            sync_start_date=sync_start_date,
            site=site,
            correlation_id=correlation_id,
        )

        auth_url = auth_response.get("auth_url")
        if auth_url:
            return DeviceDetailedResponse(
                success=True,
                message="Authentication required. Please use the provided auth URL to connect your Withings device.",
                auth_url=auth_url,
                device_status=device_status.status.value,
                requires_authentication=True,
            )
        else:
            return DeviceDetailedResponse(
                success=False,
                message="Failed to get authentication URL for Withings device",
                device_status=device_status.status.value,
                requires_authentication=True,
            )

    # Step 4: If device has token but is unhealthy, it's already authenticated
    # For Withings, this means the connection exists but may need refresh
    return DeviceDetailedResponse(
        success=True,
        message="Withings device is authenticated but may need reconnection. Please check your device settings.",
        device_status=device_status.status.value,
        requires_authentication=False,
    )


async def delete_participant_activity(
    participant_id: UUID, activity_id: UUID
) -> ShortResponse:
    """Delete participant activity."""
    try:
        async with in_transaction():
            activity = await ParticipantActivity.filter(
                id=activity_id,
            ).first()
            if not activity:
                return ShortResponse(success=False, error="Activity not found")

            await delete_solera_activity(
                participant_id=str(participant_id),
                activity_id=str(activity_id),
            )

            await ParticipantActivityRepository.delete_participant_activity(
                activity_id
            )
            return ShortResponse(success=True)

    except Exception as e:
        logger.exception(e)
        return ShortResponse(success=False, error="Unexpected error")


@strawberry.type()
class ParticipantMutation:
    """Participant mutations."""

    sign_up_participant = strawberry.field(resolver=sign_up_participant)
    update_status = strawberry.field(
        resolver=update_status, permission_classes=[IsAdmin]
    )
    resend_confirmation_link = strawberry.field(
        resolver=resend_confirmation_link
    )
    resend_confirmation_link_by_token = strawberry.field(
        resolver=resend_confirmation_link_by_token
    )
    verify_change_password = strawberry.field(resolver=verify_change_password)

    set_new_password = strawberry.field(
        resolver=set_new_password, permission_classes=[IsSoleraUser]
    )

    add_weight_data = strawberry.field(
        resolver=add_weight_data,
        permission_classes=[IsAuthenticated],
    )

    unpair_device = strawberry.field(
        resolver=unpair_device,
        permission_classes=[IsAuthenticated],
    )

    pair_device = strawberry.field(
        resolver=pair_device,
        permission_classes=[IsAuthenticated],
    )

    delete_participant_activity = strawberry.field(
        resolver=delete_participant_activity,
        permission_classes=[IsAuthenticated],
    )
