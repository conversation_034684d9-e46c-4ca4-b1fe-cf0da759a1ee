```mermaid
flowchart TD
    PPending[Participant created - status:Pending]
    PPending -->|active| PActive{_on_active}
    PPending -->|rejected| PRejected{_on_reject}
    %% PPending -->|pending| CognitoDisableUser{_on_pending}
    PActive --> |not_cognito| CreateCognito
    PActive --> |is_cognito| CognitoEnableUser
    CognitoEnableUser --> SendActivateParticipantEmail
    CreateCognito --> GenTempPass
    GenTempPass --> SendConfirmParticipantEmail
    PRejected --> CognitoDisableUser
    CognitoDisableUser --> SendRejectParticipantEmail
    PPending --> |pending| CognitoDisableUser
```
