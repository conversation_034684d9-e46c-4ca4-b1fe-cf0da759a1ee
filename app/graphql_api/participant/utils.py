from uuid import UUID

from ciba_participant.rpm_api.api import get_single_device_status
from ciba_participant.rpm_api.models import DeviceTypeEnum, DeviceStatusEnum


async def parse_withings_connection_status(participant_id: UUID) -> bool:
    connection_response = await get_single_device_status(
        str(participant_id), DeviceTypeEnum.WITHINGS
    )

    return (
        connection_response.status == DeviceStatusEnum.CONNECTED
        and connection_response.healthy is True
    )
