from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry

from app.graphql_api.participant.enums import ParticipantStatusEnum

from ciba_participant.rpm_api.models import TranstekDeviceInfo


@strawberry.type
class ParticipantType:
    """Participant model based graphql type."""

    id: UUID
    email: str
    first_name: str
    last_name: str
    chat_identity: str
    group_id: UUID
    member_id: UUID
    status: ParticipantStatusEnum
    cohort_end_date: Optional[datetime] = None
    cognito_sub: UUID | None
    medical_record: str
    heads_up_token: UUID | None
    solera_id: UUID | None
    solera_program_id: str | None
    is_weight: bool
    program_group_id: UUID | None
    disenrollment_reason: str | None = None
    disenrollment_date: datetime | None = None


@strawberry.type
class VerifyChangePasswordTokenType:
    email: str
    tmp_password: str


@strawberry.type
class TimeZonesType:
    """Time zone types."""

    id: str
    label: str


@strawberry.type
class HangoutUrlType:
    """Link to hangout url."""

    url: str


@strawberry.type
class HangoutUrl:
    """Link to hangout url."""

    url: str


@strawberry.type
class ParticipantEmail:
    """Email of the verified user"""

    email: str


@strawberry.type
class ParticipantActivityType:
    """Participant activity model based graphql type."""

    id: UUID
    participant_id: UUID
    value: str
    activity_type: str
    unit: str
    activity_device: str
    created_at: str


@strawberry.type
class WeightDataType:
    timestamp: str
    value: float


@strawberry.type
class ParticipantWeightsType:
    """Participant activity model based graphql type."""

    id: UUID
    participant_id: UUID
    value: float
    activity_type: str
    unit: str
    activity_device: str
    created_at: str


@strawberry.type
class AddActivityResultType:
    success: bool


@strawberry.type
class DeviceDetailedResponse:
    success: bool
    message: Optional[str] = None
    error_code: Optional[str] = None
    auth_url: Optional[str] = None
    device_status: Optional[str] = None
    requires_authentication: bool = False


@strawberry.type
class TranstekDeviceInfoType:
    """Strawberry type for Transtek device information."""

    id: str
    serial_number: str
    imei: str
    model: str
    device_type: str
    status: str

    # Optional fields
    tracking_number: Optional[str] = None
    carrier: Optional[str] = None
    tracking_url: Optional[str] = None
    timezone: Optional[str] = None
    last_status_report: Optional[str] = None
    member_id: Optional[str] = None

    @classmethod
    def from_model(cls, model: TranstekDeviceInfo) -> "TranstekDeviceInfoType":
        """Convert Pydantic model to Strawberry type."""
        return cls(
            id=model.id,
            serial_number=model.device_id,
            imei=model.imei,
            model=model.model,
            device_type=model.device_type,
            status=model.status,
            tracking_number=model.tracking_number,
            carrier=model.carrier,
            tracking_url=model.tracking_url,
            timezone=model.timezone,
            last_status_report=str(model.last_status_report)
            if model.last_status_report
            else None,
            member_id=model.member_id,
        )
