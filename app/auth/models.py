from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel


class AuthContext(BaseModel):
    """Context of authenticated user."""

    sub: Optional[UUID] = None
    email: Optional[str] = None
    email_verified: Optional[bool] = None
    auth_time: Optional[int] = None
    admin: bool = False
    issuer: Optional[str] = None

    def get_auth_time(self) -> Optional[datetime]:
        """Convert auth timestamp to datetime."""

        return (
            datetime.fromtimestamp(self.auth_time) if self.auth_time else None
        )
