from app.auth.base import BaseAuthenticator
from app.auth.cognito import (
    ParticipantAuthenticator,
    ProviderAuthenticator,
    SoleraAuthenticator,
)
from app.auth.constants import ADMIN, DEFAULT, PROVIDER, SOLERA
from app.auth.local import LocalAuthenticator
from app.log.logging import logger
from app.settings import ENV, get_settings

settings = get_settings()
authenticators = {
    DEFAULT: ParticipantAuthenticator,
    ADMIN: ProviderAuthenticator,
    PROVIDER: ProviderAuthenticator,
    SOLERA: SoleraAuthenticator,
}


def get_authenticator(
    authenticator: str = "default",
) -> BaseAuthenticator:
    """Define authenticator based on ENV."""
    logger.debug("get_authenticator")
    if settings.ENV == ENV.LOCAL:
        return LocalAuthenticator()
    return authenticators.get(authenticator, ParticipantAuthenticator)()  # type: ignore
