import uuid
from datetime import datetime

from app.auth.base import BaseAuthenticator
from app.auth.models import AuthContext


class LocalAuthenticator(BaseAuthenticator):
    """Local authenticator class."""

    # pylint: disable=unused-argument
    async def authenticate(
        self,
        *args: str,
    ) -> AuthContext:
        """Local user authenticator."""
        return AuthContext(
            sub=uuid.UUID(int=0),
            email="<EMAIL>",
            auth_time=int(datetime.timestamp(datetime.now())),
            email_verified=True,
            admin=True,
        )

    def get_issuer(self) -> str:
        """Issuer stub."""
        return "None"
