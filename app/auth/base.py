from abc import ABCMeta, abstractmethod

from app.auth.models import AuthContext


class BaseAuthenticator(metaclass=ABCMeta):
    """Base authenticator class."""

    BEARER_PREFIX = "Bearer "

    @abstractmethod
    async def authenticate(self, token: str) -> AuthContext:
        """Abstract authenticator method."""

    @abstractmethod
    def get_issuer(self) -> list[str] | str:
        """Abstract get issuer method."""
