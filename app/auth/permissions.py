import logging
import typing

from starlette.requests import Request
from starlette.websockets import WebSocket
from strawberry.permission import BasePermission
from strawberry.types import Info
from ciba_participant.participant.models import Participant

from app.auth import get_authenticator
from app.auth.constants import ADMIN, PROVIDER, SOLERA
from app.auth.exceptions import InvalidToken, PermissionDenied
from app.settings import get_settings
from app.auth.models import AuthContext

logger = logging.getLogger("main")
settings = get_settings()


class IsAuthenticated(BasePermission):
    """Base class that controls access to application."""

    message = "User is not authenticated"
    authenticator = "default"

    # pylint: disable=unused-argument
    async def has_permission(
        self, source: typing.Any, info: Info, **kwargs: str
    ) -> bool:
        """Check whether a request was made by logged-in user."""
        logger.info("IsAuthenticated")
        request: typing.Union[Request, WebSocket] = info.context.request
        x_auth_source = request.headers.get(
            "X-Auth-Source", self.authenticator
        )
        authenticator = get_authenticator(x_auth_source)

        # Check for API Key
        api_key = request.headers.get("x-api-key")
        user_sub = request.headers.get("api_key_user")
        if api_key and api_key in settings.PARTICIPANT_ALLOWED_API_KEY:
            # Simulate setting the user if API key is valid
            info.context.user = AuthContext(sub=user_sub)
            return True

        # Check for Authorization header
        if "Authorization" in request.headers:
            sub = None
            try:
                info.context.user = await authenticator.authenticate(
                    request.headers["Authorization"]
                )
                sub = info.context.user.sub
            except InvalidToken as ex:
                logger.warning(ex)
            except PermissionDenied:
                self.message = "Permission denied"
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logger.exception(ex)
            return sub is not None

        return False

    # pylint: disable=unused-argument
    def on_unauthorized(self, **kwargs: str) -> None:
        """Handle unauthorized access."""
        logger.info("Unauthorized access")


class CanViewParticipant(BasePermission):
    """Permission class that checks whether a user has access."""

    message = "Permission denied"

    async def has_permission(  # pylint: disable=invalid-overridden-method, unused-argument
        self, source: typing.Any, info: Info, **kwargs: str
    ) -> bool:
        """Check whether a participant can be seen by a requester."""
        logger.info("CanViewParticipant")
        participant_id = kwargs.get("participantId") or kwargs.get(
            "participant_id"
        )
        if info.context.user.admin is not True and str(
            info.context.user.sub
        ) != str(participant_id):
            return False
        return True


class IsAdmin(IsAuthenticated):
    """Class that control access to application for admins."""

    authenticator = ADMIN

    async def has_permission(  # pylint: disable=invalid-overridden-method
        self, source: typing.Any, info: Info, **kwargs: str
    ) -> bool:
        """Check whether user is admin."""
        logger.info("IsAdmin")

        return (
            await super().has_permission(source, info, **kwargs)
            and info.context.user.admin is True
        )


class IsProvider(IsAuthenticated):
    """Class that control access to application for providers."""

    authenticator = PROVIDER


class IsCibaUserOrIsAuthenticated(BasePermission):
    """Class that control access to application for 1.0 users and 2.0."""

    message = IsAuthenticated.message

    async def has_permission(  # pylint: disable=invalid-overridden-method
        self, source: typing.Any, info: Info, **kwargs: str
    ) -> bool:
        """Check whether user is admin or participant."""

        logger.info("IsCibaUserOrIsAuthenticated")
        return await IsAuthenticated().has_permission(source, info, **kwargs)


class IsSoleraUser(BasePermission):
    message = "No such user"
    authenticator = SOLERA

    async def has_permission(  # pylint: disable=invalid-overridden-method, unused-argument
        self, source: typing.Any, info: Info, **kwargs: str
    ) -> bool:
        """Check whether a request was made by logged-in user."""
        logger.info("IsSoleraUser")
        request: typing.Union[Request, WebSocket] = info.context.request
        x_auth_source = request.headers.get(
            "X-Auth-Source", self.authenticator
        )
        authenticator = get_authenticator(x_auth_source)
        if "Authorization" in request.headers:
            sub = None
            try:
                info.context.user = await authenticator.authenticate(
                    request.headers["Authorization"]
                )

                if isinstance(info.context.user, Participant):
                    sub = info.context.user.id
                else:
                    sub = getattr(info.context.user, "id", "")
                    logger.debug("Sub is: %s", sub)
            except InvalidToken as ex:
                logger.warning(ex)
            except PermissionDenied:
                self.message = "Permission denied"
            except Exception as ex:  # pylint: disable=broad-except
                logger.exception(ex)
            return sub is not None

        return False
