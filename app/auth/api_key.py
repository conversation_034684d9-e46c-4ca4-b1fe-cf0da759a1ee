from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from app.settings import get_settings

settings = get_settings()


API_KEY_NAME = "x-api-key"


class APIKeyMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # pylint: disable=line-too-long
        """
        Process the incoming request by checking the API key in the request headers and
        determining whether it matches the allowed API key for participants. If the key matches,
        the request is passed to the next middleware or handler. In either case, the response is returned.

        Args:
            request (Request): The incoming HTTP request object, which contains the headers and other details.
            call_next (Callable): A callable that represents the next middleware or request handler in the chain.

        Returns:
            Response: The HTTP response returned by the next middleware or handler.
        """
        api_key = request.headers.get(API_KEY_NAME)
        if api_key and api_key == settings.PARTICIPANT_ALLOWED_API_KEY:
            response = await call_next(request)
            return response

        response = await call_next(request)
        return response
