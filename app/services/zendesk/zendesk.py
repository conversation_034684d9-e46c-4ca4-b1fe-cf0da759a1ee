import jwt
import time
from hashlib import sha512
from app.settings import get_settings
import secrets
from ciba_participant.participant.models import Participant

settings = get_settings()


def generate_zendesk_jwt_token(participant: Participant) -> str:
    """
    Generate a JWT token for Zendesk authentication.

    Args:
        participant_id (UUID): The participant's ID.
        email (str): The participant's email address.
        full_name (str): The participant's full name.

    Returns:
        str: The generated JWT token.
    """
    now = int(time.time())
    random_value = secrets.token_bytes(16)
    jti = sha512(f"{now}{random_value}".encode()).hexdigest()

    full_name = f"{participant.first_name} {participant.last_name}"
    payload = {
        "jti": jti,
        "iat": now,
        "external_id": str(participant.id),
        "email": participant.email,
        "name": full_name,
        "email_verified": True,
        "scope": "user",
    }

    kid = settings.ZENDESK_KEY_ID
    secret = settings.ZENDESK_SIGN_IN_SECRET

    return jwt.encode(payload, secret, algorithm="HS256", headers={"kid": kid})
