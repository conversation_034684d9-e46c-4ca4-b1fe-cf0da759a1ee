import boto3
import httpx
from botocore.exceptions import (
    ClientError,
    NoCredentialsError,
    PartialCredentialsError,
)
from redis import asyncio as aioredis
from tortoise import Tortoise

from app.integrations.cognito import get_cognito_client
from app.integrations.s3 import get_s3_client
from app.log.logging import logger
from app.settings import Settings


async def check_postgres(settings: Settings) -> bool:
    logger.debug(
        "Connecting to postgres: %s:%s",
        settings.POSTGRES_HOST,
        settings.POSTGRES_PORT,
    )
    try:
        conn = Tortoise.get_connection("default")
        await conn.execute_query("SELECT 1;")
        return True
    except Exception:  # pylint: disable=broad-except
        logger.exception("Postgres check failed")
        return False


async def check_redis_connection(settings: Settings) -> bool:
    try:
        # Create a Redis connection
        redis = aioredis.from_url(
            f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}",
            password=settings.REDIS_PASSWORD,
            encoding="utf-8",
            decode_responses=True,
        )

        # Send a PING command to check the connection
        response = await redis.ping()

        # Close the connection
        await redis.close()
        await redis.connection_pool.disconnect()

        # Check the response from the PING command

        return response
    except Exception:  # pylint: disable=broad-except
        logger.exception("Failed to connect to Redis")
        return False


async def check_http_service(host: str) -> bool:
    if host:
        logger.debug("Connecting to http service: %s", host)
    else:
        logger.error("Got empty: %s", host)
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{host}/health")
            return response.status_code == 200
        except Exception:  # pylint: disable=broad-except
            logger.exception("HTTP service check for %s failed", host)
            return False


async def check_cognito_connection(settings: Settings) -> bool:
    try:
        client = get_cognito_client()
        response = client.describe_user_pool(
            UserPoolId=settings.COGNITO_USER_POOL_ID
        )
        logger.debug(
            "Successfully connected to Cognito User Pool. ID: %s. Name: %s",
            response["UserPool"]["Id"],
            response["UserPool"]["Name"],
        )
        return True
    except NoCredentialsError:
        logger.error("cognito: Error: AWS credentials not found.")
    except PartialCredentialsError:
        logger.error("cognito: Error: Incomplete AWS credentials.")
    except ClientError as e:
        if e.response["Error"]["Code"] == "ResourceNotFoundException":
            logger.error(
                "cognito: Error: "
                "The user pool with the specified ID was not found."
            )
        else:
            logger.error(
                "cognito: An AWS Client error occurred: %s ",
                e.response["Error"]["Message"],
            )
    except Exception:  # pylint: disable=broad-except
        logger.exception("Failed to connect to Cognito")

    return False


async def verify_s3_connection(settings: Settings) -> bool:
    """Verifies the S3 connection by listing all buckets."""
    try:
        s3_client = get_s3_client()
        s3_client.head_bucket(Bucket=settings.AWS_BUCKET_NAME)
        logger.info("Successfully connected to S3.")

        return True
    except NoCredentialsError:
        logger.error("s3bucket: Error: AWS credentials not found.")
        return False
    except ClientError as e:
        logger.error(
            "s3bucket: An AWS Client error occurred: %s",
            e.response["Error"]["Message"],
        )
        return False
    except Exception:  # pylint: disable=broad-except
        logger.exception("Failed to connect to S3")
        return False


def get_sqs_client(settings: Settings) -> boto3.client:
    """Initialize and return an SQS client."""
    return boto3.client("sqs", region_name=settings.AWS_REGION)


async def verify_sqs_connection(settings: Settings) -> bool:
    """Verifies the SQS connection by listing all queues."""
    try:
        sqs_client = get_sqs_client(settings)
        response = sqs_client.list_queues()
        logger.debug(
            "Successfully connected to SQS. Queues available: %s ",
            any(response.get("QueueUrls", [])),
        )
        return True
    except NoCredentialsError:
        logger.error("sqs: Error: AWS credentials not found.")
        return False
    except ClientError as e:
        logger.error(
            "sqs: An AWS Client error occurred: %s",
            e.response["Error"]["Message"],
        )
        return False
    except Exception:  # pylint: disable=broad-except
        logger.exception("Failed to connect to SQS")
        return False
