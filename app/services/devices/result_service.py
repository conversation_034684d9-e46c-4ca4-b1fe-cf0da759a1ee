from ciba_participant.participant.user_service import Devices, UserService
from ciba_participant.program.models import ProgramModuleSection

from app.graphql_api.program.inputs import PersonalSuccessInput
from app.graphql_api.program.services import ProgressService
from app.services.devices.data_preparer import DataPreparer
from app.services.devices.handler import RPM<PERSON><PERSON><PERSON><PERSON>andler


async def _loop_handler(data_list: list, device: Devices) -> None:
    for i in data_list:
        if i["value"] is not None:
            program_module_section = (
                await ProgramModuleSection.filter(
                    id=i["pms_id"], program_module_id=i["program_module_id"]
                )
                .prefetch_related(
                    "program_module", "program_module__program_course"
                )
                .get()
            )
            data = PersonalSuccessInput(value=i["value"])
            await ProgressService.make_progress(
                program_module_section,
                i["participant_id"],
                data,
            )
            await UserService().update_device_issetup(
                i["participant_id"], device
            )


class DataRequestHandler:
    async def _merge_list_dicts(self, dict1: list, dict2: list) -> list:
        """Merge two lists of dicts"""
        email_to_dict2 = {d["email"]: d for d in dict2}
        # Merge dictionaries
        merged_list = [
            {**d1, **email_to_dict2.get(d1["email"], {})} for d1 in dict1
        ]
        return merged_list
        # dict2 = {d["email"]: d for d in dict2}
        # # Merge dictionaries
        # merged_list = [{**d1, **dict2.get(d1["email"], {})} for d1 in dict1]
        # return merged_list

    async def get_result(self) -> None:
        weight_list, activity_list = await DataPreparer().prepare_data()
        endpoint = "user_data/daily_weight"
        prepared_data = {
            "emails": weight_list,
        }
        weight_get_list = await RPMRequestHandler(endpoint).post_data(
            prepared_data
        )
        if isinstance(weight_get_list, list):
            weight = await self._merge_list_dicts(weight_get_list, weight_list)
            await _loop_handler(weight, Devices.SCALE)

        endpoint = "user_data/daily_activity"
        prepared_data = {
            "emails": activity_list,
        }
        activity_get_list = await RPMRequestHandler(endpoint).post_data(
            prepared_data
        )
        if isinstance(activity_get_list, list):
            activity = await self._merge_list_dicts(
                activity_get_list, activity_list
            )
            await _loop_handler(activity, Devices.WATCH)
