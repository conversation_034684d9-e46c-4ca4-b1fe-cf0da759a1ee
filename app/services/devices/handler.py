from datetime import datetime
from typing import Any, Optional

from ciba_participant.participant.models import Participant
from ciba_participant.activity.models import ParticipantActivityDevice
from app.integrations.rpm import rpm_request
from app.log.logging import logger


PARTICIPANT = "participant"


class RPMRequestHandler:
    def __init__(self, endpoint: str = None):
        self.endpoint = endpoint

    async def get_data(
        self,
        prepared_data: Optional[dict],
        correlation_id: Optional[str] = None,
    ) -> dict:
        response = await rpm_request(
            self.endpoint,
            get_data=prepared_data,
            correlation_id=correlation_id,
        )
        return response

    async def post_data(
        self,
        prepared_data: Optional[dict],
        correlation_id: Optional[str] = None,
    ) -> dict:
        response = await rpm_request(
            self.endpoint,
            post_data=prepared_data,
            correlation_id=correlation_id,
        )
        return response

    async def get_device_auth(
        self,
        participant: Participant,
        type_device: ParticipantActivityDevice,
        mail: str,
        sync_start_date: int,
        site: str | None = None,
        correlation_id: Optional[str] = None,
    ) -> dict:
        """Call get_code endpoint RPM API return auth url or token"""
        # pylint: disable=line-too-long
        self.endpoint = "devices/get_code?mail={mail}&type_device={type_device}&member_type={member_type}&member_id={member_id}"
        prepared_data = {
            "type_device": type_device.value,
            "mail": mail,
            "member_type": PARTICIPANT,
            "member_id": participant.id,
            "sync_start_date": sync_start_date,
        }

        if site is not None:
            self.endpoint += "&site={site}"
            prepared_data.update({"site": site})

        logger.debug(
            f"[Correlation ID: {correlation_id}] devices: get_code: email %s | endpoint: %s",
            mail,
            self.endpoint,
        )
        return await self.get_data(
            prepared_data, correlation_id=correlation_id
        )

    async def disconnect(
        self,
        type_device: str,
        member_type: str,
        member_id: str,
        correlation_id: Optional[str] = None,
    ) -> dict:
        """ "Call disconnect endpoint RPM API"""
        # pylint: disable=line-too-long
        self.endpoint = "devices/disconnect?type_device={type_device}&member_type={member_type}&member_id={member_id}"

        prepared_data = {
            "type_device": type_device,
            "member_type": member_type,
            "member_id": member_id,
        }

        return await self.post_data(
            prepared_data, correlation_id=correlation_id
        )

    async def sync(
        self,
        *,
        type_device: str,
        member_type: str,
        member_id: str,
        start_date: datetime | None = None,
        correlation_id: Optional[str] = None,
    ) -> dict:
        """ "Call sync endpoint RPM API"""
        # pylint: disable=line-too-long
        self.endpoint = "devices/sync?type_device={type_device}&member_type={member_type}&member_id={member_id}"

        prepared_data: dict[str, Any] = {
            "type_device": type_device,
            "member_type": member_type,
            "member_id": member_id,
        }

        if start_date is not None:
            self.endpoint += "&start_date={start_date}"
            start_date_in_epoch = int(start_date.timestamp())

            prepared_data["start_date"] = start_date_in_epoch

        return await self.post_data(
            prepared_data, correlation_id=correlation_id
        )
