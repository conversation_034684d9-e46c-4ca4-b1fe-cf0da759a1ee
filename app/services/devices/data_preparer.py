from typing import Any, List, Tuple

from tortoise.transactions import in_transaction


def _create_cte() -> str:
    return """
    WITH RECURSIVE rpm(email, type) AS (
    SELECT participant.email,
        pms.type,
        pms.id::VARCHAR AS pms_id,
        participant.id::VARCHAR AS participant_id,
        pm.id::VARCHAR AS program_module_id
    FROM participant
    LEFT JOIN program_group_members pgm ON participant.id
    =pgm.participant_id
    LEFT JOIN program_module pm ON pm.program_group_id
    =pgm.program_group_id
    LEFT JOIN program_module_section pms ON pms.program_module_id
    =pm.id
    LEFT JOIN program_module_section_progress pmsp ON pms.id
    =pmsp.program_module_section_id
    WHERE pmsp.program_module_section_id IS NULL
    )"""


class DataPreparer:
    async def prepare_data(self) -> Tuple[List[Any], List[Any]]:
        type_list = await self._get_full_data()
        return type_list

    async def _query_with_type(self, query_type: str) -> list:
        """Get data from CTE with type"""
        cte = _create_cte()
        query = (
            f"{cte} SELECT email,pms_id,participant_id,program_module_id"
            f" FROM rpm WHERE rpm.type = "
            f"'{query_type}';"
        )

        async with in_transaction() as connection:
            result = await connection.execute_query_dict(query)
            return result

    async def _get_full_data(self) -> Tuple[List[Any], List[Any]]:
        """Get all data from CTE Here can add other queries"""
        weight_type_results = await self._query_with_type("weight_type")
        activity_type_results = await self._query_with_type("activity_type")
        return weight_type_results, activity_type_results
