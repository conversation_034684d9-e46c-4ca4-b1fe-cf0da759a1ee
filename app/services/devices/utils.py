from http import HTTPStatus

from ciba_participant.participant.models import Participant
from fastapi import HTTPException


async def get_participant_from_email(email: str) -> Participant:
    """
    Method to get a participant from a provided email address.
    :param email: participant email address
    :return: requested participant
    """
    # Rest GET does not escape plus symbol on params
    parsed_email = email.strip().replace(" ", "+")

    participant = await Participant.get_or_none(email=parsed_email)

    if participant is None:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"User with email {parsed_email} not found",
        )

    return participant
