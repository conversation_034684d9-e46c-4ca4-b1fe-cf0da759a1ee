from typing import List, Optional, Union

from pydantic import BaseModel, Field


class BaseJsonLogSchema(BaseModel):
    """Main log in JSON format."""

    thread: Union[int, str]
    level: str
    message: str
    source_log: str
    filename: str
    lineno: str
    timestamp: str = Field(..., alias="timestamp")
    app_name: str
    app_version: str
    app_env: str
    duration: int
    exceptions: Optional[Union[List[str], str]] = None
    user_id: Optional[str] = None
    trace_id: Optional[str] = None
    span_id: Optional[str] = None
    parent_id: Optional[str] = None
    correlation_id: Optional[str] = None

    class Config:
        populate_by_name = True


class RequestJsonLogSchema(BaseModel):
    """Schema for request/response answer."""

    request_uri: str
    request_referer: str
    request_method: str
    request_path: str
    request_host: str
    request_size: int
    request_content_type: str
    response_status_code: int
    duration: int
