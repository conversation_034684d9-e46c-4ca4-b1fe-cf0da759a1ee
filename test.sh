#!/bin/bash

RED='\033[0;31m'
GREEN='\033[0;32m'

while getopts "i" f
do
  case "$f" in
    i) echo -e "${GREEN}Installing test dependencies."; uv sync --frozen;;
    *) echo "usage: $0 [-i]" >&2
       exit 1 ;;
  esac
done

exit_status=0

function mytest {
    "$@"
    local cmd_status=$?
    if [ $cmd_status -eq 0 ]; then
        echo -e "${GREEN}$* succeed!"
    else
        echo -e "${RED}$* failed!"
        exit_status=1
    fi
    return $cmd_status
}

echo -e "${GREEN}Running unit tests."
export DD_REMOTE_CONFIGURATION_ENABLED=false
export ENV=test
uv run pytest --cov=./app tests/unit/ --junitxml=pytest.xml
#mytest poetry run pytest -p no:logging --ddtrace
#exit $exit_status
