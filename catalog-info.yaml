apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: participant-api
  description: |
    Participant API from Participant Ecosystem
  tags:
    - participant
    - python
  links:
    - title: Code Repository
      url: https://github.com/Cibahealth/participant-api
    - title: PRD Url - Health
      url: https://participant-api.cibahealth.com/health
    - title: DEV Url - Health
      url: https://participant-api.dev.cibahealth.com/health
  annotations:
    github.com/project-slug: cibahealth/participant-api
    backstage.io/techdocs-ref: dir:.
    sonarqube.org/project-key: cibahealth/participant-api
    backstage.io/code-coverage: scm-only
    github.com/team-slug: cibahealth/platform-team
    datadoghq.com/site: datadoghq.com
spec:
  type: service
  owner: cibahealth/ciba-team
  system: participant
  lifecycle: production
