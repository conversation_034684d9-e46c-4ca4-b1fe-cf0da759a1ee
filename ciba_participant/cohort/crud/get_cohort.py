from enum import StrEnum, auto
from typing import Optional
from uuid import UUID

from ciba_participant.cohort.models import (
    <PERSON>hor<PERSON>,
    FullCohort,
    RawCohortProgramModules,
)
from ciba_participant.participant.models import RawAuthorized, RawParticipant
from ciba_participant.program.models import RawProgram


class Include(StrEnum):
    program = auto()
    participants = auto()
    created_by = auto()
    program_modules = auto()


async def process(
    cohort_id: UUID, *, include: Optional[set[Include]] = None
) -> Optional["FullCohort"]:
    query = Cohort.filter(id=cohort_id)

    if include:
        query = query.prefetch_related(*include)

    cohort = await query.first()

    if cohort is None:
        return None

    program_output: Optional["RawProgram"] = None

    if include is None:
        include = set()

    if Include.program in include:
        program_output = RawProgram.model_validate(cohort.program)

    participants_output: Optional[list[RawParticipant]] = None

    if Include.participants in include:
        # Use the active_participants property to get only active participants
        active_participants = await cohort.active_participants

        # Convert to RawParticipant objects
        participants_output = [
            RawParticipant.model_validate(participant)
            for participant in active_participants
        ]

    created_by_output: Optional["RawAuthorized"] = None

    if Include.created_by in include:
        created_by_output = RawAuthorized.model_validate(cohort.created_by)

    program_modules_output: Optional[list["RawCohortProgramModules"]] = None

    if Include.program_modules in include:
        program_modules_output = [
            RawCohortProgramModules.model_validate(pm) for pm in cohort.program_modules
        ]

    output = FullCohort(
        id=cohort.id,
        created_at=cohort.created_at,
        updated_at=cohort.updated_at,
        name=cohort.name,
        started_at=cohort.started_at,
        limit=cohort.limit,
        status=cohort.status,
        end_date=await cohort.end_date,
        program_id=cohort.program_id,
        created_by_id=cohort.created_by_id,
        program=program_output,
        participants=participants_output,
        created_by=created_by_output,
        program_modules=program_modules_output,
    )

    return output
