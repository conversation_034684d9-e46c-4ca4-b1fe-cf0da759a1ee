from typing import List, Optional
from uuid import UUID

from tortoise.query_utils import Prefetch
from tortoise.transactions import in_transaction
from tortoise.exceptions import DoesNotExist

from ciba_participant import get_settings
from ciba_participant.chat_api.chat_api import delete_conversation
from ciba_participant.cohort.crud import (
    create_cohort,
    get_cohort,
    get_cohort_created_by,
    get_cohort_participants,
    get_cohort_program,
    get_paginated_cohorts,
    update_cohort,
    end_cohort,
)
from ciba_participant.cohort.models import (
    Cohort,
    CohortMembers,
    CohortProgramModules,
    CohortMembershipStatus,
)
from ciba_participant.cohort.pydantic_models import (
    CohortMemberOutput,
    CohortMembersOutput,
    CohortOutput,
)
from ciba_participant.log.logging import logger

settings = get_settings()


class CohortRepository:
    @staticmethod
    async def get_cohorts_count():
        return await Cohort.all().count()

    @staticmethod
    async def get_all_cohort_ids(page: int, per_page: int) -> list[UUID]:
        offset = (page - 1) * per_page
        cohorts = (
            await Cohort.all()
            .only("id")
            .offset(offset)
            .limit(per_page)
            .order_by("-started_at", "-created_at")
        )

        return [cohort.id for cohort in cohorts]

    @staticmethod
    async def get_cohorts_by_ids(cohort_ids: List[UUID]) -> List[CohortOutput]:
        cohorts = (
            await Cohort.filter(id__in=cohort_ids)
            .prefetch_related("program", "members", "program_modules")
            .order_by("-started_at", "-created_at")
        )
        return cohorts

    @staticmethod
    async def get_cohorts(page: int, per_page: int):
        offset = (page - 1) * per_page
        cohorts = (
            await Cohort.all()
            .prefetch_related(
                "program",
                Prefetch("members", queryset=CohortMembers.all()),
                Prefetch("program_modules", queryset=CohortProgramModules.all()),
            )
            .offset(offset)
            .limit(per_page)
            .order_by("-started_at", "-created_at")
        )
        return cohorts

    @staticmethod
    async def delete_cohort(cohort_id: UUID) -> None:
        async with in_transaction():
            cohort_obj = await Cohort.filter(id=cohort_id).first()

            if not cohort_obj:
                raise ValueError(f"Cohort with id {cohort_id} not found")

            await delete_conversation(unique_name=cohort_obj.unique_name)

            await cohort_obj.delete()

    @staticmethod
    async def get_cohort_program_modules(cohort_id: UUID) -> List[CohortProgramModules]:
        cohort_program_modules = await CohortProgramModules.filter(
            cohort_id=cohort_id
        ).all()
        return cohort_program_modules

    @staticmethod
    async def any_cohorts_by_program(program_id: UUID) -> bool:
        return await Cohort.filter(program_id=program_id).exists()

    # queries
    get_cohort = staticmethod(get_cohort.process)
    get_cohort_participants = staticmethod(get_cohort_participants.process)
    get_cohort_program = staticmethod(get_cohort_program.process)
    get_cohort_created_by = staticmethod(get_cohort_created_by.process)
    get_paginated_cohorts = staticmethod(get_paginated_cohorts.process)

    # commands
    create_cohort = staticmethod(create_cohort.process)
    update_cohort = staticmethod(update_cohort.process)
    end_cohort = staticmethod(end_cohort.process)


class CohortProgramModulesRepository:
    @staticmethod
    async def get_cohort_program_modules(cohort_id: UUID) -> List[CohortProgramModules]:
        cohort_program_modules = await CohortProgramModules.filter(
            cohort_id=cohort_id
        ).all()
        return cohort_program_modules

    @staticmethod
    async def get_cohort_program_module(
        program_module_id: UUID,
    ) -> Optional[CohortProgramModules]:
        cohort_program_module = await CohortProgramModules.filter(
            program_module_id=program_module_id
        ).first()
        return cohort_program_module


class CohortMembersRepository:
    @staticmethod
    async def create_cohort_member(
        cohort_id: UUID, participant_id: UUID
    ) -> CohortMemberOutput:
        """
        Create a cohort member
        """
        try:
            # Try to find a deleted membership to reactivate
            cohort_member = await CohortMembers.filter(
                cohort_id=cohort_id,
                participant_id=participant_id,
                status=CohortMembershipStatus.DELETED,
            ).get()

            # Reactivate the membership
            cohort_member.status = CohortMembershipStatus.ACTIVE
            await cohort_member.save()

            logger.info(f"Reactivated cohort member: {cohort_id}, {participant_id}")
            return await CohortMemberOutput.from_orm(cohort_member)

        except DoesNotExist:
            # Create a new membership if none exists
            async with in_transaction() as conn:
                cohort_member_obj = await CohortMembers.create(
                    cohort_id=cohort_id, participant_id=participant_id, connection=conn
                )
                logger.info(f"Created new cohort member: {cohort_id}, {participant_id}")
                return await CohortMemberOutput.from_orm(cohort_member_obj)

    @staticmethod
    async def get_cohort_member(
        participant_id: UUID,
    ) -> CohortMemberOutput | None:
        cohort_members = (
            await CohortMembers.filter(participant_id=participant_id)
            .prefetch_related("cohort__participants")
            .all()
        )

        # Filter active members from the retrieved list.
        active_members = [
            member
            for member in cohort_members
            if member.status == CohortMembershipStatus.ACTIVE
        ]

        # Choose the last active member if available; otherwise, choose the last member if any exist.
        if active_members:
            selected_member = active_members[-1]
        elif cohort_members:
            selected_member = cohort_members[-1]
        else:
            return None

        return await CohortMemberOutput.from_orm(selected_member)

    @staticmethod
    async def get_one_cohort_members(cohort_id: UUID) -> List[CohortMembers]:
        """Get all members from one cohort"""
        cohort_members = await (
            CohortMembers.filter(cohort_id=cohort_id).all()
            # .prefetch_related('cohort__participant')
        )

        members_list = []
        for member in cohort_members:
            members_list.append(await CohortMemberOutput.from_orm(member))

        return CohortMembersOutput(cohort_members=members_list)

    @staticmethod
    async def get_all_cohorts_members(page: int, per_page: int) -> List[CohortMembers]:
        cohort_members = (
            await CohortMembers.all().offset((page - 1) * per_page).limit(per_page)
        )

        members_list = []
        if cohort_members:
            for member in cohort_members:
                mmbr = await CohortMemberOutput.from_orm(member)
                members_list.append(mmbr)
        return CohortMembersOutput(cohort_members=members_list)

    @staticmethod
    async def update_cohort_member(
        participant_id: UUID, new_cohort_id: UUID
    ) -> CohortMemberOutput:
        await CohortMembers.filter(participant_id=participant_id).update(
            participant_id=participant_id, cohort_id=new_cohort_id
        )
        updated_cohort_member = await CohortMembers.get(
            participant_id=participant_id
        ).prefetch_related("cohort__participant")
        return CohortMemberOutput.from_orm(updated_cohort_member)

    @staticmethod
    async def delete_cohort_member(cohort_id: UUID, participant_id: UUID) -> bool:
        await CohortMembers.filter(
            cohort_id=cohort_id, participant_id=participant_id
        ).update(status=CohortMembershipStatus.DELETED)
        logger.info(f"Removed partcipant: {participant_id}, from cohort {cohort_id}")
        return True
