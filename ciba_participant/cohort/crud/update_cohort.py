from datetime import datetime
from typing import Optional
from uuid import UUID

from tortoise.transactions import in_transaction

from ciba_participant.cohort.models import Cohort


async def process(
    id: UUID,
    *,
    program_id: Optional[UUID] = None,
    start_date: Optional[datetime] = None,
    name: Optional[str] = None,
    limit: Optional[int] = None,
):
    async with in_transaction():
        if program_id is not None:
            raise NotImplementedError("Updating program_id is not implemented yet")

        if start_date is not None:
            raise NotImplementedError("Updating start_date is not implemented yet")

        cohort = await Cohort.get(id=id)

        if name is not None:
            cohort.name = name

        if limit is not None:
            participants_count = await cohort.participants.all().count()

            if limit < participants_count:
                raise ValueError("Limit cannot be less than the number of participants")

            cohort.limit = limit

        await cohort.save()
