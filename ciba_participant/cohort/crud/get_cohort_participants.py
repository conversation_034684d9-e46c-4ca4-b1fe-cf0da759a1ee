from uuid import UUID

from ciba_participant.participant.models import RawParticipant
from ciba_participant.cohort.models import Cohort


async def process(
    cohort_id: UUID,
) -> list[RawParticipant]:
    # Get the cohort
    cohort = await Cohort.get(id=cohort_id)

    # Use the active_participants property to get only active participants
    active_participants = await cohort.active_participants

    # Convert to RawParticipant objects
    participants_output = [
        RawParticipant.model_validate(p) for p in active_participants
    ]

    return participants_output
