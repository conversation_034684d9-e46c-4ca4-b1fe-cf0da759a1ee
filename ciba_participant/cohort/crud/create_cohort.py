from datetime import datetime, <PERSON><PERSON><PERSON>
from uuid import UUID

import tortoise
import tortoise.exceptions
import tortoise.transactions
from pydantic import BaseModel
from zoneinfo import ZoneInfo

from ciba_participant.chat_api.chat_api import (
    assign_participant_to_chat,
    create_group_chat,
)
from ciba_participant.cohort.models import (
    Cohort,
    CohortProgramModules,
)

from ciba_participant.log.logging import logger
from ciba_participant.participant.models import Authorized, role_to_participant_type
from ciba_participant.program.models import Program

from ciba_participant.settings import ENV, get_settings

settings = get_settings()


class Output(BaseModel):
    id: UUID
    name: str
    started_at: datetime
    limit: int
    program_id: UUID
    created_by: UUID


async def process(
    *,
    name: str,
    started_at: datetime,
    limit: int,
    program_id: UUID,
    created_by: UUID,
) -> Output:
    authorized = await Authorized.get_or_none(id=created_by)

    if not authorized:
        # TODO:  cognito "custom:isAdmin": "1" can create/delete programs/cohorts
        # is added by default to all cohorts as admin
        # TODO: if role != Admin  can create cohorts only

        raise ValueError("Cohort creation requires Health Coach permissions.")

    program = await Program.get_or_none(id=program_id)

    if not program:
        raise ValueError(f"Program with id {program_id} not found")

    program_modules = await program.modules

    if not program_modules:
        raise ValueError(f"No modules found for program {program.title}")

    started_at_utc = started_at.astimezone(
        ZoneInfo("UTC")
    ).replace(  # Start module on midnight pacific
        hour=8, minute=0, second=0, microsecond=0
    )

    async with tortoise.transactions.in_transaction():
        try:
            cohort = await Cohort.create(
                name=name,
                started_at=started_at_utc,
                limit=limit,
                program=program,
                created_by=authorized,
            )
        except tortoise.exceptions.IntegrityError:
            raise ValueError("Cohort with provided name already exists.")

        next_start_date = started_at_utc

        for module in program_modules:
            started_at = next_start_date
            ended_at = started_at + timedelta(days=module.length)
            next_start_date = ended_at

            await CohortProgramModules.create(
                cohort=cohort,
                program_module=module,
                started_at=started_at,
                ended_at=ended_at,
            )

        if settings.ENV not in [ENV.TEST, ENV.LOCAL]:
            group_unique_name = cohort.unique_name

            await create_group_chat(
                group_unique_name=group_unique_name, friendly_name=cohort.name
            )

            # Get the ChatRole based on the AuthorizedRole, defaulting to the role value if not found.
            participant_type = role_to_participant_type.get(
                authorized.role, authorized.role
            ).value
            await assign_participant_to_chat(
                chat_identity=authorized.chat_identity,
                group_unique_name=group_unique_name,
                participant_id=authorized.api_id,
                participant_type=participant_type,
            )

            await add_support_to_cohort_chat(group_unique_name)

        output = Output(
            id=cohort.id,
            name=cohort.name,
            started_at=cohort.started_at,
            limit=cohort.limit,
            program_id=program.id,
            created_by=authorized.id,
        )

        return output


async def add_support_to_cohort_chat(group_unique_name: str) -> None:
    """Add authorized supports to the cohort chat."""
    try:
        supports = await Authorized.filter(support_in_chat=True)
        logger.info(f"Adding {len(supports)} supports to chat {group_unique_name}")
        for support in supports:
            participant_type = role_to_participant_type.get(
                support.role, support.role
            ).value
            if support.api_id is not None:
                await assign_participant_to_chat(
                    chat_identity=support.chat_identity,
                    group_unique_name=group_unique_name,
                    participant_id=support.api_id,
                    participant_type=participant_type,
                )
            else:
                raise ValueError("Null Suppport api_id")
            logger.info(f"Added support {support.email} to chat {group_unique_name}")
        logger.info(f"Added {len(supports)} supports to chat {group_unique_name}")
    except Exception as e:
        logger.error(f"Error adding support to chat: {e})")
