from typing import Optional
from uuid import UUID

from ciba_participant.participant.models import Authorized, RawAuthorized


async def process(
    cohort_id: UUID,
) -> Optional[RawAuthorized]:
    authorized = await Authorized.filter(created_cohorts__id=cohort_id).first()

    if not authorized:
        return None

    authorized_output = RawAuthorized.model_validate(authorized)

    return authorized_output
