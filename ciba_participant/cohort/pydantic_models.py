import asyncio
from datetime import datetime
from typing import Any, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from ciba_participant.cohort.models import FullCohort
from ciba_participant.participant.pydantic_models import (
    ParticipantPydantic,
)
from ciba_participant.program.pydantic_models import ProgramOutput


# Base Pydantic models
class CohortBase(BaseModel):
    name: Optional[str] = None
    started_at: Optional[datetime] = None
    limit: Optional[int] = None
    program_id: UUID


class CohortMemberBase(BaseModel):
    cohort_id: UUID
    participant_id: UUID


# Output models with nested relationships
class CohortMemberOutput(CohortMemberBase):
    model_config = ConfigDict(from_attributes=True)

    created_at: datetime
    updated_at: datetime

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        # participant = await obj.participant
        # cohort = await obj.cohort.prefetch_related('program')
        # parsed_cohort = await CohortOutput.from_orm(cohort)
        # obj_dict['participant'] = participant
        # obj_dict['cohort'] = parsed_cohort
        return cls(**obj_dict)


class CohortOutput(CohortBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    participants: Optional[List[ParticipantPydantic]] = []
    live_sessions: Optional[List[Any]] = []
    is_editable: Optional[bool] = True
    hasParticipants: Optional[bool] = False
    program_name: Optional[str] = None
    created_by: Optional[str] = None
    program: Optional[ProgramOutput] = None

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__

        (
            program,
            members,
            # creator,
            cohort_program_modules,
        ) = await asyncio.gather(
            obj.program,
            obj.members.all().prefetch_related("participant"),
            # obj.created_by,
            obj.program_modules.all(),
        )

        # created_by = creator.full_name()
        cohort_program_modules_map = {
            m.program_module_id: m for m in await obj.program_modules
        }

        participants_list = [
            await ParticipantPydantic.from_orm(member.participant) for member in members
        ]

        merged_modules = []
        for module in await program.modules.all():
            module_dict = module.__dict__
            if module.id in cohort_program_modules_map:
                module_cohort_data = cohort_program_modules_map[module.id]
                module_dict["started_at"] = module_cohort_data.started_at
                module_dict["ended_at"] = module_cohort_data.ended_at

            merged_modules.append(module_dict)
        obj_dict.update(
            {
                "program": {**program.__dict__, "modules": merged_modules},
                "participants": participants_list,
                # 'is_editable': not participants_list,
                # 'program_name': program.title,
                # 'created_by': created_by,
                # 'hasParticipants': bool(participants_list),
            }
        )
        # TODO: Add live sessions but now program instead
        return cls(**obj_dict)


class CohortsOutput(BaseModel):
    cohorts: Optional[List[CohortOutput]] = []


class CohortMembersOutput(BaseModel):
    cohort_members: Optional[List[CohortMemberOutput]] = []


class CohortInfo(FullCohort):
    current_week: Optional[str] = None
    current_module: Optional[str] = None  # Current module number: e.g 'module 11'
    total_weeks: int
