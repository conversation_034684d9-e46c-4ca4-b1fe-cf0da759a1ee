from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, ConfigDict
from tortoise.fields import (
    CharEnumField,
    CharField,
    ForeignKeyField,
    ForeignKeyRelation,
    IntField,
    JSONField,
    ReverseRelation,
    TextField,
    UUIDField,
)
from tortoise.models import Model

from ciba_participant.activity.models import (
    ParticipantActivityCategory,
    ParticipantActivityEnum,
)
from ciba_participant.common.models import TimestampMixin

if TYPE_CHECKING:
    from ciba_participant.cohort.models import Cohort


class Program(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    title = CharField(max_length=255, unique=True)
    description = TextField(default="")

    modules: ReverseRelation["ProgramModule"]
    cohorts: ReverseRelation["Cohort"]

    class Meta:
        table = "program"


class RawProgram(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    title: str
    description: str
    created_at: datetime
    updated_at: datetime


class FullProgram(RawProgram):
    modules: Optional[list["FullProgramModule"]]
    cohorts: Optional[list["RawCohort"]]


class ProgramModule(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    title = CharField(max_length=255)
    short_title = CharField(max_length=255)
    length = IntField(description="Days duration of the module")  # days
    description = TextField()

    program_id: UUID
    program: ForeignKeyRelation["Program"] = ForeignKeyField(
        "models.Program", related_name="modules"
    )

    sections: ReverseRelation["ProgramModuleSection"]
    cohorts: ReverseRelation["Cohort"]

    order = IntField()  # New field to specify the order

    class Meta:
        table = "program_module"
        ordering = ["order"]  # Ensure modules are retrieved in the correct order


class RawProgramModule(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    title: str
    short_title: str
    length: int
    description: str
    program_id: UUID
    order: int


class FullProgramModule(RawProgramModule):
    sections: Optional[list["RawProgramModuleSection"]] = None
    program: Optional["RawProgram"] = None
    cohorts: Optional[list["RawCohort"]] = None


class ProgramModuleSection(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    title = CharField(max_length=255)
    description = TextField()
    metadata = JSONField()
    program_module_id: UUID
    program_module: ForeignKeyRelation["ProgramModule"] = ForeignKeyField(
        "models.ProgramModule", related_name="sections"
    )
    activity_type = CharEnumField(enum_type=ParticipantActivityEnum)
    activity_category = CharEnumField(enum_type=ParticipantActivityCategory)

    class Meta:
        table = "program_module_section"


class RawProgramModuleSection(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    title: str
    description: str
    metadata: dict
    program_module_id: UUID
    activity_type: ParticipantActivityEnum
    activity_category: ParticipantActivityCategory


class FullProgramModuleSection(RawProgramModuleSection):
    program_module: Optional["RawProgramModule"] = None


class MetaType(Enum):
    FILE = "file"
    TYPE_FORM = "type_form"
    INPUT = "input"
    USER_INPUT = "user_input"
    ZOOM = "zoom"
    VIDEO = "video"
    URL = "url"


from ciba_participant.cohort.models import RawCohort  # noqa

FullProgram.model_rebuild()
