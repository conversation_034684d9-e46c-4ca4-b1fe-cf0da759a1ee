from datetime import datetime
from enum import IntEnum, StrEnum
from typing import Generator

import holidays
from attr import dataclass
from dateutil import rrule
from loguru import logger

from ciba_participant.classes.models import RecurrenceEnum

us_holidays = holidays.US()  # type: ignore


class RRuleFrequency(IntEnum):
    DAILY = rrule.DAILY
    WEEKLY = rrule.WEEKLY
    MONTHLY = rrule.MONTHLY


class Frequency(StrEnum):
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"


@dataclass
class DateInfo:
    date: datetime
    has_conflict: bool


def is_holiday(date: datetime) -> bool:
    return date in us_holidays


def is_weekend(date: datetime) -> bool:
    return date.weekday() in [5, 6]


def has_conflict(date: datetime) -> bool:
    return is_holiday(date) or is_weekend(date)


def get_recurrent_dates(
    *, starting_from: datetime, frequency: Frequency, interval: int, count: int
) -> Generator[DateInfo, None, None]:
    rule = rrule.rrule(
        freq=RRuleFrequency[frequency],
        interval=interval,
        dtstart=starting_from,
        count=count,
    )

    logger.info(rule)

    dates_info = (DateInfo(date=date, has_conflict=has_conflict(date)) for date in rule)

    return dates_info


class RecurrenceToInterval(IntEnum):
    weekly = 1
    bi_weekly = 2
    tri_weekly = 3
    monthly = 4


def get_interval_and_frequency(
    recurrence: RecurrenceEnum,
) -> tuple[int, Frequency]:  # type: ignore
    interval = RecurrenceToInterval[recurrence.value].value
    frequency = Frequency.WEEKLY

    return interval, frequency
