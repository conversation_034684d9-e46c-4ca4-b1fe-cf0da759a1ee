from http.client import HTTPException

from httpx import AsyncClient

from ciba_participant.activity.models import ParticipantActivity
from ciba_participant.log.logging import logger
from ciba_participant.settings import get_settings

settings = get_settings()


async def delete_solera_activity(participant_id: str, activity_id: str) -> dict:
    """Delete activity from solera."""
    activity = await ParticipantActivity.get_or_none(id=activity_id)
    if activity is None:
        raise ValueError(f"Activity {activity_id} not found")

    activity_type = activity.activity_type.value

    async with AsyncClient(
        base_url=settings.MINTVAULT_API_URL,
        headers={"Authorization": f"Bearer {settings.MINTVAULT_API_KEY}"},
        timeout=60,
    ) as client:
        try:
            response = await client.delete(
                f"/api/v1/participants/{participant_id}/solera-activities/{activity_id}/{activity_type}/correct"
            )
            response.raise_for_status()

            return response.json()
        except Exception as e:
            logger.exception(f"Error deleting activity {activity_id}: {e}")
            raise HTTPException("Error deleting activity")
