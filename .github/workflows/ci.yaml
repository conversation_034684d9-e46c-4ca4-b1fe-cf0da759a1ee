#################################
######### Platform Team #########
#################################
name: Continuous Integration
on:
  pull_request:
    types: [opened, synchronize, reopened, labeled]

permissions:
  contents: read
  id-token: write
  pull-requests: write


jobs:
  CI:
    name: Continuous Integration
    uses: Cibahealth/platform-pipelines/.github/workflows/ci-application.yml@main
    secrets: inherit
    with:
      apply-fixes: true
      language: python
      upload-behavior-if-missed-artifact: error
      stop-on-failed-tests: true
      full-tests: true
