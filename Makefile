SHELL := /bin/bash
export IMAGE_PATH := participant-api-app

build:
	make clean
	DOCKER_BUILDKIT=1 docker build --ssh default -t participant-api:dev-latest --target dev --build-arg INSTALL_DEV=true .

lint-check:
	uv run ruff check
	uv run mypy .

lint:
	uv run ruff check --fix
	uv run mypy .

recreate:
	docker compose stop
	docker compose rm -f app
	docker rmi -f participant-api_app
	docker compose up -d


test:
	docker compose -f docker-compose.test.yaml run --rm app ./test.sh

unittest:
	export IMAGE_PATH=participant-api-app
	docker compose -f docker-compose.test.yaml run --rm app uv run pytest

recreate-with-db:
	docker compose stop
	docker rm -f ciba_participant_api
	docker rmi -f participant-api_app
	docker compose rm -f db
	docker volume rm -f participant-api_participant_db
	docker compose up -d

up:
	docker compose up -d

seeddb:
	docker exec -it ciba_participant_api python seed_local.py -dh db -p 5432

migration-gen:
	docker exec -it ciba_participant_api aerich init -t app.TORTOISE_ORM
	docker exec -it ciba_participant_api aerich init-db
	docker exec -it ciba_participant_api aerich migrate

migrate:
	docker exec -it ciba_participant_api aerich upgrade

revert:
	docker exec -it ciba_participant_api aerich downgrade

down:
	docker compose down

clean:
	docker compose down
	docker rmi -f participant-api_app
	docker volume rm -f participant-api_participant_db
	docker builder prune -a -f

term:
	docker run -it participant-api:dev-latest bash
