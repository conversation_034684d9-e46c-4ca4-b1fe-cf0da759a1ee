service_name: participant-api
metadata:
  team: participant
  source: python
type: microservice
internal: false
stage: dev
deploy:
  command:
    - /bin/sh
    - /app/entrypoint.sh
securityContext: null
image:
  pullPolicy: IfNotPresent
networking:
  backendPort: 8000
resources:
  requests:
    memory: "1280Mi"
    cpu: "768m"
  limits:
    memory: "2304Mi"
    cpu: "1256m"
environment:
  - name: "IS_NEW_ENV"
    value: "1"
ssm_params:
  - name: UI_HOST
    from: /participant/UI_HOST
  - name: SQS_URL
    from: /participant/SQS_URL
  - name: SQS_QUEUE
    from: /participant/SQS_QUEUE
  - name: SQS_REGION
    from: /participant/SQS_REGION
  - name: REDIS_PORT
    from: /participant/REDIS_PORT
  - name: REDIS_HOST
    from: /participant/REDIS_HOST
  - name: POSTGRES_USER
    from: /participant/POSTGRES_USER
  - name: POSTGRES_PORT
    from: /participant/POSTGRES_PORT
  - name: POSTGRES_PASSWORD
    from: /participant/POSTGRES_PASSWORD
  - name: POSTGRES_HOST
    from: /participant/POSTGRES_HOST
  - name: POSTGRES_DB
    from: /participant/POSTGRES_DB
  - name: ENV
    from: /participant/ENV
  - name: COGNITO_USER_POOL_ID
    from: /participant/COGNITO_USER_POOL_ID
  - name: COGNITO_AWS_REGION
    from: /participant/COGNITO_AWS_REGION
  - name: COGNITO_APP_CLIENT_ID
    from: /participant/COGNITO_APP_CLIENT_ID
  - name: PROVIDER_USER_POOL_ID
    from: /participant/PROVIDER_USER_POOL_ID
  - name: PROVIDER_APP_CLIENT_ID
    from: /participant/PROVIDER_APP_CLIENT_ID
  - name: CELERY_BROKER_URL
    from: /participant/CELERY_BROKER_URL
  - name: AWS_REGION
    from: /participant/AWS_REGION
  - name: ALLOW_ORIGINS
    from: /participant/ALLOW_ORIGINS
  - name: ALLOW_METHODS
    from: /participant/ALLOW_METHODS
  - name: ALLOW_HEADERS
    from: /participant/ALLOW_HEADERS
  - name: ADMIN_HOST
    from: /participant/ADMIN_HOST
  - name: SENDGRID_API_KEY
    from: /participant/SENDGRID_API_KEY
  - name: JWT_SECRET
    from: /participant/JWT_SECRET
  - name: ADMIN_UI_HOST
    from: /participant/ADMIN_UI_HOST
  - name: CIBA_API_HOST
    from: /participant/CIBA_API_HOST
  - name: CIBA_API_KEY
    from: /participant/CIBA_API_KEY
  - name: CHAT_API_HOST
    from: /participant/CHAT_API_HOST
  - name: CHAT_API_KEY
    from: /participant/CHAT_API_KEY
  - name: SENTRY_DSN
    from: /participant/SENTRY_DSN
  - name: SECRET_KEY
    from: /participant/manual/SECRET_KEY
  - name: COGNITO_KMS_KEY_ARN
    from: /participant/COGNITO_KMS_KEY_ARN
  - name: HEADS_UP_ENCRYPTION_KEY
    from: /participant/manual/HEADS_UP_ENCRYPTION_KEY
  - name: HEADS_UP_ORG_UUID
    from: /participant/manual/HEADS_UP_ORG_UUID
  - name: HEADS_UP_API_KEY
    from: /participant/manual/HEADS_UP_API_KEY
  - name: SENDGRID_SENDER_EMAIL
    from: /participant/manual/SENDGRID_SENDER_EMAIL
  - name: RPM_API_URL
    from: /participant/RPM_API_URL
  - name: RPM_API_KEY
    from: /participant/RPM_API_KEY
  - name: SOLERA_CLIENT_ID
    from: /participant/SOLERA_CLIENT_ID
  - name: SOLERA_CLIENT_SECRET
    from: /participant/SOLERA_CLIENT_SECRET
  - name: SOLERA_API_URL
    from: /participant/SOLERA_API_URL
  - name: SOLERA_AUTH_URL
    from: /participant/SOLERA_AUTH_URL
  - name: PARTICIPANT_EMAIL_SQS_URL
    from: /participant/PARTICIPANT_EMAIL_SQS_URL
  - name: AWS_BUCKET_NAME
    from: /participant/AWS_BUCKET_NAME
  - name: CONTENT_LIBRARY_BUCKET_NAME
    from: /participant/CONTENT_LIBRARY_BUCKET_NAME
  - name: CONTENT_LIBRARY_KEY_ID
    from: /content-library/cloudfront/key-group/id
  - name: CONTENT_LIBRARY_SIGN_KEY
    from: /content-library/cloudfront/key-group/private-key
  - name: CONTENT_LIBRARY_DISTRIBUTION
    from: /content-library/cloudfront/distribution
  - name: COGNITO_SERVER_CLIENT_ID
    from: /participant/COGNITO_SERVER_CLIENT_ID
  - name: COGNITO_SERVER_CLIENT_SECRET
    from: /participant/COGNITO_SERVER_CLIENT_SECRET
  - name: SCHEDULE_MANAGER_API_ENDPOINT
    from: /participant/SCHEDULE_MANAGER_API_ENDPOINT
  - name: PROVIDERS_COGNITO_APP_CLIENT_ID
    from: /participant/PROVIDERS_COGNITO_APP_CLIENT_ID
  - name: PROVIDERS_COGNITO_USER_POOL_ID
    from: /participant/PROVIDERS_COGNITO_USER_POOL_ID
  - name: ADMIN_SENTRY_DSN
    from: /participant/ADMIN_SENTRY_DSN
  - name: SLACK_SNS_TOPIC_ARN
    from: /participant/SLACK_SNS_TOPIC_ARN
  - name: MINTVAULT_API_URL
    from: /participant/MINTVAULT_API_URL
  - name: MINTVAULT_API_KEY
    from: /participant/MINTVAULT_API_KEY
